package com.imile.attendance.calendar;

import com.imile.attendance.calendar.dto.DayConfigDTO;
import com.imile.attendance.calendar.param.CalendarApiParam;
import com.imile.rpc.common.RpcResult;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description 考勤日历API
 */
public interface AttendanceCalendarApi {

    /**
     * 查询日历日期类型
     * 因一个国家下存在多份日历，取日历逻辑为：
     * 国家日历为该国家下绑定人数最多的日历,若存在两个日历人数相同的场景，则随机取一个日历
     */
    RpcResult<List<DayConfigDTO>> getCalendarByCondition(CalendarApiParam param);
}
