package com.imile.attendance.calendar.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Data
public class DayConfigDTO implements Serializable {

    private static final long serialVersionUID = 9191594472176840787L;
    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;

    /**
     * 日期类型：WEEKEND 休息日 ，HOLIDAY 节假日 , PRESENT 应出勤日
     */
    private String dayType;
}
