package com.imile.attendance.migration;

import com.imile.attendance.migration.dto.BatchUserMigrationVerifyResult;
import com.imile.attendance.migration.dto.UserMigrationVerifyResult;
import com.imile.attendance.migration.param.BatchUserMigrationVerifyParam;
import com.imile.attendance.migration.param.UserMigrationVerifyParam;
import com.imile.rpc.common.RpcResult;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> chen
 * @Date 2025/6/26
 * @Description 考勤迁移验证API
 */
public interface AttendanceMigrationApi {

    /**
     * 验证单个用户是否启用新考勤系统
     * 通过用户ID查询用户信息，并根据灰度配置判断是否启用新考勤系统
     * 验证优先级：用户编码白名单 > 部门白名单 > 国家白名单
     * 
     * @param param 用户验证参数，包含用户ID
     * @return 用户验证结果，包含用户ID和是否启用新考勤系统的标识
     */
    RpcResult<UserMigrationVerifyResult> verifyUserIsEnableNewAttendance(UserMigrationVerifyParam param);

    /**
     * 批量验证用户是否启用新考勤系统
     * 通过用户ID列表批量查询用户信息，并根据灰度配置判断每个用户是否启用新考勤系统
     * 验证优先级：用户编码白名单 > 部门白名单 > 国家白名单
     * 
     * @param param 批量用户验证参数，包含用户ID列表
     * @return 批量用户验证结果，包含每个用户ID对应的验证结果
     */
    RpcResult<BatchUserMigrationVerifyResult> verifyUsersIsEnableNewAttendance(BatchUserMigrationVerifyParam param);


    /**
     * 获取启用新考勤系统的国家列表(用于判断当前灰度的国家范围，不用来判断新老系统之间的切换)
     */
    RpcResult<List<String>> getEnableNewAttendanceCountry();

    /**
     * 获取备份操作员的用户编码列表
     */
    RpcResult<Set<String>> getBackupOperatorUserCodes();
}
