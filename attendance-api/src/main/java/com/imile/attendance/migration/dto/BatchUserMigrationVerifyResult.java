package com.imile.attendance.migration.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/26
 * @Description 批量用户迁移验证结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchUserMigrationVerifyResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户验证结果列表
     */
    private List<UserMigrationVerifyResult> userResults;

    /**
     * 验证总数
     */
    private Integer totalCount;

    /**
     * 启用新考勤系统的用户数量
     */
    private Integer enabledCount;

    /**
     * 未启用新考勤系统的用户数量
     */
    private Integer disabledCount;
}
