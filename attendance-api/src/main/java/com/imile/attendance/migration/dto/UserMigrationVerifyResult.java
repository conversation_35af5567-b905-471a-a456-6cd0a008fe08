package com.imile.attendance.migration.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> chen
 * @Date 2025/6/26
 * @Description 单用户迁移验证结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserMigrationVerifyResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 是否启用新考勤系统
     */
    private Boolean isEnabled;
}
