package com.imile.attendance.report;

import com.imile.attendance.report.param.AttendanceMonthReportParam;
import com.imile.rpc.common.RpcResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/6/28
 * @Description 考勤日月报外部依赖API
 */
public interface AttendanceReportApi {

    /**
     * 查询用户月报数据(薪资使用)
     */
    RpcResult<List<Map<String, String>>> selectAttendanceMonthReport(AttendanceMonthReportParam param);
}
