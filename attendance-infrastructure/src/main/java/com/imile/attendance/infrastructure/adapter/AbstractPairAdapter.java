package com.imile.attendance.infrastructure.adapter;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.exception.DataWriteException;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.shaded.com.google.common.base.Throwables;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 抽象转换适配器
 *
 * <AUTHOR> chen
 * @Date 2025/1/21
 * @Description
 */
@Slf4j
public abstract class AbstractPairAdapter<NEW, OLD> implements BaseAdapter {

    private final DataConverter<NEW, OLD> converter;
    private final Class<NEW> newType;
    private final Class<OLD> oldType;

    @Autowired
    private PlatformTransactionManager transactionManager;
    @Resource
    protected Executor bizTaskThreadPool;

    @Autowired
    public AbstractPairAdapter(List<DataConverter<NEW, OLD>> converters) {
        // 在构造函数中完成类型解析和converter选择
        TypeResolver typeResolver = new TypeResolver(getClass());
        this.newType = typeResolver.resolveType(0);
        this.oldType = typeResolver.resolveType(1);
        this.converter = selectConverter(converters);
    }

    private DataConverter<NEW, OLD> selectConverter(List<DataConverter<NEW, OLD>> converters) {
        return converters.stream()
                .filter(c -> c.getNewType().equals(newType) && c.getOldType().equals(oldType))
                .findFirst()
                .orElseThrow(() -> new IllegalStateException(
                        String.format("No suitable DataConverter found for NEW type: %s and OLD type: %s",
                                newType.getName(), oldType.getName())
                ));
    }


    protected void saveOrUpdateOneNewWrapper(NEW newData,
                                             Consumer<NEW> newConsumer,
                                             Consumer<OLD> oldConsumer) {
        saveOrUpdateOneNewWithTx(newData, newConsumer, oldConsumer, transactionManager);
    }


    private void saveOrUpdateOneNewWithTx(NEW newData,
                                          Consumer<NEW> newConsumer,
                                          Consumer<OLD> oldConsumer,
                                          PlatformTransactionManager transactionManager) {
        TransactionTemplate template = new TransactionTemplate(transactionManager);
        template.execute(status -> {
                    try {
                        newConsumer.accept(newData);
                        return null;
                    } catch (Exception e) {
                        status.setRollbackOnly();
                        log.error("new attendance saveOrUpdateOneNewWithTx fail, newData:{}", newData, e);
                        throw new DataWriteException("new attendance saveOrUpdateOneNewWithTx fail", e);
                    }
                }
        );

        if (shouldWriteOld()) {
            //异步写入老系统
            bizTaskThreadPool.execute(() -> {
                try {
                    DynamicDataSourceContextHolder.push(Constants.TableSchema.hrms);
                    OLD oldData = converter.convertFromNew(newData);
                    oldConsumer.accept(oldData);
                } catch (Exception e) {
                    log.error("old attendance saveOrUpdateOne fail, newData:{} ,exception:{}", newData, Throwables.getStackTraceAsString(e));
                } finally {
                    // 清理数据源上下文
                    DynamicDataSourceContextHolder.clear();
                }
            });
        }
    }

    protected void saveOrUpdateBatchNewWrapper(Collection<NEW> newDataList,
                                               Consumer<Collection<NEW>> newBatchConsumer,
                                               Consumer<Collection<OLD>> oldBatchConsumer) {
        saveOrUpdateBatchNewWrapperWithTx(newDataList, newBatchConsumer, oldBatchConsumer, transactionManager);
    }

    private void saveOrUpdateBatchNewWrapperWithTx(Collection<NEW> newDataList,
                                                   Consumer<Collection<NEW>> newBatchConsumer,
                                                   Consumer<Collection<OLD>> oldBatchConsumer,
                                                   PlatformTransactionManager transactionManager) {
        TransactionTemplate template = new TransactionTemplate(transactionManager);
        template.execute(status -> {
            try {
                newBatchConsumer.accept(newDataList);
                return null;
            } catch (Exception e) {
                status.setRollbackOnly();
                log.error("new attendance saveOrUpdateBatchNewWrapperWithTx fail, newDataList:{}",
                        JSONObject.toJSONString(newDataList), e);
                throw new DataWriteException("new attendance saveOrUpdateBatchNewWrapperWithTx fail", e);
            }
        });

        if (shouldWriteOld()) {
            //异步写入老系统
            bizTaskThreadPool.execute(() -> {
                try {
                    DynamicDataSourceContextHolder.push(Constants.TableSchema.hrms);
                    Collection<OLD> oldDataList = newDataList.stream()
                            .map(converter::convertFromNew)
                            .collect(Collectors.toList());
                    oldBatchConsumer.accept(oldDataList);
                } catch (Exception e) {
                    log.error("old attendance saveOrUpdateBatch fail, exception:{}", Throwables.getStackTraceAsString(e));
                } finally {
                    // 清理数据源上下文
                    DynamicDataSourceContextHolder.clear();
                }
            });
        }
    }


    protected void saveOrUpdateOneWrapper(NEW newData,
                                          Consumer<NEW> newConsumer,
                                          Consumer<NEW> oldConsumer) {
        saveOrUpdateWithTx(newData, newConsumer, oldConsumer, transactionManager);
    }

    private void saveOrUpdateWithTx(NEW newData,
                                    Consumer<NEW> newConsumer,
                                    Consumer<NEW> oldConsumer,
                                    PlatformTransactionManager transactionManager) {
        TransactionTemplate template = new TransactionTemplate(transactionManager);
        template.execute(status -> {
                    try {
                        newConsumer.accept(newData);
                        return null;
                    } catch (Exception e) {
                        status.setRollbackOnly();
                        log.error("new attendance saveOrUpdateWithTx fail, newData:{}", newData, e);
                        throw new DataWriteException("new attendance saveOrUpdateWithTx fail", e);
                    }
                }
        );

        if (shouldWriteOld()) {
            //异步写入老系统
            bizTaskThreadPool.execute(() -> {
                try {
                    DynamicDataSourceContextHolder.push(Constants.TableSchema.hrms);
                    oldConsumer.accept(newData);
                } catch (Exception e) {
                    log.error("old attendance saveOrUpdateWithTx fail, newData:{} ,exception:{}", newData, Throwables.getStackTraceAsString(e));
                } finally {
                    // 清理数据源上下文
                    DynamicDataSourceContextHolder.clear();
                }
            });
        }

    }

    protected void saveOrUpdateBatchWrapper(Collection<NEW> newDataList,
                                            Consumer<Collection<NEW>> newBatchConsumer,
                                            Consumer<Collection<NEW>> oldBatchConsumer) {
        saveOrUpdateBatchWrapperWithTx(newDataList, newBatchConsumer, oldBatchConsumer, transactionManager);
    }


    private void saveOrUpdateBatchWrapperWithTx(Collection<NEW> newDataList,
                                                Consumer<Collection<NEW>> newBatchConsumer,
                                                Consumer<Collection<NEW>> oldBatchConsumer,
                                                PlatformTransactionManager transactionManager) {
        TransactionTemplate template = new TransactionTemplate(transactionManager);
        template.execute(status -> {
            try {
                newBatchConsumer.accept(newDataList);
                return null;
            } catch (Exception e) {
                status.setRollbackOnly();
                log.error("new attendance saveOrUpdateBatchNewWrapperWithTx fail, newDataList:{}",
                        JSONObject.toJSONString(newDataList), e);
                throw new DataWriteException("new attendance saveOrUpdateBatchNewWrapperWithTx fail", e);
            }
        });

        if (shouldWriteOld()) {
            //异步写入老系统
            bizTaskThreadPool.execute(() -> {
                try {
                    DynamicDataSourceContextHolder.push(Constants.TableSchema.hrms);
                    oldBatchConsumer.accept(newDataList);
                } catch (Exception e) {
                    log.error("old attendance saveOrUpdateWithTx fail,exception:{}", Throwables.getStackTraceAsString(e));
                } finally {
                    // 清理数据源上下文
                    DynamicDataSourceContextHolder.clear();
                }
            });
        }
    }

}