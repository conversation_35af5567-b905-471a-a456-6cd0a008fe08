package com.imile.attendance.infrastructure.common;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.Collection;

/**
 * 拓展MybatisPlus，使其支持批量插入
 * 见 com.imile.attendance.mybatis.EasySqlInjector
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/11/21
 */
public interface AttendanceBaseMapper<T> extends BaseMapper<T> {
    /**
     * 批量更新插入 仅适用于mysql
     *
     * @param entityList 实体列表
     * @return 影响行数
     */
    Integer replaceIntoBatchSomeColumn(Collection<T> entityList);

    /**
     * 批量插入 仅适用于mysql
     * 解决MybatisPlus无法使用批量新增问题，但仅支持MYSQL（MybatisPlus中虽然使用savaBatch可以实现批量插入，但是使用for循环，效率比较低）
     *
     * @param entityList 实体列表
     * @return 影响行数
     */
    Integer insertBatchSomeColumn(Collection<T> entityList);
}
