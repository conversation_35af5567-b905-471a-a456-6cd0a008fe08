package com.imile.attendance.infrastructure.config;

import com.alicp.jetcache.CacheBuilder;
import com.alicp.jetcache.anno.CacheConsts;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import com.alicp.jetcache.anno.support.GlobalCacheConfig;
import com.alicp.jetcache.anno.support.JetCacheBaseBeans;
import com.alicp.jetcache.embedded.LinkedHashMapCacheBuilder;
import com.alicp.jetcache.redisson.RedissonCacheBuilder;
import com.alicp.jetcache.support.Fastjson2KeyConvertor;
import com.alicp.jetcache.support.Fastjson2ValueDecoder;
import com.alicp.jetcache.support.Fastjson2ValueEncoder;
import com.alicp.jetcache.support.JavaValueDecoder;
import com.alicp.jetcache.support.JavaValueEncoder;
import com.imile.attendance.util.RedisUtils;
import lombok.var;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> chen
 * @Date 2025/1/15 
 * @Description
 */
@Configuration
@EnableMethodCache(basePackages = "com.imile.attendance", proxyTargetClass = true)
@Import(JetCacheBaseBeans.class)
public class CacheConfig {

    private static final String JETCACHE_PREFIX = "jetCache:v1";
    private static final String JETCACHE_CHANEL_PREFIX = "jetCacheChannel:v1";

    @Bean
    public GlobalCacheConfig config(RedissonClient redissonClient) {
        Map<String, CacheBuilder> localBuilders = new HashMap<>();
        var localBuilder = LinkedHashMapCacheBuilder
                .createLinkedHashMapCacheBuilder()
                .keyConvertor(Fastjson2KeyConvertor.INSTANCE)
                .expireAfterWrite(1, TimeUnit.MINUTES);
        localBuilders.put(CacheConsts.DEFAULT_AREA, localBuilder);

        Map<String, CacheBuilder> remoteBuilders = new HashMap<>();
        var remoteCacheBuilder = RedissonCacheBuilder.createBuilder()
                .keyConvertor(Fastjson2KeyConvertor.INSTANCE)
                .valueEncoder(JavaValueEncoder.INSTANCE)
                .valueDecoder(JavaValueDecoder.INSTANCE)
                .broadcastChannel(RedisUtils.buildKey(JETCACHE_CHANEL_PREFIX))
                .keyPrefix(RedisUtils.buildKey(JETCACHE_PREFIX))
                .redissonClient(redissonClient)
                .expireAfterWrite(10, TimeUnit.MINUTES);
        remoteBuilders.put(CacheConsts.DEFAULT_AREA, remoteCacheBuilder);

        GlobalCacheConfig globalCacheConfig = new GlobalCacheConfig();
        globalCacheConfig.setLocalCacheBuilders(localBuilders);
        globalCacheConfig.setRemoteCacheBuilders(remoteBuilders);
        globalCacheConfig.setStatIntervalMinutes(15);
        return globalCacheConfig;
    }
}
