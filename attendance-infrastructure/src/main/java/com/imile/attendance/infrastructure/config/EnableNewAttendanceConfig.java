package com.imile.attendance.infrastructure.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> chen
 * @Date 2025/1/21
 * @Description
 */
@Component
@Getter
public class EnableNewAttendanceConfig {

    //==================考勤结果========================


    @Value("${newAttendance.abnormal.doubleWrite.isEnable:false}")
    private Boolean abnormalDoubleWriteEnabled;
}
