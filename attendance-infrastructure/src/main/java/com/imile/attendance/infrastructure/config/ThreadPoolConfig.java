package com.imile.attendance.infrastructure.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR> chen
 * @Date 2025/1/15 
 * @Description
 */
@Slf4j
@Configuration
public class ThreadPoolConfig {

    private static final String ATTENDANCE_TASK_THREAD_PREFIX = "ATTENDANCE-TASK-THREAD-";
    private static final String ATTENDANCE_LOG_THREAD_PREFIX = "ATTENDANCE-LOG-THREAD-";
    private static final String ATTENDANCE_CALCULATE_THREAD_PREFIX = "ATTENDANCE-CALCULATE-THREAD-";

    /**
     * 业务任务线程池
     */
    @Bean(name = "bizTaskThreadPool")
    public Executor bizTaskThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setKeepAliveSeconds(60);
        executor.setQueueCapacity(1);
        //不能处理的使用调用线程处理
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setThreadNamePrefix(ATTENDANCE_TASK_THREAD_PREFIX);
        executor.initialize();
        log.info("bizTaskThreadPool init corePoolSize={} maxPoolSize={} keepAliveSeconds={} queueCapacity={}",
                executor.getCorePoolSize(), executor.getMaxPoolSize(),
                executor.getKeepAliveSeconds(), executor.getMaxPoolSize());
        return executor;
    }

    /**
     * 考勤计算线程池
     */
    @Bean(name = "attendanceCalculateTaskThreadPool")
    public Executor attendanceCalculateTaskThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setKeepAliveSeconds(60);
        executor.setQueueCapacity(10);
        //不能处理的使用调用线程处理
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setThreadNamePrefix(ATTENDANCE_CALCULATE_THREAD_PREFIX);
        executor.initialize();
        log.info("attendanceCalculateTaskThreadPool init corePoolSize={} maxPoolSize={} keepAliveSeconds={} queueCapacity={}",
                executor.getCorePoolSize(), executor.getMaxPoolSize(),
                executor.getKeepAliveSeconds(), executor.getMaxPoolSize());
        return executor;
    }

    /**
     * 业务任务线程池
     */
    @Bean(name = "logTaskThreadPool")
    public Executor logTaskThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(4);
        executor.setKeepAliveSeconds(60);
        executor.setQueueCapacity(1);
        //不能处理的使用调用线程处理
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setThreadNamePrefix(ATTENDANCE_LOG_THREAD_PREFIX);
        executor.initialize();
        log.info("logTaskThreadPool init corePoolSize={} maxPoolSize={} keepAliveSeconds={} queueCapacity={}",
                executor.getCorePoolSize(), executor.getMaxPoolSize(),
                executor.getKeepAliveSeconds(), executor.getMaxPoolSize());
        return executor;
    }
}
