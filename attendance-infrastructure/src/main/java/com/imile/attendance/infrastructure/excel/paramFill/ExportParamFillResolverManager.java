package com.imile.attendance.infrastructure.excel.paramFill;

import com.imile.attendance.annon.ExportParamFill;
import org.springframework.beans.factory.ListableBeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2024/12/3 
 * @Description
 */
@Component
public class ExportParamFillResolverManager {

    private static ListableBeanFactory beanFactory;

    @Autowired
    public void setBeanFactory(ListableBeanFactory beanFactory) {
        ExportParamFillResolverManager.beanFactory = beanFactory;
    }


    public static Map<ExportParamFill.Source, ParameterFillResolver> getMap() {
        return beanFactory.getBeansOfType(ParameterFillResolver.class)
                .values()
                .stream()
                .collect(Collectors.toMap(ParameterFillResolver::support, Function.identity()));
    }

    public static ParameterFillResolver get(ExportParamFill.Source source) {
        return getMap().get(source);
    }
}
