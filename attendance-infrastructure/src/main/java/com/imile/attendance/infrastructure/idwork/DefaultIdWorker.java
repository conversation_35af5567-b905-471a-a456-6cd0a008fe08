package com.imile.attendance.infrastructure.idwork;

import java.io.Serializable;
import java.util.Date;
import java.util.UUID;
import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.NoPrefixEnum;
import com.imile.attendance.util.BizExceptionUtil;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.exception.BaseBusinessException;
import com.imile.common.exception.BusinessException;
import com.imile.framework.redis.client.ImileRedisClient;
import com.imile.idwork.IdWorkerUtil;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 默认ID生成器，todo 上线注意切换项目时生成的key不能重复
 * <AUTHOR> chen
 * @Date 2025/1/17 
 * @Description
 */
@Slf4j
@Component(value = "defaultIdWorker")
@RequiredArgsConstructor
public class DefaultIdWorker implements IdWorker {

    private final ImileRedisClient imileRedisClient;

    public static final String KEY = "ATTENDANCE:ID_WORKER:";
    public static final String CONFIG_NO_KEY = "CONFIG_NO";
    public static final String SEPARATOR = ":";
    public static final String LOCK_KEY = "HRMS:ID_WORKER:LOCK:";

    /**
     * 25个小时
     */
    public static final Long ATTENDANCE_CONFIG_EXPIRE_TIME = 25 * 60 * 60L;


    @Override
    public Long nextId() {
        return IdWorkerUtil.getId();
    }

    @Override
    public String nextNo(NoPrefixEnum noPrefixEnum) {
        //输出 ==> K20211217001
        Function<NoPrefixEnum, String> nextNoFunc = (idType) -> {
            long no = getAndIncrementNo(CONFIG_NO_KEY + SEPARATOR + idType.getPrefix());
            return noPrefixEnum.getPrefix() + no;
        };
        return next(nextNoFunc, noPrefixEnum, CONFIG_NO_KEY + SEPARATOR + noPrefixEnum.getPrefix());
    }

    @Override
    public String nextAttendanceConfigNo() {
        return nextNo(NoPrefixEnum.ATTENDANCE);
    }

    @Override
    public String nextPunchConfigNo() {
        return nextNo(NoPrefixEnum.PUNCH_CONFIG);
    }

    @Override
    public String nextReissueCardConfigNo() {
        return nextNo(NoPrefixEnum.REISSUE_CARD_CONFIG);
    }

    @Override
    public String nextOverTimeConfigNo() {
        return nextNo(NoPrefixEnum.OVERTIME_CONFIG);
    }

    @Override
    public String uuid() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    @Override
    public Long recordVersion(Serializable bizId) {
        //输出 ==> 20211217001
        Function<NoPrefixEnum, Long> nextNoFunc = (idType) -> {
            return getAndIncrementNo(CONFIG_NO_KEY + SEPARATOR + bizId);
        };
        return next(nextNoFunc, null, CONFIG_NO_KEY + SEPARATOR + bizId);
    }

    @Override
    public String nextPunchClassConfigNo() {
        return nextNo(NoPrefixEnum.PUNCH_CLASS_CONFIG);
    }

    /**
     * 获取并递增序列号
     *
     * @param keySuffix 缓存键的后缀，用于区分不同的序列号生成场景
     * @return 生成的唯一序列号，格式为 yyyyMMdd * 1000 + 当日递增序号
     */
    private long getAndIncrementNo(String keySuffix) {
        // 获取当前日期，格式为 yyyyMMdd
        String dayId = DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN);

        // 构建Redis缓存键，包含前缀、后缀和日期
        String cacheKey = KEY + keySuffix + SEPARATOR + dayId;

        // 从Redis获取当前值，如果不存在则从1开始
        Object value = imileRedisClient.get(cacheKey);
        long no = value == null ? 1 : Long.parseLong(String.valueOf(value)) + 1;

        // 将新值存入Redis，并设置过期时间
        imileRedisClient.set(cacheKey, no, ATTENDANCE_CONFIG_EXPIRE_TIME);

        // 返回格式化的序列号：日期 * 1000 + 当日递增序号
        return Long.parseLong(dayId) * 1000 + no;
    }

    private <R, T> R next(Function<T, R> nextNoFunc, T typeEnum, String key) {
        try {
            //锁住保证幂等操作,最多等待3秒
            boolean lockKeySuccess = imileRedisClient.tryLock(LOCK_KEY + key, 3, 10);
            //获取失败则抛出异常
            if (!lockKeySuccess) {
                throw BizExceptionUtil.ofI18n(ErrorCodeEnum.REPEAT_SUBMIT);
            }
            return nextNoFunc.apply(typeEnum);
        } catch (InterruptedException e) {
            log.error("interrupted error", e);
            Thread.currentThread().interrupt();
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } catch (BaseBusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("get max value fail", e);
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } finally {
            imileRedisClient.unlock(LOCK_KEY + key);
        }
    }
}
