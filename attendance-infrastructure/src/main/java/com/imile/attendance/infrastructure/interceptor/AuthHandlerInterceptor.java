package com.imile.attendance.infrastructure.interceptor;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.annon.NoAttendanceLoginAuthRequired;
import com.imile.attendance.annon.NoAuthRequired;
import com.imile.attendance.annon.NoLoginAuthRequired;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.context.UserContext;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.PermissionTypeEnum;
import com.imile.attendance.enums.SystemRoleEnum;
import com.imile.attendance.infrastructure.repository.common.UserPermissionService;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.permission.dto.DataPermissionDTO;
import com.imile.attendance.permission.dto.PermissionDTO;
import com.imile.common.exception.BusinessException;
import com.imile.common.result.Result;
import com.imile.ucenter.api.authenticate.AbstractUTokenInterceptor;
import com.imile.ucenter.api.authenticate.UcenterUtils;
import com.imile.ucenter.api.dto.user.UserInfoDTO;
import com.imile.util.lang.I18nUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/1/15
 * @Description
 */
@Slf4j
@Component
public class AuthHandlerInterceptor extends AbstractUTokenInterceptor {
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private UserPermissionService userPermissionService;

    /**
     * 复写这个方法可以实现检验失败之后要完成的操作 内置错误代码为 ERROR_CODE_NO_U_TOKEN ERROR_CODE_U_TOKEN_INVALID
     */
    @SneakyThrows
    @Override
    protected void afterVerifyFail(HttpServletRequest request, HttpServletResponse response, Object handler, String errorCode, String errorMessage) throws BusinessException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        response.getWriter().write(JSON.toJSONString(Result.getFailResult(errorCode, errorMessage)));
    }

    /**
     * 复写这个方法可以实现Rpc调用异常之后要完成的操作
     */
    @SneakyThrows
    @Override
    protected void afterRpcException(HttpServletRequest request, HttpServletResponse response, String errorCode, String errorMessage) throws BusinessException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        response.getWriter().write(JSON.toJSONString(Result.getFailResult(errorCode, "ucenter service error")));
    }

    /**
     * 获得uToken 使用方需要复写这个方法
     */
    @Override
    protected String getUToken(HttpServletRequest request) {
        return StringUtils.substringAfter(request.getHeader(AbstractUTokenInterceptor.AUTHORIZATION_HEADER), AbstractUTokenInterceptor.TOKEN_PREFIX);
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!super.preHandle(request, response, handler)) {
            return Boolean.FALSE;
        }
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        String uri = request.getRequestURI();
        if (null == uri) {
            return true;
        }
        UserInfoDTO userInfo = UcenterUtils.getUserInfo();
        // 不需要登录的界面，有可能会为空
        if (userInfo == null) {
            return Boolean.TRUE;
        }
        String userCode = userInfo.getUserCode();
        if (StringUtils.isEmpty(userCode)) {
            log.warn("用户编码为空！");
            afterVerifyFail(request, response, handler, ERROR_CODE_NO_U_TOKEN, I18nUtils.getMessage(ErrorCodeEnum.NO_LOGIN_AUTH_ERROR.getDesc()));
            return Boolean.FALSE;
        }
        UserInfoDO userInfoDO = userInfoDao.getByUserCode(userCode);
        if (userInfoDO == null) {
            if (Boolean.TRUE.equals(isNoAttendanceLoginAuthRequired(method))) {
                UserContext userContext = BeanUtil.copyProperties(UcenterUtils.getUserInfo(), UserContext.class);
                RequestInfoHolder.setLoginInfo(userContext);
                return Boolean.TRUE;
            } else {
                log.warn("用户{}不存在！", userCode);
                afterVerifyFail(request, response, handler, ERROR_CODE_NO_U_TOKEN, I18nUtils.getMessage(ErrorCodeEnum.NO_LOGIN_AUTH_ERROR.getDesc()));
                return Boolean.FALSE;
            }
        }
        // 设置用户信息
        UserContext userContext = BeanUtil.copyProperties(UcenterUtils.getUserInfo(), UserContext.class);
        userContext.setId(userInfoDO.getId());
        userContext.setUserCode(userCode);
        userContext.setUserNameEn(userInfoDO.getUserNameEn());
        RequestInfoHolder.setLoginInfo(userContext);
        bindingUserPermission();
        //普通角色允许登录
        if (Boolean.FALSE.equals(isNoLoginAuthRequired(method, userInfoDO))) {
            return Boolean.TRUE;
        }
        // 校验当前用户是否有companyID的权限
        if (Boolean.FALSE.equals(allowRequired(method))) {
            // 没有数据访问权限
            afterVerifyFail(request, response, handler, ErrorCodeEnum.NO_ACCESS_AUTH_ERROR.getCode(), I18nUtils.getMessage(ErrorCodeEnum.NO_ACCESS_AUTH_ERROR.getDesc()));
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 是否允许不登录hrms系统操作
     *
     * @param method
     * @return
     */
    private Boolean isNoAttendanceLoginAuthRequired(Method method) {
        return method.isAnnotationPresent(NoAttendanceLoginAuthRequired.class);
    }

    /**
     * 是否允许普通角色登录
     *
     * @param method
     * @return
     */
    private Boolean isNoLoginAuthRequired(Method method, UserInfoDO userInfoDO) {
        if (method.getAnnotation(NoLoginAuthRequired.class) == null) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private void bindingUserPermission() {
        UserContext userContext = RequestInfoHolder.getLoginInfo();
        // 用户权限
        PermissionDTO userPermission = userPermissionService.getUserPermission(userContext.getUserCode());
//        // 考勤子系统管理员权限
//        List<String> adminUserCodes = userPermissionService.getAdminUserCodesBySystem(BusinessConstant.SYSTEM_CODE);
//        if (CollectionUtils.isNotEmpty(adminUserCodes)
//                && adminUserCodes.contains(userContext.getUserCode())) {
//            userContext.setSystem(true);
//        }
        // 数据权限
        List<DataPermissionDTO> dataPermissionDTOList = userPermission.getDataPermissionDTOList();
        if (CollectionUtils.isNotEmpty(dataPermissionDTOList)) {
            // 数据权限Map
            Map<String, List<String>> typeCodeToPermissionMap = dataPermissionDTOList.stream().collect(Collectors.toMap(DataPermissionDTO::getTypeCode, DataPermissionDTO::getDataCodeList));
            // 保存数据权限 Map
            userContext.setPermissionMap(typeCodeToPermissionMap);
        }

//        // 系统管理员类型的角色，不设置 organizationIds 权限管理
//        if (userContext.isSystem()) {
//            return;
//        }

        // 非系统管理员设置权限
        Map<String, List<String>> permissionMap = userContext.getPermissionMap();

        if (MapUtils.isNotEmpty(permissionMap)) {
            // 获取部门数据权限
            List<String> deptIdList = permissionMap.get(PermissionTypeEnum.DEPT.getTypeCode());
            if (CollectionUtils.isNotEmpty(deptIdList)) {
                userContext.getOrganizationIds().addAll(deptIdList.stream().map(Long::valueOf).collect(Collectors.toList()));
            }

            // 获取员工常驻国数据权限
            List<String> countryList = permissionMap.get(PermissionTypeEnum.COUNTRY.getTypeCode());
            if (CollectionUtils.isNotEmpty(countryList)) {
                userContext.getCountryList().addAll(countryList);
            }
        }
    }

    /**
     * 是否允许访问
     *
     * @param method
     * @return
     */
    private Boolean allowRequired(Method method) {
        // 有@NoAuthRequired注解，则直接允许访问
        NoAuthRequired noAuthRequired = method.getAnnotation(NoAuthRequired.class);
        if (noAuthRequired != null) {
            return Boolean.TRUE;
        }
        UserContext userContext = RequestInfoHolder.getLoginInfo();
        if (userContext == null || userContext.getId() == null) {
            return Boolean.FALSE;
        }
        if (CollectionUtils.isEmpty(userContext.getCountryList())
                && CollectionUtils.isEmpty(userContext.getOrganizationIds())) {
            // 无数据权限
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
}
