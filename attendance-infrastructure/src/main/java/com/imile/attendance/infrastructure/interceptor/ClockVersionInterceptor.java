package com.imile.attendance.infrastructure.interceptor;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.common.exception.BusinessException;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * 考勤小程序判断版本是否落后拦截器
 *
 * <AUTHOR>
 * @since 2025/01/17
 */
@Slf4j
@Component
public class ClockVersionInterceptor implements HandlerInterceptor {

    private static final String VERSION_KEY = "Wechat-Version";


    private static final String CURRENT_VERSION = "1.0.3";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String uri = request.getRequestURI();
        if (null == uri) {
            return true;
        }
        if (uri.startsWith("/mobile/punch/detail")
                || uri.startsWith("/mobile/punch/punchIn")) {
            // 检查请求头中是否包含version字段
            String version = request.getHeader(VERSION_KEY);
            if (StringUtils.isEmpty(version)) {
                return true;
            }
            if (!Objects.equals(CURRENT_VERSION, version)) {
                response.setCharacterEncoding("UTF-8");
                response.setContentType("application/json; charset=utf-8");
                response.getWriter().write(JSON.toJSONString(BusinessException.get(ErrorCodeEnum.PUNCH_CARD_VERSION_ERROR.getCode(),
                        I18nUtils.getMessage(ErrorCodeEnum.PUNCH_CARD_VERSION_ERROR.getDesc()))));
                return false;
            }
        }
        return true;
    }
}
