package com.imile.attendance.infrastructure.listener;

import com.imile.attendance.enums.EnvEnum;
import com.imile.framework.redis.client.ImileRedisClient;
import com.imile.idwork.IdWorker;
import com.imile.idwork.IdWorkerUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationFailedEvent;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.web.context.WebServerInitializedEvent;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Random;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25
 * @Description idWork生产器注册监听器
 */
@Component
public class IdWorkRegisterListener implements ApplicationListener<ApplicationEvent> {

    private static final Logger logger = LoggerFactory.getLogger(IdWorkRegisterListener.class);

    @Resource
    private ImileRedisClient redisUtils;

    /**
     * 集群取模数
     */
    @Value("${digital.cluster.model:4}")
    private Integer clusterModel;
    /**
     * 当前环境
     */
    @Value(value = "${spring.profiles.active}")
    private String env;


    /**
     * 雪花id自增key
     */
    public static final String ATTENDANCE_IDWORK_WORK_ID = "ATTENDANCE:IDWORK:WORK:ID:";
    /**
     * redis 过期时间设置 (测试阶段设置为10秒，上线时候更新)
     */
    public static Long REDIS_EXPIRE_SECONDS_TIME = 900L;

    /**
     * 处理各类监听到的事件
     *
     * @param event 监听到的事件对象
     * <AUTHOR>
     * @date 2018年9月20日 下午8:47:02
     */
    @Override
    public void onApplicationEvent(ApplicationEvent event) {
        if (event instanceof WebServerInitializedEvent) {
            WebServerInitializedEvent webEvent = (WebServerInitializedEvent) event;
            int serverPort = webEvent.getWebServer().getPort();
            logger.info("==>当前应用节点端口号为:{}", serverPort);


        } else if (event instanceof ApplicationReadyEvent) {
            // 应用启动完成时开启集群相关配置
            try {
                register();
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        } else if (event instanceof ApplicationFailedEvent) {
            logger.info("==>当前应用节点启动失败!");
        } else if (event instanceof ContextClosedEvent) {
            // 应用停止时关闭集群相关配置
            logger.info("==>当前应用节点关闭集群相关配置!");
        }
    }

    /**
     * 注册idWork生产器
     */
    private void register() throws Exception {
        long datacenterId = getDataCenterId();
        long workerId = redisUtils.incrby(ATTENDANCE_IDWORK_WORK_ID, 1);
        workerId = workerId % 32;

        IdWorkerUtil.idWorker = new IdWorker(datacenterId, workerId);
        logger.info("idWork生产器注册成功,名称空间：attendance，中心编号：{} ,工作机编号:{},节点id:{}",
                IdWorkerUtil.idWorker.getDatacenterId(), IdWorkerUtil.idWorker.getWorkerId(), getIp());

        if (!EnvEnum.PROD.getCode().equalsIgnoreCase(env)) {
            REDIS_EXPIRE_SECONDS_TIME = 10L;
        }

    }

    /**
     * 获取数据中心ID
     *
     * @return 数据中心ID
     */
    private long getDataCenterId() throws NoSuchAlgorithmException {
        Random rd = SecureRandom.getInstanceStrong();
        // 新系统使用 DataCenterId [3, 5]，避免与老系统 [0, 2] 冲突(老系统使用[0, 2]  rd.nextInt(clusterModel - 1);)
        return (clusterModel - 1) + rd.nextInt(3);  // 范围 [3, 5]
    }

    /**
     * 获取本机IP地址
     *
     * @return 本机IP地址
     */
    private String getIp() {
        try {
            return InetAddress.getLocalHost().getHostAddress();
        } catch (Exception e) {
            logger.error("get ip error!", e);
        }
        return null;
    }
}
