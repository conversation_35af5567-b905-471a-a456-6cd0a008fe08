package com.imile.attendance.infrastructure.lock;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> chen
 * @Date 2025/1/15 
 * @Description
 */
@Component
@Slf4j
public class RedissonLockTemplate {

    private final RedissonClient redissonClient;

    public RedissonLockTemplate(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    public Scope newScope(String lockKey, Duration duration) {
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock(duration.toMillis(), TimeUnit.MILLISECONDS);
        return new Scope(lock);
    }

    public Scope newScope(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock();
        return new Scope(lock);
    }

    public Scope newScope(Set<String> keySet, Duration duration) {
        RLock lock = buildLocks(keySet);
        lock.lock(duration.toMillis(), TimeUnit.MILLISECONDS);
        return new Scope(lock);
    }

    public Scope newScope(Set<String> keySet) {
        RLock lock = buildLocks(keySet);
        lock.lock();
        return new Scope(lock);
    }

    private <T> RLock buildLocks(Set<String> lockKeys) {
        if (CollectionUtils.isEmpty(lockKeys)) {
            log.info("lock set can not be empty");
            throw new IllegalArgumentException();
        }
        RLock[] locks = new RLock[lockKeys.size()];
        TreeSet<String> sortKeys = new TreeSet<>(lockKeys);//按lock key排序，防止死锁
        int i = 0;
        for (String lockKey : sortKeys) {
            RLock lock = redissonClient.getLock(lockKey);
            locks[i] = lock;
            i++;
        }
        return redissonClient.getMultiLock(locks);
    }


    public static class Scope implements AutoCloseable {

        private final RLock lock;

        Scope(RLock lock) {
            this.lock = lock;
        }

        @Override
        public void close() {
            lock.unlock();
        }
    }

}
