package com.imile.attendance.infrastructure.logRecord;

import com.imile.attendance.infrastructure.logRecord.dto.LogRecordOptions;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31 
 * @Description 日志记录服务
 */
public interface LogRecordService {


    /**
     * 记录操作日志(不涉及对象比对)
     * @param obj 操作对象
     * @param logRecordOptions 日志选项配置
     */
    <T> void recordOperation(T obj, LogRecordOptions logRecordOptions);

    /**
     * 记录对象变更日志
     * @param newObject 新对象
     * @param oldObject 旧对象
     * @param logRecordOptions 日志选项配置
     */
    <T> void recordObjectChange(T newObject, T oldObject, LogRecordOptions logRecordOptions);


    /**
     * 记录列表变更日志 (新旧对象的id必须能对应上)
     * @param newList 新列表
     * @param oldList 旧列表(可为空表示全新增)
     * @param logRecordOptions 日志选项配置
     */
    <T> void recordListChange(List<T> newList, List<T> oldList, LogRecordOptions logRecordOptions);


    //todo 导入excel的日志

}
