package com.imile.attendance.infrastructure.logRecord.dto;

import lombok.Data;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31 
 * @Description
 */
@Data
public class OperatorLogDTO {

    /**
     * 操作人userCode非必填
     */
    private String operationUserCode;


    /**
     * 操作人userName非必填
     */
    private String operationUserName;

    /**
     * 外键关联id
     */
    private String foreignKey;

    /**
     * 外键关联表
     */
    private String foreignTable;

    /**
     * 操作编码
     */
    private String operationCode;

    /**
     * 操作编码描述
     */
    private String operationCodeDesc;

    /**
     * 操作模块
     */
    private String operationModule;

    /**
     * 操作类型
     */
    private String operationType;
}
