package com.imile.attendance.infrastructure.logRecord.enums;

import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31 
 * @Description
 */
@Getter
public enum OperationCodeEnum {

    CA<PERSON><PERSON>AR_MANAGE("CALE<PERSON>AR_MANAGE", "日历", OperationModuleEnum.CALE<PERSON>AR_MODULE),
    SHIFT_MANAGE("SHIFT_MANAGE", "班次", OperationModuleEnum.SHIFT_MODULE),
    PUNCH_CONFIG_MANAGE("PUNCH_CONFIG_MANAGE", "打卡规则", OperationModuleEnum.ATTENDANCE_RULE_MODULE),
    REISSUE_CARD_CONFIG_MANAGE("REISSUE_CARD_CONFIG_MANAGE", "补卡规则", OperationModuleEnum.ATTENDANCE_RULE_MODULE),
    OVER_TIME_CONFIG_MANAGE("OVER_TIME_CONFIG_MANAGE", "加班规则", OperationModuleEnum.ATTENDANCE_RULE_MODULE),
    SHIFT_SCHEDULE_MANAGE("SHIFT_SCHEDULE_MANAGE", "排班计划", OperationModuleEnum.SHIFT_SCHEDULE_MODULE),
    ATTENDANCE_ARCHIVE_MANAGE("ATTENDANCE_ARCHIVE_MANAGE", "考勤档案", OperationModuleEnum.ATTENDANCE_ARCHIVE_MODULE),

    GPS_CONFIG_MANAGE("GPS_CONFIG_MANAGE", "GPS", OperationModuleEnum.ATTENDANCE_DEVICE_MODULE),
    WIFI_CONFIG_MANAGE("WIFI_CONFIG_MANAGE", "WIFI", OperationModuleEnum.ATTENDANCE_DEVICE_MODULE),
    LEAVE_CONFIG_MANAGE("LEAVE_CONFIG_MANAGE", "假期规则", OperationModuleEnum.LEAVE_CONFIG_MODULE),
    ;



    private final String code;

    private final String desc;

    private final OperationModuleEnum operationModuleEnum;

    OperationCodeEnum(String code, String desc, OperationModuleEnum operationModuleEnum) {
        this.code = code;
        this.desc = desc;
        this.operationModuleEnum = operationModuleEnum;
    }

    private static final Map<String, OperationCodeEnum> cacheMap = new ConcurrentHashMap<>();

    public static OperationCodeEnum getOperationCode(String code) {
        return code == null ? null : cacheMap.get(code);
    }

    static {
        OperationCodeEnum[] attributes = values();
        for (OperationCodeEnum codeEnum : attributes) {
            cacheMap.put(codeEnum.getCode(), codeEnum);
        }

    }
}
