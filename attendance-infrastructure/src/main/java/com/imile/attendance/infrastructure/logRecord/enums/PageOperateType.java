package com.imile.attendance.infrastructure.logRecord.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31 
 * @Description 页面操作类型
 */
@Getter
@AllArgsConstructor
public enum PageOperateType {

    ADD("ADD", "新增"),
    UPDATE("UPDATE", "修改"),
    ACTIVE("ACTIVE", "启用"),
    DISABLE("DISABLE", "停用"),
    DELETE("DELETE", "删除"),
    IMPORT("IMPORT", "导入"),
    EXPORT("EXPORT", "导出"),
    ;


    private final String code;
    private final String desc;
}
