package com.imile.attendance.infrastructure.logRecord.impl;

import com.alibaba.fastjson.JSONObject;
import com.imile.attendance.infrastructure.logRecord.dto.FieldDiffDTO;
import com.imile.attendance.infrastructure.logRecord.LogContentService;
import com.imile.attendance.infrastructure.logRecord.enums.OperationCodeEnum;
import com.imile.attendance.infrastructure.logRecord.enums.PageOperateType;
import com.imile.attendance.util.ApiModelPropertyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31 
 * @Description
 */
@Slf4j
@Component
public class LogContentServiceImpl implements LogContentService {

    @Override
    public String commonLogContent(String country, String bizOperateName) {
        return "[" + country + "]的[" + bizOperateName + "]";
    }

    @Override
    public String logContent(PageOperateType pageOperateType, String bizOperate, String country, String bizOperateName) {
        String commonLogContent = commonLogContent(country, bizOperateName);
        return pageOperateType.getDesc() + bizOperate + ": " + commonLogContent;
    }

    @Override
    public String logContent(PageOperateType pageOperateType, OperationCodeEnum operationCodeEnum, String country, String bizName) {
        return logContent(pageOperateType, operationCodeEnum.getDesc(), country, bizName);
    }

    @Override
    public String pageUpdateLogContent(String bizOperate, String country, String bizOperateName, String fieldDiffJson) {
        String title = logContent(PageOperateType.UPDATE, bizOperate, country, bizOperateName);
        return title + "\n" + generateFieldDiffText(fieldDiffJson);
    }

    @Override
    public String pageUpdateLogContent(OperationCodeEnum operationCodeEnum, String country, String bizOperateName, String fieldDiffJson) {
        return pageUpdateLogContent(operationCodeEnum.getDesc(), country, bizOperateName, fieldDiffJson);
    }


    /**
     * 生成字段差异的可读文案
     * @param fieldDiffJson 字段差异的JSON字符串
     * @return 格式化后的差异文案
     */
    public String generateFieldDiffText(String fieldDiffJson) {
        try {
            if (StringUtils.isEmpty(fieldDiffJson)) {
                return "";
            }

            // 解析JSON字符串为FieldDiffDTO列表
            List<FieldDiffDTO> fieldDiffList = JSONObject.parseArray(fieldDiffJson, FieldDiffDTO.class);
            if (CollectionUtils.isEmpty(fieldDiffList)) {
                return "";
            }


            StringBuilder diffText = new StringBuilder();

            for (FieldDiffDTO fieldDiff : fieldDiffList) {
                String objClassName = fieldDiff.getObjClassName();
                Class<?> clazz = Class.forName(objClassName);

                JSONObject oldData = fieldDiff.getOldData();
                JSONObject newData = fieldDiff.getNewData();

                // 遍历新数据中发生变化的字段
                for (String fieldName : newData.keySet()) {
                    // 获取字段的中文名称
                    String fieldDisplayName = getFieldDisplayName(clazz, fieldName);

                    // 获取新旧值
                    Object oldValue = oldData.get(fieldName);
                    Object newValue = newData.get(fieldName);

                    // 格式化值显示
                    String oldDisplayValue = formatDisplayValue(fieldName, oldValue);
                    String newDisplayValue = formatDisplayValue(fieldName, newValue);

                    // 拼接差异文案
                    diffText.append(fieldDisplayName)
                            .append(":")
                            .append("[")
                            .append(oldDisplayValue)
                            .append("]")
                            .append("更新为")
                            .append("[")
                            .append(newDisplayValue)
                            .append("];")
                            .append("\n");
                }
            }

            return diffText.toString();
        } catch (Exception e) {
            log.error("生成字段差异文案失败", e);
            return "";
        }
    }

    /**
     * 获取字段显示名称
     */
    private String getFieldDisplayName(Class<?> clazz, String fieldName) {
        return ApiModelPropertyUtils.getPropertyValue(clazz, fieldName);
    }

    /**
     * 格式化显示值
     * @param value 原始值
     * @return 格式化后的显示值
     */
    private String formatDisplayValue(String fieldName, Object value) {
        if (value == null || StringUtils.isEmpty(fieldName)) {
            return "空";
        }
        if (StringUtils.startsWith(fieldName, "is")) {
            if (value.toString().equals("1")) {
                return "是";
            } else if (value.toString().equals("0")) {
                return "否";
            }
        }

        // 处理特殊类型的显示
        if (value instanceof Boolean) {
            return (Boolean) value ? "是" : "否";
        }

//        // 处理状态码等枚举值
//        if (value instanceof Number) {
//            // 这里可以通过枚举或配置转换状态码为可读文本
//            Map<String, String> statusMap = new HashMap<>();
//            statusMap.put("0", "禁用");
//            statusMap.put("1", "启用");
//            // ... 添加更多状态映射
//
//            return statusMap.getOrDefault(value.toString(), value.toString());
//        }

        return value.toString();
    }

    public static void main(String[] args) {
        LogContentServiceImpl logContentService = new LogContentServiceImpl();
//        String commonLogContent = logContentService.commonLogContent("中国", "办公室日历");
//        System.out.println(commonLogContent);

//        String logContent = logContentService.logContent(PageOperateType.ADD, "日历", "中国", "办公室日历");
//        System.out.println(logContent);


        String logContent = logContentService.pageUpdateLogContent(
                "日历", "中国", "办公室日历",
                "[{\"newData\":{\"attendanceConfigName\":\"测试日历-2-update\"},\"objClassName\":\"com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO\",\"oldData\":{\"id\":2,\"attendanceConfigName\":\"测试日历-2\"}}]");
        System.out.println(logContent);

    }
}
