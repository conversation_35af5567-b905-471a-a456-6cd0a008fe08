package com.imile.attendance.infrastructure.logRecord.mapstruct;

import com.imile.attendance.infrastructure.repository.log.dto.LogRecordPageDTO;
import com.imile.attendance.infrastructure.repository.log.model.LogOperationRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31 
 * @Description
 */
@Mapper
public interface LogRecordMapstruct {

    LogRecordMapstruct INSTANCE = Mappers.getMapper(LogRecordMapstruct.class);


    @Mapping(target = "operateUserName", source = "operationUserName")
    @Mapping(target = "operateUserCode", source = "operationUserCode")
    @Mapping(target = "operateTime", source = "operationTime")
    @Mapping(target = "operateContent", source = "remark")
    @Mapping(target = "moduleName", source = "operationModule")
    LogRecordPageDTO mapToPageDTO(LogOperationRecordDO logOperationRecordDO);

    List<LogRecordPageDTO> mapToPageDTO(List<LogOperationRecordDO> logOperationRecordDOList);
}
