package com.imile.attendance.infrastructure.mq.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/4/8 
 * @Description mq重试结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MqRetryResult {

    private static final Integer MAX_RETRY_COUNT = 5;

    /**
     * 重试是否成功
     */
    private boolean success;

    /**
     * 消息ID
     */
    private String msgId;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 最后重试时间
     */
    private Date lastRetryTime;

    /**
     * 是否达到最大重试次数
     */
    private boolean reachMaxRetry;

    public static MqRetryResult initEmptyMsgId(){
        return MqRetryResult.builder()
                .success(false)
                .errorMsg("not have a valid record, can not retry")
                .build();
    }

    /**
     * 创建失败结果
     */
    public static MqRetryResult failure(String msgId, Integer retryCount, String errorMsg) {
        return MqRetryResult.builder()
                .success(false)
                .msgId(msgId)
                .retryCount(retryCount)
                .errorMsg(errorMsg)
                .lastRetryTime(new Date())
                //根据次数判断
                .reachMaxRetry(isReachMaxRetry(retryCount))
                .build();
    }

    /**
     * 创建成功结果
     */
    public static MqRetryResult success(String msgId, Integer retryCount) {
        return MqRetryResult.builder()
                .success(true)
                .msgId(msgId)
                .retryCount(retryCount)
                .lastRetryTime(new Date())
                .reachMaxRetry(isReachMaxRetry(retryCount))
                .build();
    }

    public static boolean isReachMaxRetry(Integer retryCount){
        return retryCount >= MAX_RETRY_COUNT;
    }
}
