package com.imile.attendance.infrastructure.notice;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.notice.dto.UserWxInfoDTO;
import com.imile.util.oncall.enums.MsgTypeEnum;
import com.imile.util.oncall.notice.WxMessageUtil;
import com.imile.util.oncall.param.wx.AppMessageParam;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/5/29 
 * @Description
 */
@Slf4j
@Service
public class WeChatAppApiService {

    @Resource
    private WechatUserApiService wechatUserApiService;

    /**
     * 推送企业微信应用消息
     *
     * @param userCodeList         用户编码列表
     * @param isUserWxApiGetUser  是否通过用户API获取用户微信ID
     * @param templateName         模板名称
     * @param templateData         模板数据
     * @return Object
     */
    public Object sendAppMessage(List<String> userCodeList,
                                 Boolean isUserWxApiGetUser,
                                 String templateName,
                                 JSONObject templateData) {
        if (MapUtils.isEmpty(templateData)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_VALID_ERROR, "templateData is empty");
        }
        //获取用户的wiId
        List<String> wxIdList = null;
        if (isUserWxApiGetUser) {
            List<UserWxInfoDTO> userWxInfoDTOList = wechatUserApiService.batchGetUserWxIds(userCodeList);
            wxIdList = userWxInfoDTOList.stream()
                    .map(UserWxInfoDTO::getWxId)
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(wxIdList)) {
            log.error("sendClockMsg: WeChat relation not exist, userCodes:{}", userCodeList);
            return null;
        }
        String message = templateData.toJSONString();

        AppMessageParam param = new AppMessageParam();
        param.setAppId(BusinessConstant.IMILE_CLOCK_APP_ID);
        param.setSecret(BusinessConstant.IMILE_CLOCK_SECRET);
        param.setMsgtype(MsgTypeEnum.MARKDOWN.getCode());
        param.setAgentId(BusinessConstant.IMILE_CLOCK_AGENT_ID);
        param.setNeedTemplate(true);
        param.setToUser(wxIdList);
        param.setTemplateName(templateName);
        param.setContent(message);

        log.info("sendAppMessage | request={}", param);
        ResponseEntity<Object> response = null;
        try {
            response = WxMessageUtil.sendAppMessage(param);
            return response.getBody();
        } catch (Exception e) {
            log.error("sendAppMessage | error | request={}", param, e);
            return null;
        }
    }
}
