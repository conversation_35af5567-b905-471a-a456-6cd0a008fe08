package com.imile.attendance.infrastructure.notice;

import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.util.oncall.enums.MsgTypeEnum;
import com.imile.util.oncall.notice.WxMessageUtil;
import com.imile.util.oncall.param.wx.GroupAppMessageParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> chen
 * @Date 2025/5/29 
 * @Description
 */
@Slf4j
@Service
public class WeChatGroupAppApiService {

    /**
     * 推送企业微信群消息
     *
     * @param groupAppMessageParam 参数
     * @return ResponseEntity<Object>
     */
    public ResponseEntity<Object> sendGroupAppMessage(GroupAppMessageParam groupAppMessageParam) {
        groupAppMessageParam.setAppId(BusinessConstant.IMILE_CLOCK_APP_ID);
        groupAppMessageParam.setSecret(BusinessConstant.IMILE_CLOCK_SECRET);
        log.info("sendGroupAppMessage | request={}", groupAppMessageParam);
        ResponseEntity<Object> response = null;
        try {
            response = WxMessageUtil.sendGroupAppMessage(groupAppMessageParam);
        } catch (Exception e) {
            log.info("sendGroupAppMessage | error | request={}", groupAppMessageParam, e);
        }
        log.info("sendGroupAppMessage | request={} | response={}", groupAppMessageParam, response);
        return response;
    }

    /**
     * 推送企业微信群文本消息
     *
     * @param message 消息内容
     * @param chatId  群id
     */
    public void sendGroupAppTextMessage(String message, String chatId) {
        if (StringUtils.isEmpty(chatId)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_VALID_ERROR, chatId);
        }
        GroupAppMessageParam groupAppMessageParam = new GroupAppMessageParam();
        groupAppMessageParam.setMsgtype(MsgTypeEnum.TEXT.getCode());
        groupAppMessageParam.setChatId(chatId);
        groupAppMessageParam.setContent(message);
        sendGroupAppMessage(groupAppMessageParam);
    }
}
