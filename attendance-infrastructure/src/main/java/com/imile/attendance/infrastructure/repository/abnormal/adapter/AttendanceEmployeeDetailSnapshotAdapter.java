package com.imile.attendance.infrastructure.repository.abnormal.adapter;

import com.imile.attendance.hrms.RpcHrAbnormalClient;
import com.imile.attendance.infrastructure.adapter.AbstractPairAdapter;
import com.imile.attendance.infrastructure.adapter.DaoAdapter;
import com.imile.attendance.infrastructure.adapter.DataConverter;
import com.imile.attendance.infrastructure.config.EnableNewAttendanceConfig;
import com.imile.attendance.infrastructure.repository.abnormal.dao.AttendanceEmployeeDetailSnapshotDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailSnapshotDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendanceEmployeeDetailSnapshotDO;
import com.imile.hrms.api.attendance.dto.HrmsAttendanceEmployeeDetailSnapshotDTO;
import com.imile.util.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/18
 */
@Component
public class AttendanceEmployeeDetailSnapshotAdapter extends AbstractPairAdapter<AttendanceEmployeeDetailSnapshotDO, HrmsAttendanceEmployeeDetailSnapshotDO> implements DaoAdapter {

    @Resource
    private EnableNewAttendanceConfig enableNewAttendanceConfig;
    @Resource
    private AttendanceEmployeeDetailSnapshotDao attendanceEmployeeDetailSnapshotDao;
    @Resource
    private RpcHrAbnormalClient rpcHrAbnormalClient;

    public AttendanceEmployeeDetailSnapshotAdapter(List<DataConverter<AttendanceEmployeeDetailSnapshotDO, HrmsAttendanceEmployeeDetailSnapshotDO>> dataConverters) {
        super(dataConverters);
    }


    @Override
    public Boolean isDoubleWriteMode() {
        return enableNewAttendanceConfig.getAbnormalDoubleWriteEnabled();
    }

    //=====================dao层适配===============================


    public void updateBatchById(List<AttendanceEmployeeDetailSnapshotDO> attendanceEmployeeDetailSnapshotDOList) {
   /*     saveOrUpdateBatchNewWrapper(
                attendanceEmployeeDetailSnapshotDOList,
                newRecordList -> attendanceEmployeeDetailSnapshotDao.updateBatchById(newRecordList),
                oldRecordList -> hrmsAttendanceEmployeeDetailSnapshotDao.updateBatchById(oldRecordList)
        );*/

        attendanceEmployeeDetailSnapshotDao.updateBatchById(attendanceEmployeeDetailSnapshotDOList);

        if (isDoubleWriteMode()) {
            bizTaskThreadPool.execute(() -> {
                List<HrmsAttendanceEmployeeDetailSnapshotDTO> attendanceEmployeeDetailSnapshotDTOList = BeanUtils.convert(HrmsAttendanceEmployeeDetailSnapshotDTO.class, attendanceEmployeeDetailSnapshotDOList);
                rpcHrAbnormalClient.attendanceEmployeeDetailSnapshotBatchUpdate(attendanceEmployeeDetailSnapshotDTOList);
            });
        }
    }

    public void saveBatch(List<AttendanceEmployeeDetailSnapshotDO> attendanceEmployeeDetailSnapshotDOList) {
      /*  saveOrUpdateBatchNewWrapper(
                attendanceEmployeeDetailSnapshotDOList,
                newRecordList -> attendanceEmployeeDetailSnapshotDao.saveBatch(newRecordList),
                oldRecordList -> hrmsAttendanceEmployeeDetailSnapshotDao.saveBatch(oldRecordList)
        );*/

        attendanceEmployeeDetailSnapshotDao.saveBatch(attendanceEmployeeDetailSnapshotDOList);

        if (isDoubleWriteMode()) {
            bizTaskThreadPool.execute(() -> {
                List<HrmsAttendanceEmployeeDetailSnapshotDTO> attendanceEmployeeDetailSnapshotDTOList = BeanUtils.convert(HrmsAttendanceEmployeeDetailSnapshotDTO.class, attendanceEmployeeDetailSnapshotDOList);
                rpcHrAbnormalClient.attendanceEmployeeDetailSnapshotBatchSave(attendanceEmployeeDetailSnapshotDTOList);
            });
        }
    }
}
