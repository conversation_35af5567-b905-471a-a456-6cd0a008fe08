package com.imile.attendance.infrastructure.repository.abnormal.adapter;

import com.google.common.collect.Lists;
import com.imile.attendance.hrms.RpcHrAbnormalClient;
import com.imile.attendance.infrastructure.adapter.AbstractPairAdapter;
import com.imile.attendance.infrastructure.adapter.DaoAdapter;
import com.imile.attendance.infrastructure.adapter.DataConverter;
import com.imile.attendance.infrastructure.config.EnableNewAttendanceConfig;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalAttendanceDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalAttendanceDO;
import com.imile.hrms.api.attendance.dto.HrmsEmployeeAbnormalAttendanceDTO;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/18
 */
@Slf4j
@Component
public class EmployeeAbnormalAttendanceAdapter extends AbstractPairAdapter<EmployeeAbnormalAttendanceDO, HrmsEmployeeAbnormalAttendanceDO> implements DaoAdapter {

    @Resource
    private EnableNewAttendanceConfig enableNewAttendanceConfig;
    @Resource
    private EmployeeAbnormalAttendanceDao employeeAbnormalAttendanceDao;
    @Resource
    private RpcHrAbnormalClient rpcHrAbnormalClient;

    public EmployeeAbnormalAttendanceAdapter(List<DataConverter<EmployeeAbnormalAttendanceDO, HrmsEmployeeAbnormalAttendanceDO>> dataConverters) {
        super(dataConverters);
    }


    @Override
    public Boolean isDoubleWriteMode() {
        return enableNewAttendanceConfig.getAbnormalDoubleWriteEnabled();
    }

    //=====================dao层适配===============================

    public void updateById(EmployeeAbnormalAttendanceDO newRecord, HrmsEmployeeAbnormalAttendanceDO oldRecord) {
        employeeAbnormalAttendanceDao.updateById(newRecord);

        if (isDoubleWriteMode()) {
            bizTaskThreadPool.execute(() -> {
                HrmsEmployeeAbnormalAttendanceDTO hrmsEmployeeAbnormalAttendanceDTO = BeanUtils.convert(oldRecord, HrmsEmployeeAbnormalAttendanceDTO.class);
                rpcHrAbnormalClient.abnormalBatchUpdate(Collections.singletonList(hrmsEmployeeAbnormalAttendanceDTO));
            });
        }

     /*   if (isDoubleWriteMode()) {
            //异步写入老系统
            bizTaskThreadPool.execute(() -> {
                try {
                    DynamicDataSourceContextHolder.push(Constants.TableSchema.hrms);
                    hrmsEmployeeAbnormalAttendanceDao.updateById(oldRecord);
                } catch (Exception e) {
                    log.error("hrmsEmployeeAbnormalAttendanceDO updateById fail,exception:{}", Throwables.getStackTraceAsString(e));
                } finally {
                    // 清理数据源上下文
                    DynamicDataSourceContextHolder.clear();
                }
            });
        }*/
    }


    public void updateBatchById(List<EmployeeAbnormalAttendanceDO> newList, List<HrmsEmployeeAbnormalAttendanceDO> oldList) {
        employeeAbnormalAttendanceDao.updateBatchById(newList);

        if (isDoubleWriteMode()) {
            bizTaskThreadPool.execute(() -> {
                List<HrmsEmployeeAbnormalAttendanceDTO> hrmsEmployeeAbnormalAttendanceDTOList = BeanUtils.convert(HrmsEmployeeAbnormalAttendanceDTO.class, oldList);
                List<List<HrmsEmployeeAbnormalAttendanceDTO>> partitionList = Lists.partition(hrmsEmployeeAbnormalAttendanceDTOList, 200);
                partitionList.forEach(partition -> rpcHrAbnormalClient.abnormalBatchUpdate(hrmsEmployeeAbnormalAttendanceDTOList));

            });
        }

     /*   if (isDoubleWriteMode()) {
            bizTaskThreadPool.execute(() -> {
                try {
                    DynamicDataSourceContextHolder.push(Constants.TableSchema.hrms);
                    hrmsEmployeeAbnormalAttendanceDao.updateBatchById(oldList);
                } catch (Exception e) {
                    log.error("hrmsEmployeeAbnormalAttendanceDO updateBatchById fail ,exception:{}", Throwables.getStackTraceAsString(e));
                } finally {
                    // 清理数据源上下文
                    DynamicDataSourceContextHolder.clear();
                }
            });
        }*/
    }

    public void saveBatch(List<EmployeeAbnormalAttendanceDO> newList, List<HrmsEmployeeAbnormalAttendanceDO> oldList) {
        employeeAbnormalAttendanceDao.saveBatch(newList);

        if (isDoubleWriteMode()) {
            bizTaskThreadPool.execute(() -> {
                List<HrmsEmployeeAbnormalAttendanceDTO> hrmsEmployeeAbnormalAttendanceDTOList = BeanUtils.convert(HrmsEmployeeAbnormalAttendanceDTO.class, oldList);
                List<List<HrmsEmployeeAbnormalAttendanceDTO>> partitionList = Lists.partition(hrmsEmployeeAbnormalAttendanceDTOList, 200);
                partitionList.forEach(partition -> rpcHrAbnormalClient.abnormalBatchSave(hrmsEmployeeAbnormalAttendanceDTOList));

            });
        }

     /*   if (isDoubleWriteMode()) {
            bizTaskThreadPool.execute(() -> {
                try {
                    DynamicDataSourceContextHolder.push(Constants.TableSchema.hrms);
                    hrmsEmployeeAbnormalAttendanceDao.saveBatch(oldList);
                } catch (Exception e) {
                    log.error("hrmsEmployeeAbnormalAttendanceDO saveBatch fail ,exception:{}", Throwables.getStackTraceAsString(e));
                } finally {
                    // 清理数据源上下文
                    DynamicDataSourceContextHolder.clear();
                }
            });
        }*/
    }
}