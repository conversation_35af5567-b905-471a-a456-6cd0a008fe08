package com.imile.attendance.infrastructure.repository.abnormal.adapter;

import com.google.common.collect.Lists;
import com.imile.attendance.hrms.RpcHrAbnormalClient;
import com.imile.attendance.infrastructure.adapter.AbstractPairAdapter;
import com.imile.attendance.infrastructure.adapter.DaoAdapter;
import com.imile.attendance.infrastructure.adapter.DataConverter;
import com.imile.attendance.infrastructure.config.EnableNewAttendanceConfig;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalAttendanceSnapshotDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceSnapshotDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalAttendanceSnapshotDO;
import com.imile.hrms.api.attendance.dto.HrmsEmployeeAbnormalAttendanceSnapshotDTO;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/18
 */
@Slf4j
@Component
public class EmployeeAbnormalAttendanceSnapshotAdapter extends AbstractPairAdapter<EmployeeAbnormalAttendanceSnapshotDO, HrmsEmployeeAbnormalAttendanceSnapshotDO> implements DaoAdapter {

    @Resource
    private EnableNewAttendanceConfig enableNewAttendanceConfig;
    @Resource
    private EmployeeAbnormalAttendanceSnapshotDao employeeAbnormalAttendanceSnapshotDao;
    @Resource
    private RpcHrAbnormalClient rpcHrAbnormalClient;


    public EmployeeAbnormalAttendanceSnapshotAdapter(List<DataConverter<EmployeeAbnormalAttendanceSnapshotDO, HrmsEmployeeAbnormalAttendanceSnapshotDO>> dataConverters) {
        super(dataConverters);
    }

    @Override
    public Boolean isDoubleWriteMode() {
        return enableNewAttendanceConfig.getAbnormalDoubleWriteEnabled();
    }

    //=====================dao层适配===============================


    public void updateBatchById(List<EmployeeAbnormalAttendanceSnapshotDO> newList, List<HrmsEmployeeAbnormalAttendanceSnapshotDO> oldList) {
        employeeAbnormalAttendanceSnapshotDao.updateBatchById(newList);

        if (isDoubleWriteMode()) {
            bizTaskThreadPool.execute(() -> {
                List<HrmsEmployeeAbnormalAttendanceSnapshotDTO> hrmsEmployeeAbnormalAttendanceSnapshotDTOList = BeanUtils.convert(HrmsEmployeeAbnormalAttendanceSnapshotDTO.class, oldList);
                List<List<HrmsEmployeeAbnormalAttendanceSnapshotDTO>> partitionList = Lists.partition(hrmsEmployeeAbnormalAttendanceSnapshotDTOList, 200);
                partitionList.forEach(partition -> rpcHrAbnormalClient.abnormalSnapshotBatchUpdate(partition));

            });
        }

    /*    if (isDoubleWriteMode()) {
            //异步写入老系统
            bizTaskThreadPool.execute(() -> {
                try {
                    DynamicDataSourceContextHolder.push(Constants.TableSchema.hrms);
                    hrmsEmployeeAbnormalAttendanceSnapshotDao.updateBatchById(oldList);
                } catch (Exception e) {
                    log.error("hrmsEmployeeAbnormalAttendanceDO updateById fail,exception:{}", Throwables.getStackTraceAsString(e));
                } finally {
                    // 清理数据源上下文
                    DynamicDataSourceContextHolder.clear();
                }
            });
        }*/
    }

    public void saveBatch(List<EmployeeAbnormalAttendanceSnapshotDO> newList, List<HrmsEmployeeAbnormalAttendanceSnapshotDO> oldList) {
        employeeAbnormalAttendanceSnapshotDao.saveBatch(newList);

        if (isDoubleWriteMode()) {
            bizTaskThreadPool.execute(() -> {
                List<HrmsEmployeeAbnormalAttendanceSnapshotDTO> hrmsEmployeeAbnormalAttendanceSnapshotDTOList = BeanUtils.convert(HrmsEmployeeAbnormalAttendanceSnapshotDTO.class, oldList);
                List<List<HrmsEmployeeAbnormalAttendanceSnapshotDTO>> partitionList = Lists.partition(hrmsEmployeeAbnormalAttendanceSnapshotDTOList, 200);
                partitionList.forEach(partition -> rpcHrAbnormalClient.abnormalSnapshotBatchSave(partition));

            });
        }

       /* if (isDoubleWriteMode()) {
            bizTaskThreadPool.execute(() -> {
                try {
                    DynamicDataSourceContextHolder.push(Constants.TableSchema.hrms);
                    hrmsEmployeeAbnormalAttendanceSnapshotDao.saveBatch(oldList);
                } catch (Exception e) {
                    log.error("HrmsEmployeeAbnormalAttendanceSnapshotDO saveBatch fail ,exception:{}", Throwables.getStackTraceAsString(e));
                } finally {
                    // 清理数据源上下文
                    DynamicDataSourceContextHolder.clear();
                }
            });
        }*/
    }
}

