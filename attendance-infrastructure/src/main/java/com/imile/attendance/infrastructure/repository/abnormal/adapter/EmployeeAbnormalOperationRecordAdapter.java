package com.imile.attendance.infrastructure.repository.abnormal.adapter;

import com.imile.attendance.infrastructure.adapter.AbstractPairAdapter;
import com.imile.attendance.infrastructure.adapter.DaoAdapter;
import com.imile.attendance.infrastructure.adapter.DataConverter;
import com.imile.attendance.infrastructure.config.EnableNewAttendanceConfig;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalOperationRecordDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsEmployeeAbnormalOperationRecordDao;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalOperationRecordDO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/18
 */
@Component
public class EmployeeAbnormalOperationRecordAdapter extends AbstractPairAdapter<EmployeeAbnormalOperationRecordDO, HrmsEmployeeAbnormalOperationRecordDO> implements DaoAdapter {

    @Resource
    private EnableNewAttendanceConfig enableNewAttendanceConfig;
    @Resource
    private HrmsEmployeeAbnormalOperationRecordDao hrmsEmployeeAbnormalOperationRecordDao;
    @Resource
    private EmployeeAbnormalOperationRecordDao employeeAbnormalOperationRecordDao;

    public EmployeeAbnormalOperationRecordAdapter(List<DataConverter<EmployeeAbnormalOperationRecordDO, HrmsEmployeeAbnormalOperationRecordDO>> dataConverters) {
        super(dataConverters);
    }

    @Override
    public Boolean isDoubleWriteMode() {
        return enableNewAttendanceConfig.getAbnormalDoubleWriteEnabled();
    }

    //=====================dao层适配===============================

    public void updateById(EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecordDO) {
        saveOrUpdateOneNewWrapper(
                employeeAbnormalOperationRecordDO,
                newData -> employeeAbnormalOperationRecordDao.updateById(newData),
                oldData -> hrmsEmployeeAbnormalOperationRecordDao.updateById(oldData)
        );
    }

    public void save(EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecordDO) {
        saveOrUpdateOneNewWrapper(
                employeeAbnormalOperationRecordDO,
                newData -> employeeAbnormalOperationRecordDao.save(newData),
                oldData -> hrmsEmployeeAbnormalOperationRecordDao.save(oldData)
        );
    }

    public void updateBatchById(List<EmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOList) {
        saveOrUpdateBatchNewWrapper(
                employeeAbnormalOperationRecordDOList,
                newRecordList -> employeeAbnormalOperationRecordDao.updateBatchById(newRecordList),
                oldRecordList -> hrmsEmployeeAbnormalOperationRecordDao.updateBatchById(oldRecordList)
        );
    }

    public void saveBatch(List<EmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOList) {
        saveOrUpdateBatchNewWrapper(
                employeeAbnormalOperationRecordDOList,
                newRecordList -> employeeAbnormalOperationRecordDao.saveBatch(newRecordList),
                oldRecordList -> hrmsEmployeeAbnormalOperationRecordDao.saveBatch(oldRecordList)
        );
    }
}