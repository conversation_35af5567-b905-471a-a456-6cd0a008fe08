package com.imile.attendance.infrastructure.repository.abnormal.adapter;

import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.adapter.AbstractPairAdapter;
import com.imile.attendance.infrastructure.adapter.DaoAdapter;
import com.imile.attendance.infrastructure.adapter.DataConverter;
import com.imile.attendance.infrastructure.config.EnableNewAttendanceConfig;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.mapstruct.EmployeePunchRecordMapstruct;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsEmployeePunchRecordDao;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.punch.dao.EmployeePunchRecordDao;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/23
 */
@Component
public class EmployeePunchRecordAdapter extends AbstractPairAdapter<EmployeePunchRecordDO, HrmsEmployeePunchRecordDO> implements DaoAdapter {

    @Resource
    private EnableNewAttendanceConfig enableNewAttendanceConfig;
    @Resource
    private HrmsEmployeePunchRecordDao hrmsEmployeePunchRecordDao;
    @Resource
    private EmployeePunchRecordDao employeePunchRecordDao;

    public EmployeePunchRecordAdapter(List<DataConverter<EmployeePunchRecordDO, HrmsEmployeePunchRecordDO>> dataConverters) {
        super(dataConverters);
    }

    @Override
    public Boolean isDoubleWriteMode() {
        return enableNewAttendanceConfig.getAbnormalDoubleWriteEnabled();
    }

    //=====================dao层适配===============================

    public void updateById(EmployeePunchRecordDO employeePunchRecordDO) {
        saveOrUpdateOneWrapper(
                employeePunchRecordDO,
                newData -> employeePunchRecordDao.updateById(newData),
                newData -> {
                    HrmsEmployeePunchRecordDO hrmsEmployeePunchRecordDO = EmployeePunchRecordMapstruct.INSTANCE.mapToOld(newData);
                    hrmsEmployeePunchRecordDO.setFromNewSystem(BusinessConstant.Y);
                    hrmsEmployeePunchRecordDao.updateById(hrmsEmployeePunchRecordDO);
                }
        );
    }

    public void save(EmployeePunchRecordDO employeePunchRecordDO) {
        saveOrUpdateOneWrapper(
                employeePunchRecordDO,
                newData -> employeePunchRecordDao.save(newData),
                newData -> {
                    HrmsEmployeePunchRecordDO hrmsEmployeePunchRecordDO = EmployeePunchRecordMapstruct.INSTANCE.mapToOld(newData);
                    hrmsEmployeePunchRecordDO.setFromNewSystem(BusinessConstant.Y);
                    hrmsEmployeePunchRecordDao.save(hrmsEmployeePunchRecordDO);
                }
        );
    }

    public void updateBatchById(List<EmployeePunchRecordDO> employeePunchRecordDOList) {
        saveOrUpdateBatchWrapper(
                employeePunchRecordDOList,
                newRecordList -> employeePunchRecordDao.updateBatchById(newRecordList),
                newRecordList -> {
                    List<HrmsEmployeePunchRecordDO> hrmsEmployeePunchRecordDOList = EmployeePunchRecordMapstruct.INSTANCE.mapToOldList(employeePunchRecordDOList);
                    hrmsEmployeePunchRecordDOList.forEach(item -> item.setFromNewSystem(BusinessConstant.Y));
                    hrmsEmployeePunchRecordDao.updateBatchById(hrmsEmployeePunchRecordDOList);
                }
        );
    }

    public void saveBatch(List<EmployeePunchRecordDO> employeePunchRecordDOList) {
        saveOrUpdateBatchWrapper(
                employeePunchRecordDOList,
                newRecordList -> employeePunchRecordDao.saveBatch(newRecordList),
                newRecordList -> {
                    List<HrmsEmployeePunchRecordDO> hrmsEmployeePunchRecordDOList = EmployeePunchRecordMapstruct.INSTANCE.mapToOldList(employeePunchRecordDOList);
                    hrmsEmployeePunchRecordDOList.forEach(item -> item.setFromNewSystem(BusinessConstant.Y));
                    hrmsEmployeePunchRecordDao.saveBatch(hrmsEmployeePunchRecordDOList);
                }
        );
    }
}
