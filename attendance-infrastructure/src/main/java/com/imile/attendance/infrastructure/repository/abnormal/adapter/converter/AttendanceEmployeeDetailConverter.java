package com.imile.attendance.infrastructure.repository.abnormal.adapter.converter;

import com.imile.attendance.infrastructure.adapter.DataConverter;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.mapstruct.AttendanceEmployeeDetailMapstruct;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendanceEmployeeDetailDO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/6/18
 */
@Component(value = "AttendanceEmployeeDetailConverter")
public class AttendanceEmployeeDetailConverter implements DataConverter<AttendanceEmployeeDetailDO, HrmsAttendanceEmployeeDetailDO> {


    @Override
    public HrmsAttendanceEmployeeDetailDO convertFromNew(AttendanceEmployeeDetailDO newObj) {
        return AttendanceEmployeeDetailMapstruct.INSTANCE.mapToOld(newObj);
    }

    @Override
    public Class<AttendanceEmployeeDetailDO> getNewType() {
        return AttendanceEmployeeDetailDO.class;
    }

    @Override
    public Class<HrmsAttendanceEmployeeDetailDO> getOldType() {
        return HrmsAttendanceEmployeeDetailDO.class;
    }
}
