package com.imile.attendance.infrastructure.repository.abnormal.adapter.converter;

import com.imile.attendance.infrastructure.adapter.DataConverter;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.mapstruct.AttendanceEmployeeDetailSnapshotMapstruct;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailSnapshotDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendanceEmployeeDetailSnapshotDO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/6/18
 */
@Component(value = "AttendanceEmployeeDetailSnapshotConverter")
public class AttendanceEmployeeDetailSnapshotConverter implements DataConverter<AttendanceEmployeeDetailSnapshotDO, HrmsAttendanceEmployeeDetailSnapshotDO> {


    @Override
    public HrmsAttendanceEmployeeDetailSnapshotDO convertFromNew(AttendanceEmployeeDetailSnapshotDO newObj) {
        return AttendanceEmployeeDetailSnapshotMapstruct.INSTANCE.mapToOld(newObj);
    }

    @Override
    public Class<AttendanceEmployeeDetailSnapshotDO> getNewType() {
        return AttendanceEmployeeDetailSnapshotDO.class;
    }

    @Override
    public Class<HrmsAttendanceEmployeeDetailSnapshotDO> getOldType() {
        return HrmsAttendanceEmployeeDetailSnapshotDO.class;
    }
}
