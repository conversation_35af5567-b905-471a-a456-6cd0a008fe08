package com.imile.attendance.infrastructure.repository.abnormal.adapter.converter;

import com.imile.attendance.infrastructure.adapter.DataConverter;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.mapstruct.EmployeeAbnormalAttendanceMapstruct;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalAttendanceDO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/6/18
 */
@Component(value = "EmployeeAbnormalAttendanceConverter")
public class EmployeeAbnormalAttendanceConverter implements DataConverter<EmployeeAbnormalAttendanceDO, HrmsEmployeeAbnormalAttendanceDO> {


    @Override
    public HrmsEmployeeAbnormalAttendanceDO convertFromNew(EmployeeAbnormalAttendanceDO newObj) {
        return EmployeeAbnormalAttendanceMapstruct.INSTANCE.mapToOld(newObj);
    }

    @Override
    public Class<EmployeeAbnormalAttendanceDO> getNewType() {
        return EmployeeAbnormalAttendanceDO.class;
    }

    @Override
    public Class<HrmsEmployeeAbnormalAttendanceDO> getOldType() {
        return HrmsEmployeeAbnormalAttendanceDO.class;
    }
}
