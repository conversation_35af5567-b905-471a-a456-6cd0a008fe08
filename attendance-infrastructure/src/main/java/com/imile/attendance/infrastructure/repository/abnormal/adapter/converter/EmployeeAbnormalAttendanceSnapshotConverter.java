
package com.imile.attendance.infrastructure.repository.abnormal.adapter.converter;

import com.imile.attendance.infrastructure.adapter.DataConverter;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.mapstruct.EmployeeAbnormalAttendanceSnapshotMapstruct;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceSnapshotDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalAttendanceSnapshotDO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/6/18
 */
@Component(value = "EmployeeAbnormalAttendanceSnapshotConverter")
public class EmployeeAbnormalAttendanceSnapshotConverter implements DataConverter<EmployeeAbnormalAttendanceSnapshotDO, HrmsEmployeeAbnormalAttendanceSnapshotDO> {


    @Override
    public HrmsEmployeeAbnormalAttendanceSnapshotDO convertFromNew(EmployeeAbnormalAttendanceSnapshotDO newObj) {
        return EmployeeAbnormalAttendanceSnapshotMapstruct.INSTANCE.mapToOld(newObj);
    }

    @Override
    public Class<EmployeeAbnormalAttendanceSnapshotDO> getNewType() {
        return EmployeeAbnormalAttendanceSnapshotDO.class;
    }

    @Override
    public Class<HrmsEmployeeAbnormalAttendanceSnapshotDO> getOldType() {
        return HrmsEmployeeAbnormalAttendanceSnapshotDO.class;
    }
}
