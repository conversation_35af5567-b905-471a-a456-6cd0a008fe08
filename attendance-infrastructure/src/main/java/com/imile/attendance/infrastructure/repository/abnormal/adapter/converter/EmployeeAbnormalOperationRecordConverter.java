package com.imile.attendance.infrastructure.repository.abnormal.adapter.converter;

import com.imile.attendance.infrastructure.adapter.DataConverter;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.mapstruct.EmployeeAbnormalOperationRecordMapstruct;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalOperationRecordDO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/6/18
 */
@Component(value = "EmployeeAbnormalOperationRecordConverter")
public class EmployeeAbnormalOperationRecordConverter implements DataConverter<EmployeeAbnormalOperationRecordDO, HrmsEmployeeAbnormalOperationRecordDO> {


    @Override
    public HrmsEmployeeAbnormalOperationRecordDO convertFromNew(EmployeeAbnormalOperationRecordDO newObj) {
        return EmployeeAbnormalOperationRecordMapstruct.INSTANCE.mapToOld(newObj);
    }

    @Override
    public Class<EmployeeAbnormalOperationRecordDO> getNewType() {
        return EmployeeAbnormalOperationRecordDO.class;
    }

    @Override
    public Class<HrmsEmployeeAbnormalOperationRecordDO> getOldType() {
        return HrmsEmployeeAbnormalOperationRecordDO.class;
    }
}
