package com.imile.attendance.infrastructure.repository.abnormal.adapter.converter;

import com.imile.attendance.infrastructure.adapter.DataConverter;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.mapstruct.AttendanceEmployeeDetailMapstruct;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.mapstruct.EmployeePunchRecordMapstruct;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/6/23
 */
@Component(value = "EmployeePunchRecordConverter")
public class EmployeePunchRecordConverter implements DataConverter<EmployeePunchRecordDO, HrmsEmployeePunchRecordDO> {


    @Override
    public HrmsEmployeePunchRecordDO convertFromNew(EmployeePunchRecordDO newObj) {
        return EmployeePunchRecordMapstruct.INSTANCE.mapToOld(newObj);
    }

    @Override
    public Class<EmployeePunchRecordDO> getNewType() {
        return EmployeePunchRecordDO.class;
    }

    @Override
    public Class<HrmsEmployeePunchRecordDO> getOldType() {
        return HrmsEmployeePunchRecordDO.class;
    }
}
