
package com.imile.attendance.infrastructure.repository.abnormal.adapter.mapstruct;

import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendanceEmployeeDetailDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 员工出勤明细表映射
 *
 * <AUTHOR>
 * @since 2025/6/18
 */
@Mapper
public interface AttendanceEmployeeDetailMapstruct {

    AttendanceEmployeeDetailMapstruct INSTANCE = Mappers.getMapper(AttendanceEmployeeDetailMapstruct.class);

    HrmsAttendanceEmployeeDetailDO mapToOld(AttendanceEmployeeDetailDO attendanceEmployeeDetailDO);
}
