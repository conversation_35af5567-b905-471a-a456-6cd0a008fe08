
package com.imile.attendance.infrastructure.repository.abnormal.adapter.mapstruct;

import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailSnapshotDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendanceEmployeeDetailSnapshotDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 员工出勤明细快照表映射
 *
 * <AUTHOR>
 * @since 2025/6/18
 */
@Mapper
public interface AttendanceEmployeeDetailSnapshotMapstruct {

    AttendanceEmployeeDetailSnapshotMapstruct INSTANCE = Mappers.getMapper(AttendanceEmployeeDetailSnapshotMapstruct.class);

    HrmsAttendanceEmployeeDetailSnapshotDO mapToOld(AttendanceEmployeeDetailSnapshotDO attendanceEmployeeDetailSnapshotDO);
}
