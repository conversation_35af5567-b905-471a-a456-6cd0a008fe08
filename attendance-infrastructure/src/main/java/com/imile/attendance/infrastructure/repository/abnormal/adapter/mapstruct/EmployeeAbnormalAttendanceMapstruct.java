
package com.imile.attendance.infrastructure.repository.abnormal.adapter.mapstruct;

import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalAttendanceDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * 异常表映射
 *
 * <AUTHOR>
 * @since 2025/6/18
 */
@Mapper
public interface EmployeeAbnormalAttendanceMapstruct {

    EmployeeAbnormalAttendanceMapstruct INSTANCE = Mappers.getMapper(EmployeeAbnormalAttendanceMapstruct.class);

    HrmsEmployeeAbnormalAttendanceDO mapToOld(EmployeeAbnormalAttendanceDO employeeAbnormalAttendanceDO);

    List<HrmsEmployeeAbnormalAttendanceDO> mapToOldList(List<EmployeeAbnormalAttendanceDO> employeeAbnormalAttendanceDOList);
}
