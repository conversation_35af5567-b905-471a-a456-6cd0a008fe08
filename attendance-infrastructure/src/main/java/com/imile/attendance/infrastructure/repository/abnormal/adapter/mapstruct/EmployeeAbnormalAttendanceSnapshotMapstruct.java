
package com.imile.attendance.infrastructure.repository.abnormal.adapter.mapstruct;

import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceSnapshotDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalAttendanceSnapshotDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * 异常快照表映射
 *
 * <AUTHOR>
 * @since 2025/6/18
 */
@Mapper
public interface EmployeeAbnormalAttendanceSnapshotMapstruct {

    EmployeeAbnormalAttendanceSnapshotMapstruct INSTANCE = Mappers.getMapper(EmployeeAbnormalAttendanceSnapshotMapstruct.class);

    HrmsEmployeeAbnormalAttendanceSnapshotDO mapToOld(EmployeeAbnormalAttendanceSnapshotDO employeeAbnormalAttendanceSnapshotDO);

    List<HrmsEmployeeAbnormalAttendanceSnapshotDO> mapToOldList(List<EmployeeAbnormalAttendanceSnapshotDO> employeeAbnormalAttendanceSnapshotDOList);
}
