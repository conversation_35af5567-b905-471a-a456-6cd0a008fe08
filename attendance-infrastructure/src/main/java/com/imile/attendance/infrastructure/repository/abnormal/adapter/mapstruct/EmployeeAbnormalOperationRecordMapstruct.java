
package com.imile.attendance.infrastructure.repository.abnormal.adapter.mapstruct;

import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalOperationRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 异常操作记录表映射
 *
 * <AUTHOR>
 * @since 2025/6/18
 */
@Mapper
public interface EmployeeAbnormalOperationRecordMapstruct {

    EmployeeAbnormalOperationRecordMapstruct INSTANCE = Mappers.getMapper(EmployeeAbnormalOperationRecordMapstruct.class);

    HrmsEmployeeAbnormalOperationRecordDO mapToOld(EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecordDO);
}
