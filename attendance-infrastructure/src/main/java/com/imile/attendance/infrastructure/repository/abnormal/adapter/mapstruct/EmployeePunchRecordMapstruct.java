
package com.imile.attendance.infrastructure.repository.abnormal.adapter.mapstruct;

import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * 员工打卡记录表映射
 *
 * <AUTHOR>
 * @since 2025/6/23
 */
@Mapper
public interface EmployeePunchRecordMapstruct {

    EmployeePunchRecordMapstruct INSTANCE = Mappers.getMapper(EmployeePunchRecordMapstruct.class);

    HrmsEmployeePunchRecordDO mapToOld(EmployeePunchRecordDO employeePunchRecordDO);

    List<HrmsEmployeePunchRecordDO> mapToOldList(List<EmployeePunchRecordDO> employeePunchRecordDOList);
}
