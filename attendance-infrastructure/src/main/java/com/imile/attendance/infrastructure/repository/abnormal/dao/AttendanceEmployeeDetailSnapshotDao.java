package com.imile.attendance.infrastructure.repository.abnormal.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailSnapshotDO;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} AttendanceEmployeeDetailSnapshotService
 * {@code @since:} 2024-11-27 14:13
 * {@code @description:}
 */
public interface AttendanceEmployeeDetailSnapshotDao extends IService<AttendanceEmployeeDetailSnapshotDO> {


    List<AttendanceEmployeeDetailSnapshotDO> selectByUserIdListAndDayIdList(List<Long> userIds, List<Long> preDayIdList);

    List<AttendanceEmployeeDetailSnapshotDO> selectByIds(List<Long> idList);
}
