package com.imile.attendance.infrastructure.repository.abnormal.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.abnormal.dto.EmployeeAbnormalAttendanceDTO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.query.AbnormalAttendanceQuery;
import com.imile.attendance.infrastructure.repository.abnormal.query.EmployeeAbnormalAttendancePageQuery;

import java.util.List;

/**
 * <p>
 * 员工异常考勤数据表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-22
 */
public interface EmployeeAbnormalAttendanceDao extends IService<EmployeeAbnormalAttendanceDO> {

    EmployeeAbnormalAttendanceDO selectById(Long abnormalId);

    List<EmployeeAbnormalAttendanceDO> selectAbnormal(AbnormalAttendanceQuery query);


    List<EmployeeAbnormalAttendanceDO> listAbnormal(AbnormalAttendanceQuery query);

    List<EmployeeAbnormalAttendanceDO> selectAbnormalByIdList(List<Long> idList);

    /**
     * 查询用户指定天的异常考勤
     */
    List<EmployeeAbnormalAttendanceDO> selectAbnormalAttendanceByDayIdList(List<Long> userIdList, List<Long> dayIdList);

    /**
     * 查询用户的所有异常考勤
     */
    List<EmployeeAbnormalAttendanceDO> selectAbnormalByUserIdList(List<Long> userIdList);


    /**
     * 异常考勤分页查询
     *
     * @param query
     * @return
     */
    List<EmployeeAbnormalAttendanceDTO> list(EmployeeAbnormalAttendancePageQuery query);

    Integer selectCount(EmployeeAbnormalAttendancePageQuery query);


    /**
     * 查询用户当前考勤日的所有异常考勤
     */
    List<EmployeeAbnormalAttendanceDO> selectAbnormalByUserIdAndDayId(Long userId, Long dayId);


    /**
     * 查询用户在一段时间内的异常考勤
     */
    List<EmployeeAbnormalAttendanceDO> selectAbnormalByUserId(Long userId, Long startDayId, Long endDayId);

}
