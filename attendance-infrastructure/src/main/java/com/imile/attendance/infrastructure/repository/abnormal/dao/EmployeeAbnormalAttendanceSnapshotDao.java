package com.imile.attendance.infrastructure.repository.abnormal.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceSnapshotDO;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} EmployeeAbnormalAttendanceSnapshotDao
 * {@code @since:} 2024-11-27 14:45
 * {@code @description:}
 */
public interface EmployeeAbnormalAttendanceSnapshotDao extends IService<EmployeeAbnormalAttendanceSnapshotDO> {
    List<EmployeeAbnormalAttendanceSnapshotDO> selectByUserIdListAndDayIdList(List<Long> userIdList, List<Long> dayIdList);

    List<EmployeeAbnormalAttendanceSnapshotDO> selectByIds(List<Long> idList);
}
