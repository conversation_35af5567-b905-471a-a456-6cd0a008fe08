package com.imile.attendance.infrastructure.repository.abnormal.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.abnormal.dao.AttendanceEmployeeDetailSnapshotDao;
import com.imile.attendance.infrastructure.repository.abnormal.mapper.AttendanceEmployeeDetailSnapshotMapper;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailSnapshotDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceSnapshotDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} AttendanceEmployeeDetailSnapshotDaoImpl
 * {@code @since:} 2024-11-27 14:28
 * {@code @description:}
 */
@Service
@Slf4j
public class AttendanceEmployeeDetailSnapshotDaoImpl extends ServiceImpl<AttendanceEmployeeDetailSnapshotMapper, AttendanceEmployeeDetailSnapshotDO> implements AttendanceEmployeeDetailSnapshotDao {

    @Override
    public List<AttendanceEmployeeDetailSnapshotDO> selectByUserIdListAndDayIdList(List<Long> userIdList, List<Long> dayIdList) {
        if (CollectionUtils.isEmpty(userIdList) || CollectionUtils.isEmpty(dayIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<AttendanceEmployeeDetailSnapshotDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(AttendanceEmployeeDetailSnapshotDO::getUserId, userIdList);
        queryWrapper.in(AttendanceEmployeeDetailSnapshotDO::getDayId, dayIdList);
        queryWrapper.eq(AttendanceEmployeeDetailSnapshotDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<AttendanceEmployeeDetailSnapshotDO> selectByIds(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AttendanceEmployeeDetailSnapshotDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(AttendanceEmployeeDetailSnapshotDO::getId, idList);
        queryWrapper.eq(AttendanceEmployeeDetailSnapshotDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByAsc(AttendanceEmployeeDetailSnapshotDO::getCreateDate);
        return this.list(queryWrapper);
    }
}
