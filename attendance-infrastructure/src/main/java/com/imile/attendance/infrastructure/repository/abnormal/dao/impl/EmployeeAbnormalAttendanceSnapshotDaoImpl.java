package com.imile.attendance.infrastructure.repository.abnormal.dao.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalAttendanceSnapshotDao;
import com.imile.attendance.infrastructure.repository.abnormal.mapper.EmployeeAbnormalAttendanceSnapshotMapper;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceSnapshotDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} EmployeeAbnormalAttendanceSnapshotDaoImpl
 * {@code @since:} 2024-11-27 14:46
 * {@code @description:}
 */
@Service
@Slf4j
public class EmployeeAbnormalAttendanceSnapshotDaoImpl extends ServiceImpl<EmployeeAbnormalAttendanceSnapshotMapper, EmployeeAbnormalAttendanceSnapshotDO> implements EmployeeAbnormalAttendanceSnapshotDao {

    @Override
    public List<EmployeeAbnormalAttendanceSnapshotDO> selectByUserIdListAndDayIdList(List<Long> userIdList, List<Long> dayIdList) {
        if (CollectionUtils.isEmpty(userIdList) || CollectionUtils.isEmpty(dayIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<EmployeeAbnormalAttendanceSnapshotDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(EmployeeAbnormalAttendanceSnapshotDO::getUserId, userIdList);
        queryWrapper.in(EmployeeAbnormalAttendanceSnapshotDO::getDayId, dayIdList);
        queryWrapper.eq(EmployeeAbnormalAttendanceSnapshotDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<EmployeeAbnormalAttendanceSnapshotDO> selectByIds(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<EmployeeAbnormalAttendanceSnapshotDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(EmployeeAbnormalAttendanceSnapshotDO::getId, idList);
        queryWrapper.eq(EmployeeAbnormalAttendanceSnapshotDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByAsc(EmployeeAbnormalAttendanceSnapshotDO::getCreateDate);
        return this.list(queryWrapper);
    }
}
