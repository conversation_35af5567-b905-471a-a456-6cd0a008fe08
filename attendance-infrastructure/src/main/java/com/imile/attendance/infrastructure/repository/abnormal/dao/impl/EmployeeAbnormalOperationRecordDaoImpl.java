package com.imile.attendance.infrastructure.repository.abnormal.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalOperationRecordDao;
import com.imile.attendance.infrastructure.repository.abnormal.mapper.EmployeeAbnormalOperationRecordMapper;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.common.enums.IsDeleteEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 员工异常考勤操作记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-28
 */
@Service
public class EmployeeAbnormalOperationRecordDaoImpl extends ServiceImpl<EmployeeAbnormalOperationRecordMapper, EmployeeAbnormalOperationRecordDO> implements EmployeeAbnormalOperationRecordDao {
    @Override
    public List<EmployeeAbnormalOperationRecordDO> selectByAbnormalList(List<Long> abnormalList) {
        if (CollectionUtils.isEmpty(abnormalList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<EmployeeAbnormalOperationRecordDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(EmployeeAbnormalOperationRecordDO::getAbnormalId, abnormalList);
        wrapper.in(EmployeeAbnormalOperationRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        wrapper.orderByAsc(EmployeeAbnormalOperationRecordDO::getId);
        return this.list(wrapper);
    }
}
