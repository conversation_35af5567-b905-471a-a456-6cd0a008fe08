package com.imile.attendance.infrastructure.repository.abnormal.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 出勤明细DTO
 *
 * <AUTHOR>
 */
@Data
public class AttendanceDetailDTO {

    /**
     * 本日是否出勤
     */
    private Integer isAttendance;
    /**
     * 每日出勤比例
     */
    private BigDecimal attendanceRate;
    /**
     * 是否应出勤日
     */
    private Integer isNeedAttendance;
    /**
     * 考勤日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;
    /**
     * 天
     */
    private Integer day;
    /**
     * 星期几
     */
    private String dayOfWeek;
    /**
     * 司机OFD数据
     */
    private Integer driverOfdCount;

    /**
     * 加班小时数
     */
    private BigDecimal overtimeHours;

    /**
     * 出勤小时数
     */
    private BigDecimal attendanceHours;

    /**
     * 请假小时数
     */
    private BigDecimal leaveHours;

    /**
     * 缺勤小时数
     */
    private BigDecimal absentHours;

    /**
     * 加班时长(分钟)
     */
    private BigDecimal overtimeMinutes;

    /**
     * 出勤时长(分钟)
     */
    private BigDecimal attendanceMinutes;

    /**
     * 请假时长(分钟)
     */
    private BigDecimal leaveMinutes;

    /**
     * 外勤时长(分钟)
     */
    private BigDecimal oooMinutes;


    /**
     * 缺勤时长(分钟)
     */
    private BigDecimal absentMinutes;

    /**
     * 当天法定工作时长
     */
    private BigDecimal defaultLegalWorkingHours;

    /**
     * dayId
     */
    private Long dayId;
    /**
     * 本日出勤类型：工作日，节假日，休息日
     */
    private String attendanceType;
    /**
     * 本日是否允许编辑
     */
    private boolean editPermission;

    /**
     * 仓内员工扫件数
     */
    private Integer scanNumber;

    /**
     * 具体的考勤类型(只针对出勤/缺勤)
     */
    private String attendanceConcreteType;

    /**
     * 打卡规则类型
     */
    private String punchConfigType;

    /**
     * 打卡规则类型描述
     */
    private String punchConfigTypeDesc;

    /**
     * 考勤照片路径(只针对出勤/缺勤)
     */
    private List<String> attendancePicturePathList;

    /**
     * 员工在该天的请假类型和时长
     */
    private List<LeaveHoursRecordDTO> leaveHoursRecordDTOList;

    /**
     * 员工在该天是否提交过考勤流程(请假/补卡/外勤)
     * TODO 后续添加 加班、出差
     */
    private boolean hasSubmittedAttendanceProcess;
}
