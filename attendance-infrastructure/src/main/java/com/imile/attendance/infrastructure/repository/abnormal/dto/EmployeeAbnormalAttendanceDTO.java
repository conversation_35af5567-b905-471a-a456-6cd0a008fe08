package com.imile.attendance.infrastructure.repository.abnormal.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EmployeeAbnormalAttendanceDTO implements Serializable {
    private static final long serialVersionUID = 8142687005267401979L;

    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户编码
     */
    private String userCode;
    /**
     * 工号
     */
    private String workNo;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 邮件
     */
    private String email;
    /**
     * 员工类型
     */
    private String employeeType;
    /**
     * 异常类型
     */
    private String abnormalType;

    private String abnormalTypeDesc;

    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 部门名称-英文
     */
    private String deptNameEn;

    private Long dayId;
    /**
     * 考勤异常日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;
    /**
     * 考勤日类型
     */
    private String attendanceType;

    /**
     * 最后修改时间
     */
    private Date lastUpdDate;

    /**
     * 最后修改人名称
     */
    private String lastUpdUserName;

    /**
     * 异常数据状态
     */
    private String status;

    /**
     * 异常数据状态
     */
    private String statusDesc;

    /**
     * 所属国
     */
    private String country;

    /**
     * 打卡规则id
     */
    private Long punchConfigId;

    /**
     * 打卡班次id
     */
    private Long punchClassConfigId;

    /**
     * 打卡时段id
     */
    private Long punchClassItemConfigId;

    /**
     * 日历名称
     */
    private String attendanceConfigName;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;

    /**
     * 工作网点ID
     */
    private Long ocId;

    /**
     * 工作网点名称
     */
    private String ocName;

    /**
     * 工作供应商ID
     */
    private Long vendorId;

    /**
     * 工作供应商名称
     */
    private String vendorName;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 职工类型
     */
    private String staffType;

    /**
     * 处理信息
     */
    private String processingInformation;

    /**
     * 用工形式
     */
    private String employmentForm;
}
