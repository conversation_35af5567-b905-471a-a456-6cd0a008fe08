package com.imile.attendance.infrastructure.repository.abnormal.query;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AbnormalAttendanceQuery {

    private List<Long> userIds;

    private Date startDate;

    private Date endDate;

    private String dayId;

    private List<String> dayIds;

    private String status;

    private List<String> statusList;

    private List<Long> userIdList;

    private List<Long> deptIdList;

    private List<String> countryList;

    private Long startDayId;
}
