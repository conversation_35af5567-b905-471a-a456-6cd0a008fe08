package com.imile.attendance.infrastructure.repository.abnormal.query;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class AttendanceEmployeeDetailQuery {
    /**
     * userCode
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String userCode;

    /**
     * 年
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long year;

    /**
     * 月
     */
    private Long month;

}
