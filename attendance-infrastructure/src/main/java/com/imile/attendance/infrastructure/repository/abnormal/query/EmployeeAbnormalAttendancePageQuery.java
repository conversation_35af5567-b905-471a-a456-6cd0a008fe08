package com.imile.attendance.infrastructure.repository.abnormal.query;

import com.imile.attendance.query.ResourceQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmployeeAbnormalAttendancePageQuery extends ResourceQuery {

    /**
     * 姓名、系统账号名、员工编码、工号
     */
    private String nameOrCode;

    /**
     * 用户账号或姓名
     */
    private String userCodeOrName;
    /**
     * 部门id
     */
    private Long deptId;

    private List<Long> deptIds;

    /**
     * 状态(异常单据状态)
     */
    private String status;
    /**
     * 员工类型
     */
    private String employeeType;
    /**
     * 员工类型集合
     */
    private List<String> employeeTypeList;

    /**
     * 异常类型
     */
    private String abnormalType;

    /**
     * 异常类型列表
     */
    private List<String> abnormalTypeList;

    /**
     * 开始时间
     */
    private Date startDate;
    /**
     * 截止时间
     */
    private Date endDate;
    /**
     * 邮箱
     */
    private String email;

    /**
     * 司机:driver 仓内:warehouse 办公室员工:office  已处理界面不用传，全部类型都需要查出来
     */
    private List<String> staffTypes;

    private String staffType;

    private List<Long> userIds;

    private List<String> statusList;

    /**
     * 常驻国
     */
    private String locationCountry;

    /**
     * 权限常驻国
     */
    private List<String> authLocationCountryList;

    /**
     * 权限部门
     */
    private List<Long> authDeptIdList;

    /**
     * 考勤日历编码
     */
    private String attendanceConfigNo;

    /**
     * 配置打卡No
     */
    private String punchConfigNo;

    /**
     * 查询来源 1:仓内异常查询
     */
    private Integer source;

    /**
     * 异常id列表
     */
    private List<Long> abnormalIds;

    /**
     * 考勤日
     */
    private Long dayId;

    /**
     * 配置打卡时段
     */
    private List<Long> punchClassItemIdList;

    /**
     * 班次ID
     */
    private Long classId;
}
