package com.imile.attendance.infrastructure.repository.base.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/4/8
 * @Description
 */
@ApiModel(description = "MQ消息消费失败记录表")
@Data
@EqualsAndHashCode(callSuper = false) // 假设没有继承父类，如果继承了，请修改
@TableName("mq_fail_record")
public class MqFailRecordDO extends BaseDO {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "消息主题")
    private String topic;

    @ApiModelProperty(value = "消息标签")
    private String tag;

    @ApiModelProperty(value = "消息key")
    private String msgKey;

    @ApiModelProperty(value = "消息ID")
    private String msgId;

    @ApiModelProperty(value = "消息内容")
    private String msgBody;

    @ApiModelProperty(value = "消息重试服务")
    private String msgRetryServiceBean;

    @ApiModelProperty(value = "处理状态：0-初始化，1-重试成功，2-重试失败")
    private Integer status;

    @ApiModelProperty(value = "重试次数")
    private Integer retryCount;

    @ApiModelProperty(value = "错误信息")
    private String errorMsg;

    @ApiModelProperty(value = "错误堆栈")
    private String errorStack;

    @ApiModelProperty(value = "最后重试时间")
    private Date lastRetryTime;
}

