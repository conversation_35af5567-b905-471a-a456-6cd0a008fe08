package com.imile.attendance.infrastructure.repository.calendar.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.enums.CycleTypeEnum;
import com.imile.attendance.infrastructure.repository.calendar.model.BaseDayInfoDO;
import com.imile.attendance.infrastructure.repository.calendar.query.BaseDayQuery;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description
 */
public interface BaseDayInfoDao extends IService<BaseDayInfoDO> {

    /**
     * 返回日历
     * @param query
     * @return
     */
    List<BaseDayInfoDO> getBaseDay(BaseDayQuery query);

    /**
     * 获取上个薪资周期具体日期
     * @param CycleDate
     * @param cycleType
     * @param beginDate
     * @return
     */
    BaseDayInfoDO getCycleDate(String CycleDate, CycleTypeEnum cycleType, Date beginDate);

}

