package com.imile.attendance.infrastructure.repository.calendar.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.calendar.dto.CalendarConfigRangeIdsDTO;
import com.imile.attendance.infrastructure.repository.calendar.dto.CalendarRangeCountDTO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigDateQuery;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigRangeQuery;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} CalendarConfigRangeDao
 * {@code @since:} 2025-01-17 15:32
 * {@code @description:}
 */
public interface CalendarConfigRangeDao extends IService<CalendarConfigRangeDO> {

    /**
     * 查询日历配置对应的适用范围
     */
    CalendarConfigRangeIdsDTO getActiveRecords(CalendarConfigRangeQuery calendarConfigRangeQuery);

    /**
     * 覆盖旧记录
     *
     * @param bizIds
     */
    void coverOldRecords(List<Long> bizIds);

    /**
     * 覆盖旧记录(业务编码)
     *
     * @param bizCodes
     */
    void coverOldRecordsByBizCodes(List<String> bizCodes);

    /**
     * 将日历配置对应的适用范围置为失效
     *
     * @param calendarConfigNo
     */
    void updateToOld(String calendarConfigNo);

    /**
     * 将日历配置对应的适用范围置为失效，结束时间置为Now()
     *
     * @param calendarConfigNo
     * @param targetIsLatest   是否最新,如果传1，则会被更新为1，如果为0则会被更新为0，允许为空
     */
    void updateToOld(String calendarConfigNo, Integer targetIsLatest);

    /**
     * 继承父部门的考勤配置(用于部门新增)，todo 内部添加数据，不可直接适配双写
     *
     * @param parentDeptId
     * @param deptId
     * @return
     */
    void extendParentDept(Long parentDeptId, Long deptId);

    /**
     * 继承父部门的考勤配置(用于部门新增-编码)，todo 内部添加数据，不可直接适配双写
     *
     * @param parentDeptCode
     * @param deptCode
     */
    void extendParentDeptByCode(String parentDeptCode, String deptCode);

    /**
     * 根据bizIds和公司id查询员工配置的考勤日历范围
     *
     * @param bizIds
     * @return
     */
    List<CalendarConfigRangeDO> selectConfigRange(List<Long> bizIds);


    /**
     * 根据bizCodes查询员工配置的考勤日历
     *
     * @param bizCodes
     * @return
     */
    List<CalendarConfigRangeDO> selectConfigRangeByBizCodes(List<String> bizCodes);


    /**
     * 根据日历编码查询日历配置信息
     *
     * @param calendarConfigNo
     * @return
     */
    List<CalendarConfigRangeDO> selectRangeByCalendarConfigNo(String calendarConfigNo);


    List<CalendarConfigRangeDO> selectCalendarConfigByDate(CalendarConfigDateQuery query);

    /**
     * 根据日历配置id查询
     */
    List<CalendarConfigRangeDO> selectCalendarConfigByIds(List<Long> calendarConfigIds);

    /**
     * 查询指定条件的生效数据
     *
     * @param query
     * @return
     */
    List<CalendarConfigRangeDO> listAllRecords(CalendarConfigRangeQuery query);


    List<CalendarConfigRangeDO> listByPage(int currentPage, int pageSize);

    /**
     * 统计考勤日历范围的绑定员工数
     * 过滤在职且启用的员工
     */
    List<CalendarRangeCountDTO> countCalendarRange(List<Long> calendarConfigIds);

    /**
     * 解绑用户日历适用范围
     */
    void deleteByBizId(Long bizId);

    /**
     * 查询在职非司机且未配置日历的用户列表
     */
    List<UserInfoDO> listOnJobNoDriverUsersExcludeRangeConfigured(RuleRangeUserQuery ruleRangeUserQuery);

    /**
     * 查询用户所有日历
     */
    List<CalendarConfigRangeDO> selectAllByBizId(Long bizId);
}
