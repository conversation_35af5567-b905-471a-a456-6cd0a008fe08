package com.imile.attendance.infrastructure.repository.calendar.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarLegalLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarLegalLeaveConfigQuery;

import java.util.List;

/**
 * <p>
 * 日历法定假期配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-23
 */
public interface CalendarLegalLeaveConfigDao extends IService<CalendarLegalLeaveConfigDO> {

    /**
     * 根据条件查询法定假期
     * @param query 查询条件
     * @return 法定假期列表
     */
    List<CalendarLegalLeaveConfigDO> queryByCondition(CalendarLegalLeaveConfigQuery query);

    List<CalendarLegalLeaveConfigDO> queryByIds(List<Long> ids);

    List<CalendarLegalLeaveConfigDO> queryByConditionGroup(CalendarLegalLeaveConfigQuery query);

    List<CalendarLegalLeaveConfigDO> listByPage(int currentPage, int pageSize);

    CalendarLegalLeaveConfigDO getByCalendarConfigIdAndDayId(Long calendarConfigId, Long dayId);

    List<CalendarLegalLeaveConfigDO> selectListByConfigIdsAndYear(List<Long> calendarConfigIds, List<Integer> years);
}
