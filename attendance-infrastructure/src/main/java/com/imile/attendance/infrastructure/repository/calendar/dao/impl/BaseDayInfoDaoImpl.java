package com.imile.attendance.infrastructure.repository.calendar.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.enums.CycleTypeEnum;
import com.imile.attendance.infrastructure.repository.calendar.mapper.BaseDayInfoMapper;
import com.imile.attendance.infrastructure.repository.calendar.model.BaseDayInfoDO;
import com.imile.attendance.infrastructure.repository.calendar.query.BaseDayQuery;
import com.imile.attendance.infrastructure.repository.calendar.dao.BaseDayInfoDao;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description
 */
@Component
@RequiredArgsConstructor
public class BaseDayInfoDaoImpl extends ServiceImpl<BaseDayInfoMapper, BaseDayInfoDO> implements BaseDayInfoDao {

    @Override
    public List<BaseDayInfoDO> getBaseDay(BaseDayQuery query) {
        LambdaQueryWrapper<BaseDayInfoDO> queryWrapper = new LambdaQueryWrapper<>();
        if (query.getYear() != null) {
            queryWrapper.eq(BaseDayInfoDO::getYear, query.getYear());
        }
        if (Objects.nonNull(query.getStartDay()) && Objects.nonNull(query.getEndDay())) {
            queryWrapper.between(BaseDayInfoDO::getId, query.getStartDay(), query.getEndDay());
        }
        if (Objects.nonNull(query.getMonth())) {
            queryWrapper.eq(BaseDayInfoDO::getMonth, query.getMonth());
        }
        queryWrapper.eq(BaseDayInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());

        return list(queryWrapper);
    }

    @Override
    public BaseDayInfoDO getCycleDate(String CycleDate, CycleTypeEnum cycleType, Date beginDate) {
        LambdaQueryWrapper<BaseDayInfoDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.lt(BaseDayInfoDO::getDate, beginDate);
        if (cycleType != null && cycleType.name().equals(CycleTypeEnum.MONTH.name())) {
            queryWrapper.eq(BaseDayInfoDO::getDay, CycleDate);
        } else if (cycleType != null) {
            queryWrapper.eq(BaseDayInfoDO::getDayOfWeek, CycleDate);
        }
        queryWrapper.eq(BaseDayInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(BaseDayInfoDO::getId);
        queryWrapper.last("limit 1");
        return getOne(queryWrapper);
    }
}

