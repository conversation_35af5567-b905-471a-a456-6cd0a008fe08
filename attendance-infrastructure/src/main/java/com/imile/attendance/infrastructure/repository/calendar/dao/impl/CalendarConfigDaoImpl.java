package com.imile.attendance.infrastructure.repository.calendar.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.enums.AttendanceTypeEnum;
import com.imile.attendance.infrastructure.repository.calendar.mapper.CalendarConfigMapper;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigQuery;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigDao;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * {@code @author:} allen
 * {@code @className:} CalendarConfigDaoImpl
 * {@code @since:} 2025-01-17 13:42
 * {@code @description:}
 */
@Component
@DS(Constants.TableSchema.attendance)
@RequiredArgsConstructor
public class CalendarConfigDaoImpl extends ServiceImpl<CalendarConfigMapper, CalendarConfigDO> implements CalendarConfigDao {

    @Override
    public List<CalendarConfigDO> selectByCountryList(List<String> countryList) {
        if (CollectionUtils.isEmpty(countryList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CalendarConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CalendarConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(CalendarConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(CalendarConfigDO::getCountry, countryList);
        return this.list(queryWrapper);
    }

    @Override
    public CalendarConfigDO getActiveById(Long id) {
        LambdaQueryWrapper<CalendarConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CalendarConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(CalendarConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(CalendarConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(CalendarConfigDO::getId, id);
        return this.getOne(queryWrapper);
    }

    @Override
    public Integer countDefaultCalendarConfig(String country) {
        if (StringUtils.isBlank(country)) {
            return BusinessConstant.ZERO;
        }
        LambdaQueryWrapper<CalendarConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CalendarConfigDO::getCountry, country);
        queryWrapper.eq(CalendarConfigDO::getType, AttendanceTypeEnum.DEFAULT.name());
        queryWrapper.eq(CalendarConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(CalendarConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(CalendarConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        return this.count(queryWrapper);
    }

    @Override
    public List<CalendarConfigDO> listByQuery(CalendarConfigQuery query) {
        LambdaQueryWrapper<CalendarConfigDO> queryWrapper = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(query.getCountry())) {
            queryWrapper.eq(CalendarConfigDO::getCountry, query.getCountry());
        }
        if (CollectionUtils.isNotEmpty(query.getCountryList())) {
            queryWrapper.in(CalendarConfigDO::getCountry, query.getCountryList());
        }
        if (StringUtils.isNotEmpty(query.getType())) {
            queryWrapper.eq(CalendarConfigDO::getType, query.getType());
        }
        if (CollectionUtils.isNotEmpty(query.getNos())) {
            queryWrapper.in(CalendarConfigDO::getAttendanceConfigNo, query.getNos());
        }
        if (CollectionUtils.isNotEmpty(query.getIds())) {
            queryWrapper.in(CalendarConfigDO::getId, query.getIds());
        }
        if (StringUtils.isNotBlank(query.getStatus())) {
            queryWrapper.eq(CalendarConfigDO::getStatus, query.getStatus());
        }
        //todo like查询
        if (CollectionUtils.isNotEmpty(query.getDeptIdList())) {
            queryWrapper.and(i -> query.getDeptIdList()
                    .forEach(deptId -> i.like(CalendarConfigDO::getDeptIds, deptId).or()));
        }
        if (CollectionUtils.isNotEmpty(query.getDeptCodeList())) {
            queryWrapper.and(i -> query.getDeptCodeList()
                    .forEach(deptCode -> i.like(CalendarConfigDO::getDeptCodes, deptCode).or()));
        }
        if (StringUtils.isNotBlank(query.getAttendanceConfigName())) {
            queryWrapper.like(CalendarConfigDO::getAttendanceConfigName, query.getAttendanceConfigName());
        }
        queryWrapper.eq(CalendarConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(CalendarConfigDO::getIsLatest, BusinessConstant.Y);
        // 创建时间升序
        queryWrapper.orderByAsc(CalendarConfigDO::getCreateDate);
        return list(queryWrapper);
    }

    @Override
    public List<CalendarConfigDO> listByIds(Collection<? extends Serializable> idList) {
        CalendarConfigQuery query = new CalendarConfigQuery();
        query.setIds(idList);
        return listByQuery(query);
    }

    @Override
    public List<CalendarConfigDO> listByNos(Collection<String> attendanceConfigNos) {
        CalendarConfigQuery query = new CalendarConfigQuery();
        query.setNos(attendanceConfigNos);
        return listByQuery(query);
    }

    @Override
    public List<CalendarConfigDO> getCountryCalendarByType(AttendanceTypeEnum calendarType, String country) {
        LambdaQueryWrapper<CalendarConfigDO> query = new LambdaQueryWrapper<>();
        if (Objects.nonNull(calendarType)) {
            query.eq(CalendarConfigDO::getType, calendarType.name());
        }
        query.eq(StringUtils.isNotBlank(country), CalendarConfigDO::getCountry, country)
                .eq(CalendarConfigDO::getStatus, StatusEnum.ACTIVE.getCode())
                .eq(CalendarConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(query);
    }

    @Override
    public List<CalendarConfigDO> listByCountry(String country) {
        if (StringUtils.isBlank(country)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CalendarConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CalendarConfigDO::getCountry, country);
        queryWrapper.eq(CalendarConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(CalendarConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(CalendarConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<CalendarConfigDO> getByCalendarConfigIds(List<Long> calendarConfigIds) {
        if (CollectionUtils.isEmpty(calendarConfigIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CalendarConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CalendarConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .in(CalendarConfigDO::getId, calendarConfigIds);
        return this.list(queryWrapper);
    }

    @Override
    public List<CalendarConfigDO> listByPage(int currentPage, int pageSize) {
        PageInfo<CalendarConfigDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }
}
