package com.imile.attendance.infrastructure.repository.calendar.dao.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigDetailDao;
import com.imile.attendance.infrastructure.repository.calendar.mapper.CalendarConfigDetailMapper;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigDetailQuery;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} CalendarConfigDetailDaoImpl
 * {@code @since:} 2025-01-17 15:14
 * {@code @description:}
 */
@Component
@DS(Constants.TableSchema.attendance)
@RequiredArgsConstructor
public class CalendarConfigDetailDaoImpl extends ServiceImpl<CalendarConfigDetailMapper, CalendarConfigDetailDO> implements CalendarConfigDetailDao {


    @Override
    public List<CalendarConfigDetailDO> listLatestRecords(CalendarConfigDetailQuery configDetailQuery) {
        LambdaQueryWrapper<CalendarConfigDetailDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(configDetailQuery.getCalendarConfigId() != null,
                        CalendarConfigDetailDO::getAttendanceConfigId, configDetailQuery.getCalendarConfigId())
                .eq(configDetailQuery.getYear() != null, CalendarConfigDetailDO::getYear, configDetailQuery.getYear())
                .eq(configDetailQuery.getMonth() != null, CalendarConfigDetailDO::getMonth, configDetailQuery.getMonth())
                .eq(CalendarConfigDetailDO::getIsLatest, BusinessConstant.Y)
                .eq(CalendarConfigDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public void updateToOld(Long calendarConfigId, List<Integer> yearList) {
        // 校验考勤方案配置id不能为空
        BusinessLogicException.checkTrue(calendarConfigId == null,
                MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "calendarConfigId");

        LambdaUpdateWrapper<CalendarConfigDetailDO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(CalendarConfigDetailDO::getAttendanceConfigId, calendarConfigId)
                .eq(CalendarConfigDetailDO::getIsLatest, BusinessConstant.Y)
                .eq(CalendarConfigDetailDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .in(CollUtil.isNotEmpty(yearList), CalendarConfigDetailDO::getYear, yearList);

        CalendarConfigDetailDO model = new CalendarConfigDetailDO();
        model.setIsLatest(BusinessConstant.N);
        BaseDOUtil.fillDOUpdate(model);
        update(model, updateWrapper);
    }

    @Override
    public List<CalendarConfigDetailDO> listRecords(CalendarConfigDetailQuery configDetailQuery) {
        LambdaQueryWrapper<CalendarConfigDetailDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(configDetailQuery.getCalendarConfigId() != null,
                        CalendarConfigDetailDO::getAttendanceConfigId, configDetailQuery.getCalendarConfigId())
                .ge(configDetailQuery.getStartTime() != null, CalendarConfigDetailDO::getDate, configDetailQuery.getStartTime())
                .lt(configDetailQuery.getEndTime() != null, CalendarConfigDetailDO::getDate, configDetailQuery.getEndTime())
                .eq(configDetailQuery.getYear() != null, CalendarConfigDetailDO::getYear, configDetailQuery.getYear())
                .eq(configDetailQuery.getDayId() != null, CalendarConfigDetailDO::getDayId, configDetailQuery.getDayId())
                .eq(CalendarConfigDetailDO::getIsLatest, BusinessConstant.Y)
                .eq(CalendarConfigDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<CalendarConfigDetailDO> selectListByConfigIds(List<Long> calendarConfigIds) {
        if (CollectionUtils.isEmpty(calendarConfigIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CalendarConfigDetailDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CalendarConfigDetailDO::getAttendanceConfigId, calendarConfigIds)
                .eq(CalendarConfigDetailDO::getIsLatest, BusinessConstant.Y)
                .eq(CalendarConfigDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<CalendarConfigDetailDO> selectListByConfigIdsAndYear(List<Long> calendarConfigIds, List<Integer> years) {
        if (CollectionUtils.isEmpty(calendarConfigIds) || CollectionUtils.isEmpty(years)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CalendarConfigDetailDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CalendarConfigDetailDO::getAttendanceConfigId, calendarConfigIds)
                .in(CalendarConfigDetailDO::getYear, years)
                .eq(CalendarConfigDetailDO::getIsLatest, BusinessConstant.Y)
                .eq(CalendarConfigDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<CalendarConfigDetailDO> listByPage(int currentPage, int pageSize) {
        PageInfo<CalendarConfigDetailDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }
}
