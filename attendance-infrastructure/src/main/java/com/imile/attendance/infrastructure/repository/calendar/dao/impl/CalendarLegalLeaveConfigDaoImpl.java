package com.imile.attendance.infrastructure.repository.calendar.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarLegalLeaveConfigDao;
import com.imile.attendance.infrastructure.repository.calendar.mapper.CalendarLegalLeaveConfigMapper;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarLegalLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarLegalLeaveConfigQuery;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 日历法定假期配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-23
 */
@Slf4j
@Component
@DS(Constants.TableSchema.attendance)
@RequiredArgsConstructor
public class CalendarLegalLeaveConfigDaoImpl extends ServiceImpl<CalendarLegalLeaveConfigMapper, CalendarLegalLeaveConfigDO> implements CalendarLegalLeaveConfigDao {

    /**
     * 根据条件查询法定假期
     *
     * @param query 查询条件
     * @return 法定假期列表
     */
    @Override
    public List<CalendarLegalLeaveConfigDO> queryByCondition(CalendarLegalLeaveConfigQuery query) {
        LambdaQueryWrapper<CalendarLegalLeaveConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getLocationCountry()), CalendarLegalLeaveConfigDO::getLocationCountry, query.getLocationCountry());
        queryWrapper.in(CollUtil.isNotEmpty(query.getLocationCountryList()), CalendarLegalLeaveConfigDO::getLocationCountry, query.getLocationCountryList());
        if (ObjectUtil.isNotNull(query.getNextYear()) || ObjectUtil.isNotNull(query.getLastYear())) {
            // 如果下一年参数不为null，表示需要查询多年的数据，所以需要查询大于等于当前年份，小于等于下一年份的数据
            queryWrapper.ge(ObjectUtil.isNotNull(query.getLastYear()), CalendarLegalLeaveConfigDO::getYear, query.getLastYear());
            queryWrapper.le(ObjectUtil.isNotNull(query.getNextYear()), CalendarLegalLeaveConfigDO::getYear, query.getNextYear());
        } else {
            // 查询当前年份的数据
            queryWrapper.eq(ObjectUtil.isNotNull(query.getYear()), CalendarLegalLeaveConfigDO::getYear, query.getYear());
        }
        queryWrapper.in(CollUtil.isNotEmpty(query.getYearList()), CalendarLegalLeaveConfigDO::getYear, query.getYearList());
        queryWrapper.eq(ObjectUtil.isNotNull(query.getAttendanceConfigId()), CalendarLegalLeaveConfigDO::getAttendanceConfigId, query.getAttendanceConfigId());
        queryWrapper.like(ObjectUtil.isNotEmpty(query.getLegalLeaveName()), CalendarLegalLeaveConfigDO::getLegalLeaveName, query.getLegalLeaveName());
        queryWrapper.eq(CalendarLegalLeaveConfigDO::getIsDelete, BusinessConstant.N);
        queryWrapper.orderByAsc(CalendarLegalLeaveConfigDO::getLegalLeaveStartDayId);
        return list(queryWrapper);
    }

    @Override
    public List<CalendarLegalLeaveConfigDO> queryByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return null;
        }
        LambdaQueryWrapper<CalendarLegalLeaveConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CalendarLegalLeaveConfigDO::getId, ids);
        queryWrapper.orderByDesc(CalendarLegalLeaveConfigDO::getId);
        return list(queryWrapper);
    }

    @Override
    public List<CalendarLegalLeaveConfigDO> queryByConditionGroup(CalendarLegalLeaveConfigQuery query) {
        LambdaQueryWrapper<CalendarLegalLeaveConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getLocationCountry()), CalendarLegalLeaveConfigDO::getLocationCountry, query.getLocationCountry());
        queryWrapper.in(CollUtil.isNotEmpty(query.getLocationCountryList()), CalendarLegalLeaveConfigDO::getLocationCountry, query.getLocationCountryList());
        queryWrapper.eq(ObjectUtil.isNotNull(query.getYear()), CalendarLegalLeaveConfigDO::getYear, query.getYear());
        return this.getBaseMapper().selectCalendarLegalLeaveConfigList(query);
    }

    @Override
    public List<CalendarLegalLeaveConfigDO> listByPage(int currentPage, int pageSize) {
        PageInfo<CalendarLegalLeaveConfigDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }

    @Override
    public CalendarLegalLeaveConfigDO getByCalendarConfigIdAndDayId(Long calendarConfigId, Long dayId) {
        if (null == calendarConfigId || null == dayId) {
            return null;
        }
        LambdaQueryWrapper<CalendarLegalLeaveConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CalendarLegalLeaveConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(CalendarLegalLeaveConfigDO::getAttendanceConfigId, calendarConfigId)
                .le(CalendarLegalLeaveConfigDO::getLegalLeaveStartDayId, dayId)
                .ge(CalendarLegalLeaveConfigDO::getLegalLeaveEndDayId, dayId);
        List<CalendarLegalLeaveConfigDO> list = list(queryWrapper);
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    @Override
    public List<CalendarLegalLeaveConfigDO> selectListByConfigIdsAndYear(List<Long> calendarConfigIds, List<Integer> years) {
        if (CollectionUtils.isEmpty(calendarConfigIds) || CollectionUtils.isEmpty(years)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CalendarLegalLeaveConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CalendarLegalLeaveConfigDO::getAttendanceConfigId, calendarConfigIds)
                .in(CalendarLegalLeaveConfigDO::getYear, years)
                .eq(CalendarLegalLeaveConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }
}
