package com.imile.attendance.infrastructure.repository.calendar.dto;

import lombok.Data;

import java.util.Date;


/**
 * <AUTHOR>
 * @since 2025/5/10
 */
@Data
public class CalendarConfigModifyDTO {

    /**
     * 配置ID
     */
    private Long id;

    /**
     * 国家
     */
    private String country;

    /**
     * 日历名称
     */
    private String calendarName;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 创建人
     */
    private String createUserName;
}
