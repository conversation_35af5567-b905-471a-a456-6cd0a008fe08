package com.imile.attendance.infrastructure.repository.calendar.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * {@code @author:} allen
 * {@code @className:} CalendarConfigDetailMapper
 * {@code @since:} 2025-01-17 14:43
 * {@code @description:}
 */
@Mapper
@Repository
public interface CalendarConfigDetailMapper extends BaseMapper<CalendarConfigDetailDO> {

}