package com.imile.attendance.infrastructure.repository.calendar.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.calendar.dto.CalendarRangeCountDTO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigDateQuery;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigRangeQuery;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} CalendarConfigRangeMapper
 * {@code @since:} 2025-01-17 15:19
 * {@code @description:}
 */
@Mapper
@Repository
public interface CalendarConfigRangeMapper extends BaseMapper<CalendarConfigRangeDO> {

    /**
     * 查询指定条件的生效数据
     */
    List<CalendarConfigRangeDO> listActiveRecords(CalendarConfigRangeQuery query);

    /**
     * 查询指定条件的生效数据
     */
    List<CalendarConfigRangeDO> listAllRecords(CalendarConfigRangeQuery query);


    List<CalendarConfigRangeDO> selectConfigRangeByDate(CalendarConfigDateQuery query);

    List<CalendarRangeCountDTO> countCalendarRange(@Param("calendarConfigIds") List<Long> calendarConfigIds);


    List<UserInfoDO> listOnJobNoDriverUsersExcludeRangeConfigured(RuleRangeUserQuery ruleRangeUserQuery);

}