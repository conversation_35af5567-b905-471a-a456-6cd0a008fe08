package com.imile.attendance.infrastructure.repository.calendar.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import com.imile.attendance.enums.RangeTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * {@code @author:} allen
 * {@code @className:} CalendarConfigRangeDO
 * {@code @since:} 2025-01-17 15:19
 * {@code @description:}
 */
@ApiModel(description = "日历配置适用范围表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("calendar_config_range")
public class CalendarConfigRangeDO extends BaseDO {

    @ApiModelProperty(value = "")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 业务ID 部门id、用户ID
     */
    @ApiModelProperty(value = "业务ID 部门id、用户ID")
    private Long bizId;


    /**
     * 业务编码 部门编码、用户编码
     */
    @ApiModelProperty(value = "业务编码 部门编码、用户编码")
    private String bizCode;

    /**
     * 日历方案ID
     */
    @ApiModelProperty(value = "日历方案ID")
    private Long attendanceConfigId;

    /**
     * 日历方案编码
     */
    @ApiModelProperty(value = "日历方案编码")
    private String attendanceConfigNo;

    /**
     * 范围类型 DEPT,USER
     */
    @ApiModelProperty(value = "范围类型 DEPT,USER")
    private String rangeType;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date startDate;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endDate;

    /**
     * 是否为最新
     */
    @ApiModelProperty(value = "是否为最新")
    private Integer isLatest;

    /**
     * 备注说明
     */
    @ApiModelProperty(value = "备注说明")
    private String remark;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private BigDecimal orderby;


    public boolean areUserRangeType() {
        return StringUtils.equalsIgnoreCase(this.rangeType, RangeTypeEnum.USER.getCode());
    }
}