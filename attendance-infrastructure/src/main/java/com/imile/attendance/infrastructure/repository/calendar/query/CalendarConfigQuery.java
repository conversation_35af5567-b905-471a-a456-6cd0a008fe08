package com.imile.attendance.infrastructure.repository.calendar.query;

import com.imile.common.query.BaseQuery;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/21 
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CalendarConfigQuery extends BaseQuery {

    /**
     * 国家
     */
    private String country;

    /**
     * 配置编码批量查询
     */
    private Collection<String> nos;

    /**
     * 配置ID批量查询
     */
    private Collection<? extends Serializable> ids;

    /**
     * 配置类型
     */
    private String type;
    /**
     * 状态
     */
    private String status;

    /**
     * 是否默认
     */
    private Integer isDefault;

    /**
     * 国家列表
     */
    private List<String> countryList;


    /**
     * 考勤日历方案名称
     */
    private String attendanceConfigName;


    /**
     * 兼容旧逻辑
     */
    private List<Long> deptIdList;
    private List<Long> userIdList;


    /**
     * 后面使用部门，人员编码代替deptIdList,userIdList
     */
    private List<Long> deptCodeList;
    private List<Long> userCodeList;
}
