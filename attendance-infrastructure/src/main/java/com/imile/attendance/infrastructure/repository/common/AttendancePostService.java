package com.imile.attendance.infrastructure.repository.common;

import com.imile.attendance.hrms.RpcPostClient;
import com.imile.attendance.hrms.support.RpcPostClientSupport;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.mapstruct.CommonMapstruct;
import com.imile.attendance.infrastructure.repository.common.dto.AttendancePost;
import com.imile.common.enums.StatusEnum;
import com.imile.hrms.api.base.param.PostConditionParam;
import com.imile.hrms.api.base.result.PostDTO;
import com.imile.attendance.infrastructure.repository.common.dto.AttendancePost;
import com.imile.hrms.api.organization.dto.DeptDTO;
import com.imile.hrms.api.organization.query.DeptConditionParam;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24
 * @Description 岗位服务
 */
@Component
public class AttendancePostService {

    @Resource
    private RpcPostClient rpcPostClient;
    @Resource
    private RpcPostClientSupport rpcPostClientSupport;

    /**
     * 根据岗位ID列表查询岗位信息
     * 查询启用的岗位
     *
     * @param postIds 岗位ID列表
     * @return 岗位信息列表
     * 注意：当传入空列表时，将返回空集合
     */
    public List<AttendancePost> listByPostList(List<Long> postIds) {
        List<PostDTO> postList = rpcPostClientSupport.listByPostList(postIds, StatusEnum.ACTIVE.getCode());
        return CommonMapstruct.INSTANCE.mapToPost(postList);
    }

    /**
     * 根据岗位ID列表查询岗位信息
     * 不过滤岗位状态
     *
     * @param postIds 岗位ID列表
     * @return 岗位信息列表
     * 注意：当传入空列表时，将返回空集合
     */
    public List<AttendancePost> listAllStatusByPostList(List<Long> postIds) {
        List<PostDTO> postList = rpcPostClientSupport.listByPostList(postIds,null);
        return CommonMapstruct.INSTANCE.mapToPost(postList);
    }

    /**
     * 根据岗位ID获取岗位信息
     *
     * @param postId 岗位ID
     * @return 单个岗位信息
     * 注意：当传入null或未找到对应岗位时，将返回null
     */
    public AttendancePost getByPostId(Long postId) {
        PostDTO postDTO = rpcPostClient.getPostById(postId);
        return CommonMapstruct.INSTANCE.mapToPost(postDTO);
    }

    /**
     * 查询全量岗位信息
     *
     * @return 岗位信息列表
     * 注意：当传入空列表时，将返回空集合
     */
    public List<AttendancePost> listAllPostInfo() {
        PostConditionParam postConditionParam = new PostConditionParam();
        List<PostDTO> postList = rpcPostClient.listPostByCondition(postConditionParam);
        return CommonMapstruct.INSTANCE.mapToPost(postList);
    }
}
