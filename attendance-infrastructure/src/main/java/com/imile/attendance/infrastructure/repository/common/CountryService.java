package com.imile.attendance.infrastructure.repository.common;

import cn.hutool.core.util.ObjectUtil;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.hermes.RpcHermesCountryClient;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.hermes.support.RpcHermesCountrySupport;
import com.imile.attendance.util.CommonUtil;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hermes.business.query.CountryApiQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description 国家配置信息
 */
@Component
public class CountryService {

    @Resource
    private RpcHermesCountryClient rpcHermesCountryClient;
    @Resource
    private RpcHermesCountrySupport rpcHermesCountrySupport;


    /**
     * 拉取所有业务国家配置
     */
    public List<CountryDTO> listAllCountry() {
        return rpcHermesCountrySupport.listAllCountry();
    }

    /**
     * 拉取所有业务国家配置
     */
    public List<CountryConfigDTO> queryAllCountryConfigList() {
        return rpcHermesCountryClient.queryAllCountryConfigList();
    }


    /**
     * 根据国家编码拉取国家配置
     */
    public CountryDTO queryCountry(String countryCode) {
        return rpcHermesCountrySupport.queryCountry(countryCode);
    }

    /**
     * 根据国家编码列表拉取国家配置
     */
    public List<CountryDTO> queryCountryList(List<String> countryList){
        return rpcHermesCountrySupport.queryCountryList(countryList);
    }

    /**
     * 获取国家配置
     */
    public CountryConfigDTO queryCountryConfig(CountryApiQuery query) {
        return rpcHermesCountryClient.queryCountryConfig(query);
    }

    /**
     * 获取国家配置
     */
    public List<CountryConfigDTO> selectCountryConfigList(CountryApiQuery query) {
        return rpcHermesCountryClient.queryCountryConfigList(query);
    }

}
