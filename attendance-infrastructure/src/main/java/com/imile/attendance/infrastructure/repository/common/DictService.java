package com.imile.attendance.infrastructure.repository.common;

import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.hermes.RpcHermesDictClient;
import com.imile.attendance.hermes.dto.DictVO;
import com.imile.attendance.hermes.support.RpcHermesDictClientSupport;
import com.imile.hermes.resource.dto.DictDataDTO;
import com.imile.ucenter.api.context.RequestInfoHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Component
public class DictService {

    @Resource
    private RpcHermesDictClientSupport rpcHermesDictClientSupport;
    @Resource
    private RpcHermesDictClient rpcHermesDictClient;


    /**
     * 根据typeCode查询code->value
     *
     * @param typeCode type code
     * @return 映射
     */
    public Map<String, DictVO> getByTypeCode(String typeCode) {
        return rpcHermesDictClientSupport.getByTypeCode(typeCode);
    }

    /**
     * 更具typeCode查询code->value 集合
     *
     * @param typeCodes type code
     * @return 映射集合
     */
    public Map<String, Map<String, DictVO>> getByTypeCodes(List<String> typeCodes) {
        return rpcHermesDictClientSupport.getByTypeCodes(typeCodes);
    }

    /**
     * 中英文
     */
    public Map<String, Map<String, DictVO>> getAllLangByTypeCodes(List<String> typeCodes) {
        return rpcHermesDictClientSupport.getAllLangByTypeCodes(typeCodes);
    }

    public List<DictDataDTO> getByTypeCodeList(String typeCode) {
        return rpcHermesDictClientSupport.getByTypeCodeList(typeCode);
    }


    /**
     * 更具typeCode查询code->value 集合
     *
     * @param typeCodes type code
     * @return 映射集合
     */
    public Map<String, Map<String, DictVO>> getByTypeCodes(List<String> typeCodes, Long orgId) {
        return rpcHermesDictClientSupport.getByTypeCodes(typeCodes, orgId);
    }

    public List<DictDataDTO> getByTypeCodeList(String typeCode, Long orgId) {
        return rpcHermesDictClientSupport.getByTypeCodeList(typeCode, orgId);
    }

    /**
     * 根据typeCode查询code->value
     *
     * @param typeCode type code
     * @param orgId
     * @return 映射
     */
    public Map<String, DictVO> getByTypeCodeAndOrgId(String typeCode, Long orgId) {
        return rpcHermesDictClientSupport.getByTypeCodeAndOrgId(typeCode, orgId);
    }

    /**
     * 据typeCode查询code->value
     *
     * @param typeCode  type code
     * @param orgId     企业id
     * @param localeTxt 本地标识
     * @return 映射
     */
    public Map<String, DictVO> getByTypeCodeAndOrgIdAndLocale(String typeCode, Long orgId, String localeTxt) {
        return rpcHermesDictClientSupport.getByTypeCodeAndOrgIdAndLocale(typeCode, orgId, localeTxt);
    }

    /**
     * 获取字典编码与字典数据映射
     *
     * @param typeCodeList 字典编码列表
     * @return Map
     */
    public Map<String, List<DictDataDTO>> getDictCodeDataListMap(List<String> typeCodeList) {
        if (CollectionUtils.isEmpty(typeCodeList)) {
            return Collections.emptyMap();
        }
        return rpcHermesDictClient.findDictDataByTypeCodeAndLangMap(typeCodeList,
                BusinessConstant.DEFAULT_ORG_ID, RequestInfoHolder.getLocaleTxt());

    }
}
