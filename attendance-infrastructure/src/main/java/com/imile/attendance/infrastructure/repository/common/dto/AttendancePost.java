package com.imile.attendance.infrastructure.repository.common.dto;

import com.imile.ucenter.api.context.RequestInfoHolder;
import lombok.Data;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Data
public class AttendancePost {

    /**
     * 岗位id
     */
    private Long id;
    /**
     * 岗位名称（英文）
     */
    private String postNameEn;

    /**
     * 岗位名称（中文）hr返回目前的都是英文
     */
    private String postNameCn;

    /**
     * 状态
     */
    private String status;


    public String getLocalizeName() {
        return RequestInfoHolder.isChinese() ?
                this.postNameCn : this.postNameEn;
    }
}
