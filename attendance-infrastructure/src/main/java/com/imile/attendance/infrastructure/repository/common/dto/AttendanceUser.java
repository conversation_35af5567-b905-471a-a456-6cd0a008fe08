package com.imile.attendance.infrastructure.repository.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.common.enums.StatusEnum;
import com.imile.genesis.api.enums.WorkStatusEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/2/11 
 * @Description todo 去除考勤无需的字段
 */
@Data
public class AttendanceUser implements Serializable {

    private static final long serialVersionUID = 7936540465262717174L;

    private Long id;

    /**
     * 工号
     */
    private String workNo;

    /**
     * 用户编码/账号 Ucenter里面的用户编码，也是本系统中的账号
     */
    private String userCode;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 英文名
     */
    private String userNameEn;

    /**
     * 所属国家名称
     */
    private String countryName;

    /**
     * 所属国家编码
     */
    private String countryCode;

    /**
     * 性别 性别(1:男,2:女)
     */
    private Integer sex;

    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    /**
     * 员工性质 INTERNAL-内部员工,EXTERNAL-外部员工
     */
    private String employeeType;

    /**
     * 供应商id
     */
    private Long vendorId;
    /**
     * 供应商企业id
     */
    private Long vendorOrgId;
    /**
     * 供应商编码
     */
    private String vendorCode;
    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 工作岗位
     */
    private Long postId;

    /**
     * 职级id
     */
    private Long gradeId;

    /**
     * 职级数
     */
    private String gradeNo;

    /**
     * 职级等级
     */
    private String jobGrade;

    /**
     * 所属部门id
     */
    private Long deptId;

    /**
     * 所属网点id(对应hrms_ent_dept.id 并不是hermes的网点ID 未来废弃 新代码最好别再用这个字段)
     */
    @Deprecated
    private Long ocId;

    /**
     * 所属网点编码
     */
    private String ocCode;

    /**
     * 所属业务节点id
     */
    private Long bizModelId;

    /**
     * 项目ID（⚠️⚠️⚠️为兼容下游系统 暂时保留项目ID 记得赋值）
     */
    @Deprecated
    private Long projectId;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 结算主体编码（迁移至hrms_labor_contract_info.contract_company_code）
     */
    @Deprecated
    private String settlementCenterCode;

    /**
     * 汇报上级ID
     */
    private Long leaderId;

    /**
     * 汇报上级名称
     */
    private String leaderName;

    /**
     * 公司邮箱
     */
    private String email;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 企业微信手机号码
     */
    private String wechatPhone;

    /**
     * 区号
     */
    private Long countryCallingId;

    /**
     * 状态 状态(ACTIVE 生效,DISABLED)
     */
    private String status;

    /**
     * 工作状态 在职、离职等
     */
    private String workStatus;

    /**
     * 员工头像地址
     */
    private String profilePhotoUrl;

    /**
     * 是否为司机 是否为司机
     */
    private Integer isDriver;

    /**
     * 是否与UCenter已同步 是否与UCenter已同步,即UCenter当中是否有该用户的信息
     */
    private Integer isSync;

    /**
     * 数据来源 入职添加、数据导入、其他系统同步
     */
    private String dataSource;

    /**
     * 描述
     */
    private String remark;

    /**
     * 排序
     */
    private BigDecimal orderby;
    /**
     * 是否司机leader
     */
    private Integer isDtl;
    /**
     * 猎头公司
     */
    private Long recruitmentCompanyId;

    /**
     * 该项信息是否填写完毕
     */
    private Integer isFinish;

    /**
     * 账号冻结时间
     */
    private Date disabledDate;

    /**
     * 账号冻结原因
     */
    private String disabledReason;

    /**
     * 系统账号名称
     */
    private String sysAccountName;

    /**
     * 是否虚拟员工 0:否  1:是
     */
    private Integer isVirtual;

    /**
     * 职能
     */
    private String functional;
    /**
     * 是否仓内作业员工
     */
    private Integer isWarehouseStaff;

    /**
     * 车辆类别
     */
    private String vehicleModel;

    /**
     * 所属国
     */
    private String originCountry;

    /**
     * offer id
     */
    private Long recruitmentJobOfferId;

    /**
     * offer单据编码
     */
    private String recruitmentJobOfferApplicationCode;

    /**
     * hc id
     */
    private Long recruitmentJobHcId;


    /**
     * 是否多国发薪
     */
    private Integer isSalaryMultinational;

    /**
     * 是否薪资配置
     */
    private Integer isSalaryConfig;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 常驻地省份
     */
    private String locationProvince;

    /**
     * 常驻地城市
     */
    private String locationCity;

    /**
     * 是否全球派遣（0:否 1:是）
     */
    private Integer isGlobalRelocation;

    /**
     * 班次性质（FIXED_CLASS,MULTIPLE_CLASS）
     */
    private String classNature;


    public boolean areOnJob() {
        return StringUtils.equals(this.workStatus, WorkStatusEnum.ON_JOB.getCode());
    }

    public boolean areDimission() {
        return StringUtils.equals(this.workStatus, WorkStatusEnum.DIMISSION.getCode());
    }

    public boolean areActive() {
        return StringUtils.equals(this.status, StatusEnum.ACTIVE.getCode());
    }

    public boolean areActiveAndOnJob() {
        return areActive() && areOnJob();
    }

    public boolean areWareHouse() {
        return Objects.equals(isWarehouseStaff, BusinessConstant.Y);
    }

    public boolean areDriver() {
        return Objects.equals(isDriver, BusinessConstant.Y);
    }

    public String queryLocaleUserName(){
        return RequestInfoHolder.isChinese() ? this.userName : this.userNameEn;
    }

    public String getLocaleName() {
        return RequestInfoHolder.isChinese() ? this.userName : this.userNameEn;
    }


}
