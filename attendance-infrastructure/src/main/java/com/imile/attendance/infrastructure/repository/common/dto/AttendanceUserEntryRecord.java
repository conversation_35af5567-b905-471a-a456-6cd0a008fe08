package com.imile.attendance.infrastructure.repository.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Data
public class AttendanceUserEntryRecord {

    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 预计入职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entryDate;

    /**
     * 试用期月数
     * 默认为-1使用者注意
     */
    private Integer probationMonths;

    /**
     * 实际入职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date confirmDate;

    /**
     * 入职状态 入职状态：待发送邀请、待员工登记、待确认入职、已入职、已放弃入职
     */
    private String entryStatus;

    /**
     * 入职快照（JSON格式）
     */
    private String entrySnapshot;
}
