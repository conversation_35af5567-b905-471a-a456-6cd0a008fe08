package com.imile.attendance.infrastructure.repository.common.dto;

import com.imile.hrms.api.organization.dto.DeptOwnerDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/28 
 * @Description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserSuperiorDeptOwnerDTO {

    private Long userId;

    private String userCode;

    private String superiorDeptCode;

    private List<DeptOwnerDTO> superiorDeptOwnerList;
}
