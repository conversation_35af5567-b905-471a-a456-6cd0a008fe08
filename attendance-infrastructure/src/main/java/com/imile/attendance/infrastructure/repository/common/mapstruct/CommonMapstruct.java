package com.imile.attendance.infrastructure.repository.common.mapstruct;


import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendancePost;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.employee.modle.UserEntryRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserEntryRecordDO;
import com.imile.hrms.api.base.result.PostDTO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserInfoDO;
import com.imile.hrms.api.organization.dto.DeptDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/21 
 * @Description
 */
@Mapper
public interface CommonMapstruct {

    CommonMapstruct INSTANCE = Mappers.getMapper(CommonMapstruct.class);


    AttendanceUser mapToUser(UserInfoDO userInfoDO);

    List<AttendanceUser> mapToUser(List<UserInfoDO> userInfoDOList);


    AttendanceUser hrMapToUser(HrmsUserInfoDO hrmsUserInfoDO);

    List<AttendanceUser> hrMapToUser(List<HrmsUserInfoDO> hrmsUserInfoDOList);


    @Mapping(target = "vendorOrgId", ignore = true)
    @Mapping(target = "settleOrgId", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "region", ignore = true)
    @Mapping(target = "recentDisabledTime", ignore = true)
    @Mapping(target = "recentActiveTime", ignore = true)
    @Mapping(target = "province", ignore = true)
    @Mapping(target = "parentId", ignore = true)
    @Mapping(target = "orgId", ignore = true)
    @Mapping(target = "orderby", ignore = true)
    @Mapping(target = "ocType", ignore = true)
    @Mapping(target = "leaderName", ignore = true)
    @Mapping(target = "leaderCode", ignore = true)
    @Mapping(target = "deptShortName", ignore = true)
    @Mapping(target = "deptPosition", ignore = true)
    @Mapping(target = "deptPath", ignore = true)
    @Mapping(target = "deptDuty", ignore = true)
    @Mapping(target = "city", ignore = true)
    @Mapping(target = "bizModelId", ignore = true)
    @Mapping(target = "bizArea", ignore = true)
    @Mapping(target = "address", ignore = true)
    @Mapping(target = "deptType", source = "deptType")
    AttendanceDept mapToDept(DeptDTO deptDTO);

    List<AttendanceDept> mapToDept(List<DeptDTO> deptDTOList);



    @Mapping(target = "id", source = "postCode")
    @Mapping(target = "postNameEn", source = "postName")
    @Mapping(target = "postNameCn", source = "postName")
    AttendancePost mapToPost(PostDTO postDTO);

    List<AttendancePost> mapToPost(List<PostDTO> postDTOList);


    AttendanceUserEntryRecord mapToUserEntryRecord(UserEntryRecordDO userEntryRecordDO);

    List<AttendanceUserEntryRecord> mapToUserEntryRecord(List<UserEntryRecordDO> userEntryRecordDOList);

    AttendanceUserEntryRecord hrMapToUserEntryRecord(HrmsUserEntryRecordDO hrmsUserEntryRecordDO);

    List<AttendanceUserEntryRecord> hrMapToUserEntryRecord(List<HrmsUserEntryRecordDO> hrmsUserEntryRecordDOList);
}
