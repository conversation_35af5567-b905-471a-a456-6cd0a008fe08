package com.imile.attendance.infrastructure.repository.cycleConfig.model;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import com.imile.common.enums.StatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> chen
 * @Date 2025/2/24
 * @Description
 */
@ApiModel(description = "考勤周期配置表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("attendance_cycle_config")
public class AttendanceCycleConfigDO extends BaseDO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 常驻地国家
     */
    @ApiModelProperty(value = "常驻地国家")
    private String country;

    /**
     * 考勤周期类型：1:月，2:周,AttendanceCycleTypeEnum
     */
    @ApiModelProperty(value = "考勤周期类型：1:月，2:周")
    private Integer cycleType;

    /**
     * 周期开始
     */
    @ApiModelProperty(value = "周期开始")
    private String cycleStart;

    /**
     * 周期结束
     */
    @ApiModelProperty(value = "周期结束")
    private String cycleEnd;

    /**
     * 考勤异常过期设置，比如2，包含本周期，往前推2个周期
     */
    @ApiModelProperty(value = "考勤异常过期设置，比如2，包含本周期，往前推2个周期")
    private Integer abnormalExpired;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;


    public boolean areActive(){
        return ObjectUtil.equal(this.status, StatusEnum.ACTIVE.getCode());
    }
}
