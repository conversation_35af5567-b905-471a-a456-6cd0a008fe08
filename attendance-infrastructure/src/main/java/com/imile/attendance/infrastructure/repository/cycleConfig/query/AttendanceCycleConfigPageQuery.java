package com.imile.attendance.infrastructure.repository.cycleConfig.query;


import com.imile.attendance.query.ResourceQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/24
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AttendanceCycleConfigPageQuery extends ResourceQuery {

    /**
     * 国家
     */
    private String country;

    /**
     * 国家列表
     */
    private List<String> countryList;

    /**
     * 状态
     */
    private String status;
}
