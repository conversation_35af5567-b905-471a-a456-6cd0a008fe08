package com.imile.attendance.infrastructure.repository.cycleConfig.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/24
 * @Description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceCycleConfigQuery {

    /**
     * 国家
     */
    private String country;

    /**
     * 考勤周期类型：1:月，2:周，3:自定义,AttendanceCycleTypeEnum
     */
    private Integer cycleType;
}
