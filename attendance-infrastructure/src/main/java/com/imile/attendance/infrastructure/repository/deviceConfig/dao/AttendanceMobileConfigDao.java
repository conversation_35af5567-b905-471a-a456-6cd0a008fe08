package com.imile.attendance.infrastructure.repository.deviceConfig.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.deviceConfig.dto.AttendanceMobileConfigListDTO;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceMobileConfigDO;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceMobileConfigListQuery;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceMobileConfigQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/17
 * @Description
 */
public interface AttendanceMobileConfigDao extends IService<AttendanceMobileConfigDO> {

    /**
     * 考勤手机列表查询
     */
    List<AttendanceMobileConfigListDTO> queryAttendanceMobileConfig(AttendanceMobileConfigListQuery query);

    List<AttendanceMobileConfigDO> list(AttendanceMobileConfigQuery query);

    List<AttendanceMobileConfigDO> listByPage(int currentPage, int pageSize);

    /**
     * 用户考勤手机查询
     */
    List<AttendanceMobileConfigDO> queryAttendanceMobileConfigByUserCode(String userCode);

    /**
     * 用户考勤手机历史记录查询
     */
    List<AttendanceMobileConfigDO> queryHistoryMobileConfigByUserCode(String userCode);
}
