package com.imile.attendance.infrastructure.repository.deviceConfig.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceWifiConfigDO;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceConfigFilterQuery;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceWifiConfigQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/16
 * @Description
 */
public interface AttendanceWifiConfigDao extends IService<AttendanceWifiConfigDO> {

    /**
     * wifi列表查询
     */
    List<AttendanceWifiConfigDO> list(AttendanceWifiConfigQuery query);

    List<AttendanceWifiConfigDO> listByPage(int currentPage, int pageSize);

    /**
     * wifi筛选条件查询
     */
    List<String> selectFilterList(AttendanceConfigFilterQuery query);
}
