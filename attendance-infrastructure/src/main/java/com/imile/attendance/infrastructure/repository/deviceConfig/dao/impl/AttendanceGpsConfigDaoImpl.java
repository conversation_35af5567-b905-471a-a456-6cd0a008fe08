package com.imile.attendance.infrastructure.repository.deviceConfig.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.enums.WhetherEnum;
import com.imile.attendance.infrastructure.repository.deviceConfig.dao.AttendanceGpsConfigDao;
import com.imile.attendance.infrastructure.repository.deviceConfig.mapper.AttendanceGpsConfigMapper;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceGpsConfigDO;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceConfigFilterQuery;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceGpsConfigQuery;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/4/15
 * @Description
 */
@Component
@RequiredArgsConstructor
public class AttendanceGpsConfigDaoImpl extends ServiceImpl<AttendanceGpsConfigMapper, AttendanceGpsConfigDO> implements AttendanceGpsConfigDao {

    @Override
    public List<AttendanceGpsConfigDO> list(AttendanceGpsConfigQuery query) {
        LambdaQueryWrapper<AttendanceGpsConfigDO> queryWrapper = Wrappers.lambdaQuery();
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(query.getIds())) {
            queryWrapper.in(AttendanceGpsConfigDO::getId, query.getIds());
        }
        if (StringUtils.isNotBlank(query.getCountry())) {
            queryWrapper.eq(AttendanceGpsConfigDO::getCountry, query.getCountry());
        }
        if (CollectionUtils.isNotEmpty(query.getCountryList())) {
            queryWrapper.in(AttendanceGpsConfigDO::getCountry, query.getCountryList());
        }
        if (StringUtils.isNotBlank(query.getLocationCity())) {
            queryWrapper.eq(AttendanceGpsConfigDO::getLocationCity, query.getLocationCity());
        }
        if (StringUtils.isNotBlank(query.getAddressName())) {
            queryWrapper.like(AttendanceGpsConfigDO::getAddressName, query.getAddressName());
        }
        if (Objects.nonNull(query.getLongitude())) {
            queryWrapper.eq(AttendanceGpsConfigDO::getLongitude, query.getLongitude());
        }
        if (Objects.nonNull(query.getLatitude())) {
            queryWrapper.eq(AttendanceGpsConfigDO::getLatitude, query.getLatitude());
        }
        queryWrapper.eq(AttendanceGpsConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(AttendanceGpsConfigDO::getCreateDate);
        return list(queryWrapper);
    }

    @Override
    public List<AttendanceGpsConfigDO> listByPage(int currentPage, int pageSize) {
        PageInfo<AttendanceGpsConfigDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }

    @Override
    public List<String> selectFilterList(AttendanceConfigFilterQuery query) {
        // 查询国家
        if (WhetherEnum.NO.getKey().equals(query.getType())) {
            return this.baseMapper.queryGpsCountry();
        }
        return this.baseMapper.queryGpsCity(query.getCountry());
    }
}
