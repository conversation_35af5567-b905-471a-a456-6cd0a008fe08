package com.imile.attendance.infrastructure.repository.deviceConfig.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceGpsConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/15
 * @Description
 */
@Mapper
@Repository
public interface AttendanceGpsConfigMapper extends BaseMapper<AttendanceGpsConfigDO> {

    List<String> queryGpsCountry();

    List<String> queryGpsCity(String country);
}
