package com.imile.attendance.infrastructure.repository.deviceConfig.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.deviceConfig.dto.AttendanceMobileConfigListDTO;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceMobileConfigDO;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceMobileConfigListQuery;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/17
 * @Description
 */
@Mapper
@Repository
public interface AttendanceMobileConfigMapper extends BaseMapper<AttendanceMobileConfigDO> {

    List<AttendanceMobileConfigListDTO> queryAttendanceMobileConfig(AttendanceMobileConfigListQuery query);

}
