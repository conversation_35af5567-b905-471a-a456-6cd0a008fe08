package com.imile.attendance.infrastructure.repository.deviceConfig.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceWifiConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/16
 * @Description
 */
@Mapper
@Repository
public interface AttendanceWifiConfigMapper extends BaseMapper<AttendanceWifiConfigDO> {
    List<String> queryWifiCountry();

    List<String> queryWifiCity(String country);
}
