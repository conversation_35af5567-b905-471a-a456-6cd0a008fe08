package com.imile.attendance.infrastructure.repository.deviceConfig.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * GPS打卡办公地址配置表
 *
 * <AUTHOR>
 * @Date 2025/4/15
 * @Description
 */
@ApiModel(description = "GPS打卡办公地址配置表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("attendance_gps_config")
public class AttendanceGpsConfigDO extends BaseDO {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "城市")
    private String locationCity;

    @ApiModelProperty(value = "地址名称")
    private String addressName;

    @ApiModelProperty(value = "详细地址")
    private String addressDetail;

    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    @ApiModelProperty(value = "有效范围")
    private Integer effectiveRange;
}
