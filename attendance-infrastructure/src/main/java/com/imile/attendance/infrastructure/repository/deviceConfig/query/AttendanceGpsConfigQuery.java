package com.imile.attendance.infrastructure.repository.deviceConfig.query;


import com.imile.attendance.query.ResourceQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/15
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceGpsConfigQuery extends ResourceQuery {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 主键id
     */
    private List<Long> ids;

    /**
     * 国家
     */
    private String country;

    /**
     * 城市
     */
    private String locationCity;

    /**
     * 地址名称
     */
    private String addressName;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 国家列表
     */
    private List<String> countryList;
}
