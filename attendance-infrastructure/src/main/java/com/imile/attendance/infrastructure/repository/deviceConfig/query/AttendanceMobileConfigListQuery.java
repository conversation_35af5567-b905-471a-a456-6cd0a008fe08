package com.imile.attendance.infrastructure.repository.deviceConfig.query;


import com.imile.attendance.query.ResourceQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/18
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceMobileConfigListQuery extends ResourceQuery {

    /**
     * 用户账号或名称
     */
    private String userCodeOrName;

    /**
     * 国家
     */
    private String country;

    /**
     * 当前登录人编码
     */
    private String loginUserCode;

    /**
     * 国家列表
     */
    private List<String> countryList;
}
