package com.imile.attendance.infrastructure.repository.deviceConfig.query;


import com.imile.attendance.query.ResourceQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/4/18
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceMobileConfigQuery extends ResourceQuery {

    /**
     * 当前登录人编码
     */
    private String userCode;

    /**
     * 手机唯一标识
     */
    private String mobileUnicode;

    /**
     * 手机型号
     */
    private String mobileModel;

    /**
     * 手机品牌
     */
    private String mobileBranch;

    /**
     * 手机系统版本
     */
    private String mobileVersion;

    /**
     * 是否删除
     */
    private Integer isDelete;
}
