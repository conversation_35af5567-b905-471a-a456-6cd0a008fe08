package com.imile.attendance.infrastructure.repository.driver.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceDetailDTO;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceDetailMonthDTO;
import com.imile.attendance.infrastructure.repository.driver.model.DriverAttendanceDetailDO;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceDetailInfoQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceDetailMonthQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceDetailQuery;

import java.util.List;

/**
 * 司机考勤明细数据访问接口
 * 提供司机考勤数据的查询操作，包括日报查询、月报查询、条件查询等
 * 支持分页查询和复杂条件筛选
 *
 * <AUTHOR> chen
 * @Date 2025/3/6
 */
public interface DriverAttendanceDetailDao extends IService<DriverAttendanceDetailDO> {


    /**
     * 查询司机考勤日报数据
     * 根据查询条件获取司机的日常考勤记录，用于生成考勤日报
     * 支持按日期、部门、用户等条件筛选，返回包含用户基本信息的考勤数据
     *
     * @param query 查询条件，包含日期、部门、用户筛选等参数
     * @return 司机考勤明细列表，包含用户信息和考勤状态
     */
    List<DriverAttendanceDetailDTO> queryDriverAttendance(DriverAttendanceDetailQuery query);

    /**
     * 根据条件查询司机考勤明细数据
     * 支持按时间范围、用户列表、考勤类型等多种条件组合查询
     * 主要用于月报统计、数据导出等场景
     *
     * @param driverAttendanceDetailQuery 查询条件，包含时间范围、用户列表、考勤类型等
     * @return 司机考勤明细DO对象列表，包含完整的考勤记录信息
     */
    List<DriverAttendanceDetailDO> queryDriverAttendanceByCondition(DriverAttendanceDetailInfoQuery driverAttendanceDetailQuery);


    /**
     * 查询司机月度考勤统计数据
     * 获取指定时间范围内的司机基本信息，用于月报生成和统计分析
     * 返回的数据包含用户基本信息，但不包含具体的考勤明细，需要配合其他方法获取完整数据
     *
     * @param query 月度查询条件，包含时间范围、部门、用户筛选等参数
     * @return 司机月度考勤基础数据列表，包含用户基本信息
     */
    List<DriverAttendanceDetailMonthDTO> queryDriverMonthAttendance(DriverAttendanceDetailMonthQuery query);


    /**
     * 分页查询司机考勤明细数据
     *
     * @param currentPage 当前页码，从1开始
     * @param pageSize 每页记录数
     * @return 司机考勤明细DO对象列表
     */
    List<DriverAttendanceDetailDO> listByPage(int currentPage, int pageSize);



}

