package com.imile.attendance.infrastructure.repository.driver.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.driver.model.DriverAttendanceOperateRecordDO;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceOperateRecordQuery;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/6
 * @Description
 */
public interface DriverAttendanceOperateRecordDao extends IService<DriverAttendanceOperateRecordDO> {

    List<DriverAttendanceOperateRecordDO> listAttendanceOperateRecordDetail(DriverAttendanceOperateRecordQuery query);

    List<DriverAttendanceOperateRecordDO> listByPage(int currentPage, int pageSize);


}

