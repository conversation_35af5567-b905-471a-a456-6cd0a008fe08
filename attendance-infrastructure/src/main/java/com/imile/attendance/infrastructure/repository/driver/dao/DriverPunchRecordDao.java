package com.imile.attendance.infrastructure.repository.driver.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.driver.model.DriverPunchRecordDO;
import com.imile.attendance.infrastructure.repository.driver.query.DriverPunchRecordDetailQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverPunchRecordQuery;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/6
 * @Description
 */
public interface DriverPunchRecordDao extends IService<DriverPunchRecordDO> {

    /**
     * 根据司机账号、day_id、操作类型查询
     */
    List<DriverPunchRecordDO> listByUserCodeAndDayIdAndOperationType(DriverPunchRecordQuery query);

    List<DriverPunchRecordDO> listPunchRecordDetail(DriverPunchRecordDetailQuery query);

    List<DriverPunchRecordDO> listByPage(int currentPage, int pageSize);


}

