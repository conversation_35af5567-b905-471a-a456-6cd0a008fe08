package com.imile.attendance.infrastructure.repository.driver.dao.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.infrastructure.repository.driver.dao.DriverAttendanceDetailDao;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceDetailDTO;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceDetailMonthDTO;
import com.imile.attendance.infrastructure.repository.driver.mapper.DriverAttendanceDetailMapper;
import com.imile.attendance.infrastructure.repository.driver.model.DriverAttendanceDetailDO;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceDetailInfoQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceDetailMonthQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceDetailQuery;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/6
 * @Description
 */
@Component
@RequiredArgsConstructor
public class DriverAttendanceDetailDaoImpl extends ServiceImpl<DriverAttendanceDetailMapper, DriverAttendanceDetailDO>
        implements DriverAttendanceDetailDao {

    @Resource
    private final DriverAttendanceDetailMapper driverAttendanceDetailMapper;


    @Override
    public List<DriverAttendanceDetailDTO> queryDriverAttendance(DriverAttendanceDetailQuery query) {
        if(ObjectUtil.isNull(query)){
            return Collections.emptyList();
        }
        return driverAttendanceDetailMapper.queryDriverAttendance(query);
    }

    @Override
    public List<DriverAttendanceDetailDO> queryDriverAttendanceByCondition(DriverAttendanceDetailInfoQuery query) {
        if(ObjectUtil.isNull(query)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<DriverAttendanceDetailDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ObjectUtil.isNotNull(query.getYear()), DriverAttendanceDetailDO::getYear, query.getYear());
        queryWrapper.eq(ObjectUtil.isNotNull(query.getDayId()), DriverAttendanceDetailDO::getDayId, query.getDayId());
        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getUserCode()), DriverAttendanceDetailDO::getUserCode, query.getUserCode());
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getUserCodeList()), DriverAttendanceDetailDO::getUserCode, query.getUserCodeList());
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getAttendanceTypeList()), DriverAttendanceDetailDO::getAttendanceType, query.getAttendanceTypeList());
        if (ObjectUtil.isNotNull(query.getMonthStartDate()) && ObjectUtil.isNotNull(query.getMonthEndDate())) {
            queryWrapper.ge(DriverAttendanceDetailDO::getDayId, query.getMonthStartDate());
            queryWrapper.le(DriverAttendanceDetailDO::getDayId, query.getMonthEndDate());
        }
        queryWrapper.eq(DriverAttendanceDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<DriverAttendanceDetailMonthDTO> queryDriverMonthAttendance(DriverAttendanceDetailMonthQuery query) {
        if(ObjectUtil.isNull(query)){
            return Collections.emptyList();
        }
        return driverAttendanceDetailMapper.queryDriverMonthAttendance(query);
    }

    @Override
    public List<DriverAttendanceDetailDO> listByPage(int currentPage, int pageSize) {
        PageInfo<DriverAttendanceDetailDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }
}

