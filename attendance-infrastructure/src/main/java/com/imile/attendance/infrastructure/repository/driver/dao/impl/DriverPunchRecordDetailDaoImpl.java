package com.imile.attendance.infrastructure.repository.driver.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.infrastructure.repository.driver.dao.DriverPunchRecordDetailDao;
import com.imile.attendance.infrastructure.repository.driver.mapper.DriverPunchRecordDetailMapper;
import com.imile.attendance.infrastructure.repository.driver.model.DriverPunchRecordDetailDO;
import com.imile.attendance.infrastructure.repository.driver.query.DriverPunchRecordDetailInfoQuery;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/6
 * @Description
 */
@Component
@RequiredArgsConstructor
public class DriverPunchRecordDetailDaoImpl extends ServiceImpl<DriverPunchRecordDetailMapper, DriverPunchRecordDetailDO>
        implements DriverPunchRecordDetailDao {

    @Override
    public List<DriverPunchRecordDetailDO> queryDriverPunchRecordDetailByCondition(DriverPunchRecordDetailInfoQuery detailInfoQuery) {
        if (ObjectUtil.isNull(detailInfoQuery)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<DriverPunchRecordDetailDO> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(DriverPunchRecordDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());

        lambdaQuery.eq(ObjectUtil.isNotEmpty(detailInfoQuery.getOperationType()), DriverPunchRecordDetailDO::getOperationType, detailInfoQuery.getOperationType());
        lambdaQuery.in(CollUtil.isNotEmpty(detailInfoQuery.getUserCodeList()), DriverPunchRecordDetailDO::getUserCode, detailInfoQuery.getUserCodeList());
        lambdaQuery.ge(ObjectUtil.isNotNull(detailInfoQuery.getStartDayId()), DriverPunchRecordDetailDO::getDayId, detailInfoQuery.getStartDayId());
        lambdaQuery.le(ObjectUtil.isNotNull(detailInfoQuery.getEndDayId()), DriverPunchRecordDetailDO::getDayId, detailInfoQuery.getEndDayId());

        lambdaQuery.eq(ObjectUtil.isNotEmpty(detailInfoQuery.getUserCode()), DriverPunchRecordDetailDO::getUserCode, detailInfoQuery.getUserCode());
        lambdaQuery.eq(ObjectUtil.isNotNull(detailInfoQuery.getDayId()), DriverPunchRecordDetailDO::getDayId, detailInfoQuery.getDayId());
        lambdaQuery.in(CollUtil.isNotEmpty(detailInfoQuery.getPunchRecordIdList()), DriverPunchRecordDetailDO::getPunchRecordId, detailInfoQuery.getPunchRecordIdList());

        return list(lambdaQuery);
    }

    @Override
    public List<DriverPunchRecordDetailDO> listByPage(int currentPage, int pageSize) {
        PageInfo<DriverPunchRecordDetailDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }
}

