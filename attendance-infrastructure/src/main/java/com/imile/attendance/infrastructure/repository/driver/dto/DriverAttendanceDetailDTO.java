package com.imile.attendance.infrastructure.repository.driver.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * {@code @author:} allen
 * {@code @className:} DriverAttendanceDetailDTO
 * {@code @since:} 2024-01-22 16:49
 * {@code @description:}
 */
@Data
public class DriverAttendanceDetailDTO implements Serializable {

    private static final long serialVersionUID = -5215716171903133521L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 主键ID
     */
    private Long userId;

    /**
     * 用户编码/账号 Ucenter里面的用户编码，也是本系统中的账号
     */
    private String userCode;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 英文名
     */
    private String userNameEn;

    /**
     * 工作状态 在职、离职等
     */
    private String workStatus;

    /**
     * 状态 状态(ACTIVE 生效,DISABLED)
     */
    private String status;


    /**
     * 出勤类型: 1：P 出勤，2：A 缺勤， 3：L 请假 ...
     */
    private Integer attendanceType;

    /**
     * 出勤类型: 1：P 出勤，2：A 缺勤， 3：L 请假 ...
     */
    private String attendanceTypeString;

    /**
     * 轨迹打卡次数
     */
    private Long locusNumber;

    /**
     * 司机签收次数
     */
    private Long dldNumber;

    /**
     * 常驻国家
     */
    private String locationCountry;

    /**
     * 供应商编码
     */
    private String vendorCode;
    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 所属部门id
     */
    private Long deptId;

    /**
     * 所属网点code
     */
    private String ocCode;

    /**
     * 所属网点类型
     */
    private String ocType;

    /**
     * 所属部门名称
     */
    private String deptName;

    /**
     * 工作岗位id
     */
    private Long postId;

    /**
     * 工作岗位名称
     */
    private String postName;

//    /**
//     * 汇报上级编码
//     */
//    private Long leaderId;
//
//    /**
//     * 汇报上级名称
//     */
//    private String leaderName;


    /**
     * 员工性质
     */
    private String employeeType;

    /**
     * 员工性质名称
     */
    private String employeeTypeDesc;

    /**
     * 入职日期
     */
    private Date entryDate;

    /**
     * 离职日期
     */
    private Date actualDimissionDate;

    /**
     * 最近操作时间：取司机打卡记录表某人最近的操作时间
     */
    private Date lastOperatingTime;

    /**
     * 最近操作时间：取司机打卡记录表某人最近的操作时间 【转换为本地】
     */
    private Date localLastOperatingTime;

    /**
     * day_id 示例：20240124
     */
    private Long dayId;

    /**
     * 年
     */
    private Integer year;

    /**
     * 月份
     */
    private Integer month;

    /**
     * 日
     */
    private Integer day;

    /**
     * 日期
     */
    private Date date;

}
