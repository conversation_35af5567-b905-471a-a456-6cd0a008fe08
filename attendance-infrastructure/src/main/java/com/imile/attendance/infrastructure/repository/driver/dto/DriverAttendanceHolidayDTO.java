package com.imile.attendance.infrastructure.repository.driver.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * {@code @author:} allen
 * {@code @className:} dasda
 * {@code @since:} 2024-01-25 11:10
 * {@code @description:}
 */
@Data
public class DriverAttendanceHolidayDTO implements Serializable {

    private static final long serialVersionUID = 862722844487191949L;
    /**
     * 前端title
     */
    private String title;

    /**
     * 前端显示的值
     */
    private String value;

}
