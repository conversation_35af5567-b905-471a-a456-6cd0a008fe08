package com.imile.attendance.infrastructure.repository.driver.dto;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * {@code @author:} allen
 * {@code @className:} DriverPunchRecordDTO
 * {@code @since:} 2024-01-22 10:45
 * {@code @description:}
 */
public class DriverPunchRecordDTO implements Serializable {

    private static final long serialVersionUID = 8014602585564149223L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 司机账号
     */
    private String userCode;

    /**
     * day_id 示例：20240124
     */
    private Long dayId;

    /**
     * 数据来源：1：TMS 2：司机App 3：考勤系统
     */
    private Integer sourceType;

    /**
     * 操作类型：1：DLD签收 2：轨迹打卡 3：请假 4：修改考勤 ...
     */
    private Integer operationType;

    /**
     * 操作内容：DLD签收、轨迹打卡、假期类型，天数（d），单据编号 、被修改日期：2024-01-02 修改内容："p"改成"L"
     */
    private String operationContent;

    /**
     * 操作时间
     */
    private LocalDateTime operatingTime;

    /**
     * 记录数量：轨迹打卡次数、司机签收次数
     */
    private Long number;

    /**
     * 申请单id
     */
    private Long formId;

    private Date createDate;

    private String createUserCode;

    private String createUserName;

    private Date lastUpdDate;

    private String lastUpdUserCode;

    private String lastUpdUserName;

    /**
     * 是否删除
     */
    private Integer isDelete;


    /**
     * 版本号
     */
    private Long recordVersion;
}
