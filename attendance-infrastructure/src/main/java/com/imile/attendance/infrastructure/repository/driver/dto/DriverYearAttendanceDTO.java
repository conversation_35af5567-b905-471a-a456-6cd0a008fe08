package com.imile.attendance.infrastructure.repository.driver.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} DriverYearAttendanceDTO
 * {@code @since:} 2024-01-24 14:57
 * {@code @description:}
 */
@Data
public class DriverYearAttendanceDTO implements Serializable {
    /**
     * 年
     */
    private Long year;

    /**
     * 司机月考勤
     */
    private List<DriverMonthAttendanceDTO> driverMonthAttendanceList;
}
