package com.imile.attendance.infrastructure.repository.driver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceDetailDTO;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceDetailMonthDTO;
import com.imile.attendance.infrastructure.repository.driver.model.DriverAttendanceDetailDO;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceDetailMonthQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceDetailQuery;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/6
 * @Description
 */
@Mapper
@Repository
public interface DriverAttendanceDetailMapper extends BaseMapper<DriverAttendanceDetailDO> {


    List<DriverAttendanceDetailDTO> queryDriverAttendance(DriverAttendanceDetailQuery query);

    List<DriverAttendanceDetailMonthDTO> queryDriverMonthAttendance(DriverAttendanceDetailMonthQuery query);

}

