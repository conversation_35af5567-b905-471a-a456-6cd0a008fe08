package com.imile.attendance.infrastructure.repository.driver.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR> chen
 * @Date 2025/3/6
 * @Description
 */
@ApiModel(description = "打卡记录详情表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("driver_punch_record_detail")
public class DriverPunchRecordDetailDO extends BaseDO {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 打卡记录ID
     */
    @ApiModelProperty(value = "打卡记录ID")
    private Long punchRecordId;

    /**
     * 司机账号
     */
    @ApiModelProperty(value = "司机账号")
    private String userCode;

    /**
     * day_id 示例：20240124
     */
    @ApiModelProperty(value = "day_id 示例：20240124")
    private Long dayId;

    /**
     * 数据来源：1：TMS 2：司机App 3：考勤系统
     */
    @ApiModelProperty(value = "数据来源：1：TMS 2：司机App 3：考勤系统")
    private Integer sourceType;

    /**
     * 操作类型：1：DLD签收 2：轨迹打卡 3：请假 4：修改考勤 ...
     */
    @ApiModelProperty(value = "操作类型：1：DLD签收 2：轨迹打卡 3：请假 4：修改考勤 ...")
    private Integer operationType;

    /**
     * 假期类型
     */
    @ApiModelProperty(value = "假期类型")
    private String leaveType;

    /**
     * 请假分钟
     */
    @ApiModelProperty(value = "请假分钟")
    private BigDecimal leaveMinutes;

}

