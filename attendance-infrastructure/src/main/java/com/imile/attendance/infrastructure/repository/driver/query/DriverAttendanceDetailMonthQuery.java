package com.imile.attendance.infrastructure.repository.driver.query;

import com.imile.attendance.query.ResourceQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} DriverAttendanceDetailMonthQuery
 * {@code @since:} 2024-01-24 21:14
 * {@code @description:}
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DriverAttendanceDetailMonthQuery extends ResourceQuery implements Serializable {

    private static final long serialVersionUID = -6887556705348816476L;
    /**
     * 国家
     */
    private String country;

    /**
     * startDayId 示例：20240124
     * 司机月报查询条件
     */
    private Long monthStartDayId;

    /**
     * endDayId 示例：20240124
     * 司机月报查询条件
     */
    private Long monthEndDayId;

    /**
     * startDate 示例：2024-01-24
     * 司机月报查询条件
     */
    private Date monthStartDate;

    /**
     * endDate 示例：2024-01-24
     * 司机月报查询条件
     */
    private Date monthEndDate;


    /**
     * startDate 示例：2024-01-24 00:00:00 用于mybaits 查询sql判断
     * 司机月报查询条件
     */
    private String monthStartDateString;

    /**
     * endDate 示例：2024-01-24 23:59:59 用于mybaits 查询sql判断
     * 司机月报查询条件
     */
    private String monthEndDateString;

    /**
     * 出勤类型: 1：P 出勤，2：A 缺勤， 3：L 请假 ...
     */
    private List<Integer> attendanceTypeList;

    /**
     * 用工类型
     */
    private List<String> employeeTypeList;

    /**
     * 账号状态
     */
    private String status;

    /**
     * 工作状态
     */
    private String workStatus;

    /**
     * 工作状态集合
     */
    private List<String> workStatusList;

    /**
     * 供应商code
     */
    private String vendorCode;

    /**
     * 账号/姓名,无用
     */
    @Deprecated
    private String userCodeAndName;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 部门id 集合
     */
    private List<Long> deptIdList;


    /**
     * 汇报上级
     */
    private Long leaderId;



    /**
     * 部门id
     */
    private List<Long> deptList;

    /**
     * 国家列表
     */
    private List<String> countryList;

    /**
     * 是否司机
     */
    private Integer isDriver;

    /**
     * 账号/姓名
     */
    private String userCodeOrName;

    /**
     * 用户code集合
     */
    private List<String> userCodeList;
}

