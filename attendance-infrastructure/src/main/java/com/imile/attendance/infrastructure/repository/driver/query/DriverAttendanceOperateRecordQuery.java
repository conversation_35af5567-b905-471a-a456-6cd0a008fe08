package com.imile.attendance.infrastructure.repository.driver.query;

import com.imile.attendance.query.ResourceQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * {@code @author:} allen
 * {@code @className:} DriverAttendanceOperateRecordQuery
 * {@code @since:} 2024-01-22 14:14
 * {@code @description:}
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DriverAttendanceOperateRecordQuery extends ResourceQuery implements Serializable {
    private static final long serialVersionUID = 3623868116132461554L;

    /**
     * 国家
     */
    private String country;

    /**
     * 查询起始时间
     */
    private Date startTime;


    /**
     * 查询结束时间
     */
    private Date endTime;
}
