package com.imile.attendance.infrastructure.repository.driver.query;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} DriverPunchRecordDetailInfoQuery
 * {@code @since:} 2024-01-25 14:26
 * {@code @description:}
 */
@Data
@Builder
public class DriverPunchRecordDetailInfoQuery implements Serializable {


    /**
     * 司机账号集合
     */
    private List<Long> punchRecordIdList;

    /**
     * 司机账号
     */
    private String userCode;

    /**
     * 司机账号集合
     */
    private List<String> userCodeList;

    /**
     * day_id 示例：20240124
     */
    private Long dayId;

    /**
     * day_id 示例：20240124
     */
    private Long startDayId;

    /**
     * day_id 示例：20240124
     */
    private Long endDayId;

    /**
     * 操作类型：1：DLD签收 2：轨迹打卡 3：请假 4：修改考勤 ...
     * 目前只使用了3.请假
     */
    private Integer operationType;

}
