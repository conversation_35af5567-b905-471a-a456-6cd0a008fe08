package com.imile.attendance.infrastructure.repository.driver.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/3/6
 * @Description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DriverPunchRecordQuery {

    /**
     * 司机账号
     */
    private String userCode;

    /**
     * day_id 示例：20240124
     */
    private Long dayId;

    /**
     * 数据来源：1：TMS 2：司机App 3：考勤系统
     */
    private Integer sourceType;

    /**
     * 操作类型：1：DLD签收 2：轨迹打卡 3：请假 4：修改考勤 ...
     */
    private Integer operationType;


    /**
     * 操作时间
     */
    private Date operatingTime;

    /**
     * 申请单id
     */
    private Long formId;
}
