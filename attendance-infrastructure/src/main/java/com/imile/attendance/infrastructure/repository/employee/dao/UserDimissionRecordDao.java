package com.imile.attendance.infrastructure.repository.employee.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.employee.modle.UserDimissionRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserEntryRecordDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/1/20 
 * @Description
 */
public interface UserDimissionRecordDao extends IService<UserDimissionRecordDO> {

    /**
     * 根据用户id和离职状态查询离职记录
     */
    UserDimissionRecordDO getByUserId(Long userId, String dimissionStatus);

    /**
     * 查询用户离职记录
     */
    List<UserDimissionRecordDO> listByUserId(Long userId);

    List<UserDimissionRecordDO> listByUserIds(List<Long> userIds);

    List<UserDimissionRecordDO> listByPage(int currentPage, int pageSize);

}
