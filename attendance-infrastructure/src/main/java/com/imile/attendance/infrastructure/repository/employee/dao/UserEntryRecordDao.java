package com.imile.attendance.infrastructure.repository.employee.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.employee.dto.UserEntryInfoDTO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserEntryRecordDO;
import com.imile.attendance.query.PageDTO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/1/20
 * @Description
 */
public interface UserEntryRecordDao extends IService<UserEntryRecordDO> {

    UserEntryRecordDO getById(Long userId);

    /**
     * 查询用户入职记录
     */
    List<UserEntryRecordDO> listByUserIds(List<Long> userIds);

    List<UserEntryRecordDO> listByPage(int currentPage, int pageSize);

    /**
     * 根据国家和入职确认时间查找用户
     *
     * @param locationCountry 国家代码，如'CHN'
     * @param confirmDate 入职确认时间的起始日期
     * @return 符合条件的用户列表
     */
    List<UserEntryInfoDTO> findUsersByCountryAndConfirmDate(String locationCountry, Date confirmDate);

    /**
     * 根据国家和入职确认时间分页查找用户
     *
     * @param locationCountry 国家代码，如'CHN'
     * @param confirmDate 入职确认时间的起始日期
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    PageDTO<UserEntryInfoDTO> findUsersByCountryAndConfirmDateWithPage(String locationCountry, Date confirmDate,
                                                                       int currentPage, int pageSize);
}
