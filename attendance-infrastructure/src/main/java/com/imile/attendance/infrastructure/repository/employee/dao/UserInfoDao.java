package com.imile.attendance.infrastructure.repository.employee.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.employee.dto.UserArchiveDTO;
import com.imile.attendance.infrastructure.repository.employee.dto.UserDTO;
import com.imile.attendance.infrastructure.repository.employee.dto.UserInformationDTO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.DriverQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserArchiveQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveQuery;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/1/20
 * @Description
 */
public interface UserInfoDao extends IService<UserInfoDO> {

    UserDTO getUserByCode(String userCode);

    List<UserDTO> getUserByCodes(List<String> userCodeList);

    UserInfoDO getByUserCode(String userCode);

    List<UserInfoDO> userList(UserDaoQuery query);

    List<UserInfoDO> listByUserCodes(List<String> userCodes);

    List<UserInfoDO> listByLocationCountrys(List<String> locationCountryList);

    /**
     * 根据用户id获取用户的部门等属性(部门，岗位在rpc获取)
     */
    UserInformationDTO getUserInfoInformation(Long userId);

    /**
     * 查询司机接口
     *
     * @param driverQuery 查询条件
     * @return List<HrmsUserInfoDO>
     */
    List<UserInfoDO> queryDriver(DriverQuery driverQuery);

    List<UserInfoDO> selectUserForZkteco(List<Long> deptIdList, List<Long> userIdList);


    UserInfoDO getByUserId(Long userId);

    List<UserInfoDO> getByUserIds(List<Long> userIdList);


    List<UserInfoDO> listByPage(int currentPage, int pageSize);

    List<UserInfoDO> selectByAssociateCondition(UserDaoQuery query);

    /**
     * 员工考勤档案
     */
    List<UserArchiveDTO> userAttendanceArchive(UserArchiveQuery query);

    /**
     * 假期相关用户查询
     */
    List<UserInfoDO> selectLeaveUser(UserLeaveQuery query);

    /**
     * 查询用户常驻国集合
     *
     * @return
     */
    List<String> selectUserLocationCountry();

}
