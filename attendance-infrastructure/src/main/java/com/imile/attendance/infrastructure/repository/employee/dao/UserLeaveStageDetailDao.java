package com.imile.attendance.infrastructure.repository.employee.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveStageDetailQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
public interface UserLeaveStageDetailDao extends IService<UserLeaveStageDetailDO> {

    /**
     * 根据用户详情表id查询余额信息
     *
     * @param leaveIdList
     * @return
     */
    List<UserLeaveStageDetailDO> selectByLeaveId(List<Long> leaveIdList);

    /**
     * 根据id查询余额西悉尼
     */
    List<UserLeaveStageDetailDO> selectById(List<Long> idList);

    /**
     * 通过条件查询
     *
     * @param userLeaveStageDetailQuery 查询条件
     * @return 查询结果
     */
    List<UserLeaveStageDetailDO> selectByCondition(UserLeaveStageDetailQuery userLeaveStageDetailQuery);
}
