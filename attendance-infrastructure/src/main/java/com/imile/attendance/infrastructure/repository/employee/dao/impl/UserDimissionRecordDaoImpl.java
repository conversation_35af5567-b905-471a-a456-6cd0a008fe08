package com.imile.attendance.infrastructure.repository.employee.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.infrastructure.repository.employee.dao.UserDimissionRecordDao;
import com.imile.attendance.infrastructure.repository.employee.mapper.UserDimissionRecordMapper;
import com.imile.attendance.infrastructure.repository.employee.modle.UserDimissionRecordDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/1/20 
 * @Description
 */
@Component
@RequiredArgsConstructor
public class UserDimissionRecordDaoImpl extends ServiceImpl<UserDimissionRecordMapper, UserDimissionRecordDO> implements UserDimissionRecordDao {

    @Override
    public UserDimissionRecordDO getByUserId(Long userId, String dimissionStatus) {
        if (userId == null) {
            return null;
        }
        LambdaQueryWrapper<UserDimissionRecordDO> queryWrapper = Wrappers.lambdaQuery(UserDimissionRecordDO.class);
        queryWrapper.eq(UserDimissionRecordDO::getUserId, userId)
                .eq(UserDimissionRecordDO::getDimissionStatus, dimissionStatus)
                .eq(UserDimissionRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.getOne(queryWrapper);
    }

    @Override
    public List<UserDimissionRecordDO> listByUserId(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserDimissionRecordDO> queryWrapper = Wrappers.lambdaQuery(UserDimissionRecordDO.class);
        queryWrapper.eq(UserDimissionRecordDO::getUserId, userId)
                .eq(UserDimissionRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<UserDimissionRecordDO> listByUserIds(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserDimissionRecordDO> queryWrapper = Wrappers.lambdaQuery(UserDimissionRecordDO.class);
        queryWrapper.in(UserDimissionRecordDO::getUserId, userIds)
                .eq(UserDimissionRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<UserDimissionRecordDO> listByPage(int currentPage, int pageSize) {
        PageInfo<UserDimissionRecordDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }
}
