package com.imile.attendance.infrastructure.repository.employee.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.infrastructure.repository.employee.dao.UserEntryRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dto.UserEntryInfoDTO;
import com.imile.attendance.infrastructure.repository.employee.mapper.UserEntryRecordMapper;
import com.imile.attendance.infrastructure.repository.employee.modle.UserEntryRecordDO;
import com.imile.attendance.query.PageDTO;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.page.Pagination;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/1/20 
 * @Description
 */
@Component
@RequiredArgsConstructor
public class UserEntryRecordDaoImpl extends ServiceImpl<UserEntryRecordMapper, UserEntryRecordDO> implements UserEntryRecordDao {

    @Override
    public UserEntryRecordDO getById(Long userId) {
        LambdaQueryWrapper<UserEntryRecordDO> queryWrapper = Wrappers.lambdaQuery(UserEntryRecordDO.class);
        queryWrapper.eq(UserEntryRecordDO::getUserId, userId);
        queryWrapper.eq(UserEntryRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.getOne(queryWrapper);
    }

    @Override
    public List<UserEntryRecordDO> listByUserIds(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserEntryRecordDO> queryWrapper = Wrappers.lambdaQuery(UserEntryRecordDO.class);
        queryWrapper.eq(UserEntryRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(UserEntryRecordDO::getUserId, userIds);
        return this.list(queryWrapper);
    }

    @Override
    public List<UserEntryRecordDO> listByPage(int currentPage, int pageSize) {
        PageInfo<UserEntryRecordDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }

    @Override
    public List<UserEntryInfoDTO> findUsersByCountryAndConfirmDate(String locationCountry, Date confirmDate) {
        return this.baseMapper.findUsersByCountryAndConfirmDate(locationCountry, confirmDate);
    }

    @Override
    public PageDTO<UserEntryInfoDTO> findUsersByCountryAndConfirmDateWithPage(String locationCountry, Date confirmDate,
                                                                              int currentPage, int pageSize) {
        PageInfo<UserEntryInfoDTO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.findUsersByCountryAndConfirmDate(locationCountry, confirmDate));

        // 构建分页结果
        Pagination pagination = new Pagination();
        pagination.setCurrentPage(pageInfo.getPageNum());
        pagination.setTotalPage(pageInfo.getPages());
        pagination.setTotalResult((int) pageInfo.getTotal());

        return PageDTO.of(pagination, pageInfo.getList());
    }
}
