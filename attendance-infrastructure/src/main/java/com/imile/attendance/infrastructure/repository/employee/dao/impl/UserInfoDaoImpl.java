package com.imile.attendance.infrastructure.repository.employee.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.dto.UserArchiveDTO;
import com.imile.attendance.infrastructure.repository.employee.dto.UserDTO;
import com.imile.attendance.infrastructure.repository.employee.dto.UserInformationDTO;
import com.imile.attendance.infrastructure.repository.employee.mapper.UserInfoMapper;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.DriverQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserArchiveQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveQuery;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/1/20
 * @Description
 */
@Component("userInfoDaoImpl")
@RequiredArgsConstructor
public class UserInfoDaoImpl extends ServiceImpl<UserInfoMapper, UserInfoDO> implements UserInfoDao {

    private final UserInfoMapper userInfoMapper;

    @Override
    public UserDTO getUserByCode(String userCode) {
        if (StringUtils.isEmpty(userCode)) {
            return null;
        }
        List<UserDTO> userByCodes = userInfoMapper.getUserByCode(Collections.singletonList(userCode));
        if (CollectionUtils.isEmpty(userByCodes)) {
            return null;
        }
        return userByCodes.get(0);
    }

    @Override
    public List<UserDTO> getUserByCodes(List<String> userCodeList) {
        if (CollectionUtils.isEmpty(userCodeList)) {
            return null;
        }
        return userInfoMapper.getUserByCode(userCodeList);
    }

    @Override
    public UserInfoDO getByUserCode(String userCode) {
        if (StringUtils.isEmpty(userCode)) {
            return null;
        }
        LambdaQueryWrapper<UserInfoDO> queryWrapper = Wrappers.lambdaQuery(UserInfoDO.class);
        queryWrapper.eq(UserInfoDO::getUserCode, userCode)
                .eq(UserInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());
        List<UserInfoDO> list = this.list(queryWrapper);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    @Override
    public List<UserInfoDO> userList(UserDaoQuery query) {
        LambdaQueryWrapper<UserInfoDO> queryWrapper = Wrappers.lambdaQuery(UserInfoDO.class);
        if (query.getUserId() != null) {
            queryWrapper.eq(UserInfoDO::getId, query.getUserId());
        }
        if (CollectionUtils.isNotEmpty(query.getUserIds())) {
            queryWrapper.in(UserInfoDO::getId, query.getUserIds());
        }
        if (query.getEmail() != null) {
            queryWrapper.eq(UserInfoDO::getEmail, query.getEmail());
        }
        if (StringUtils.isNotBlank(query.getUserName())) {
            queryWrapper.eq(UserInfoDO::getUserName, query.getUserName());
        }
        if (StringUtils.isNotBlank(query.getUserNameEn())) {
            queryWrapper.eq(UserInfoDO::getUserNameEn, query.getUserNameEn());
        }
        if (!CollUtil.isEmpty(query.getEmails())) {
            queryWrapper.in(UserInfoDO::getEmail, query.getEmails());
        }
        if (!CollUtil.isEmpty(query.getUserCodes())) {
            queryWrapper.in(UserInfoDO::getUserCode, query.getUserCodes());
        }
        if (query.getUserCode() != null) {
            queryWrapper.eq(UserInfoDO::getUserCode, query.getUserCode());
        }
        if (query.getStatus() != null) {
            queryWrapper.eq(UserInfoDO::getStatus, query.getStatus());
        }
        if (ObjectUtil.isNotEmpty(query.getWorkStatus())) {
            queryWrapper.eq(UserInfoDO::getWorkStatus, query.getWorkStatus());
        }
        if (CollUtil.isNotEmpty(query.getWorkStatusList())) {
            queryWrapper.in(UserInfoDO::getWorkStatus, query.getWorkStatusList());
        }
        if (CollectionUtils.isNotEmpty(query.getDeptIds())) {
            queryWrapper.in(UserInfoDO::getDeptId, query.getDeptIds());
        }
        if (query.getDeptId() != null) {
            queryWrapper.eq(UserInfoDO::getDeptId, query.getDeptId());
        }
        if (query.getIsDriver() != null) {
            queryWrapper.eq(UserInfoDO::getIsDriver, query.getIsDriver());
        }
        if (query.getIsWarehouseStaff() != null) {
            queryWrapper.eq(UserInfoDO::getIsWarehouseStaff, query.getIsWarehouseStaff());
        }
        if (CollectionUtils.isNotEmpty(query.getEmployeeTypeList())) {
            queryWrapper.in(UserInfoDO::getEmployeeType, query.getEmployeeTypeList());
        }
        if (query.getSex() != null) {
            queryWrapper.eq(UserInfoDO::getSex, query.getSex());
        }
        if (CollectionUtils.isNotEmpty(query.getLocalProviceList())) {
            queryWrapper.in(UserInfoDO::getLocationProvince, query.getLocalProviceList());
        }
        if (CollectionUtils.isNotEmpty(query.getCountryCodeList())) {
            queryWrapper.in(UserInfoDO::getCountryCode, query.getCountryCodeList());
        }
        if (Objects.nonNull(query.getIsGlobalRelocation())) {
            queryWrapper.eq(UserInfoDO::getIsGlobalRelocation, query.getIsGlobalRelocation());
        }
        if (StringUtils.isNotBlank(query.getCodeOrNameLike())) {
            queryWrapper.and(wrapper -> wrapper.like(UserInfoDO::getUserCode, query.getCodeOrNameLike()).or().like(UserInfoDO::getUserName, query.getCodeOrNameLike()));
        }
        if (StringUtils.isNotEmpty(query.getClassNature())) {
            queryWrapper.eq(UserInfoDO::getClassNature, query.getClassNature());
        }

        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getLocationCountry()), UserInfoDO::getLocationCountry, query.getLocationCountry());
        queryWrapper.in(CollUtil.isNotEmpty(query.getLocationCountryList()), UserInfoDO::getLocationCountry, query.getLocationCountryList());
        queryWrapper.in(CollUtil.isNotEmpty(query.getLocationCityList()), UserInfoDO::getLocationCity, query.getLocationCityList());

        //非删除数据
        queryWrapper.eq(UserInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());

        return this.list(queryWrapper);
    }

    @Override
    public List<UserInfoDO> listByLocationCountrys(List<String> locationCountryList) {
        if (CollectionUtils.isEmpty(locationCountryList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserInfoDO> queryWrapper = Wrappers.lambdaQuery(UserInfoDO.class);
        queryWrapper.in(UserInfoDO::getLocationCountry, locationCountryList)
                .eq(UserInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<UserInfoDO> listByUserCodes(List<String> userCodes) {
        if (CollectionUtils.isEmpty(userCodes)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserInfoDO> queryWrapper = Wrappers.lambdaQuery(UserInfoDO.class);
        queryWrapper.in(UserInfoDO::getUserCode, userCodes)
                .eq(UserInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public UserInformationDTO getUserInfoInformation(Long userId) {
        return userInfoMapper.getUserInfoInformation(userId);
    }

    @Override
    public List<UserInfoDO> queryDriver(DriverQuery driverQuery) {
        if (ObjectUtil.isNull(driverQuery)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserInfoDO> query = Wrappers.lambdaQuery();
        query.eq(UserInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());
        query.eq(UserInfoDO::getIsDriver, BusinessConstant.Y);
        query.in(CollectionUtils.isNotEmpty(driverQuery.getLocationCountryList()),
                UserInfoDO::getLocationCountry, driverQuery.getLocationCountryList());
        query.in(CollectionUtils.isNotEmpty(driverQuery.getEmployeeTypeList()),
                UserInfoDO::getEmployeeType, driverQuery.getEmployeeTypeList());
        query.isNotNull(UserInfoDO::getUserCode);
        return list(query);
    }

    @Override
    public List<UserInfoDO> selectUserForZkteco(List<Long> deptIdList, List<Long> userIdList) {
        //查询对应公司的员工
        LambdaQueryWrapper<UserInfoDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());
        if (CollectionUtils.isNotEmpty(deptIdList) && CollectionUtils.isEmpty(userIdList)) {
            wrapper.in(UserInfoDO::getDeptId, deptIdList);
        }
        if (CollectionUtils.isNotEmpty(userIdList) && CollectionUtils.isEmpty(deptIdList)) {
            wrapper.in(UserInfoDO::getId, userIdList);
        }
        if (CollectionUtils.isNotEmpty(userIdList) && CollectionUtils.isNotEmpty(deptIdList)) {
            wrapper.and(param ->
                    param.in(UserInfoDO::getDeptId, deptIdList)
                            .or(i -> i.in(UserInfoDO::getId, userIdList)));
        }
        wrapper.eq(UserInfoDO::getStatus, StatusEnum.ACTIVE.getCode());
        wrapper.in(UserInfoDO::getWorkStatus,
                Arrays.asList(WorkStatusEnum.ON_JOB.getCode(), WorkStatusEnum.TRANSFER.getCode()));
        wrapper.isNotNull(UserInfoDO::getUserCode);
        return list(wrapper);
    }

    @Override
    public UserInfoDO getByUserId(Long userId) {
        if (null == userId) {
            return null;
        }
        LambdaQueryWrapper<UserInfoDO> queryWrapper = Wrappers.lambdaQuery(UserInfoDO.class);
        queryWrapper.eq(UserInfoDO::getId, userId)
                .eq(UserInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.getOne(queryWrapper);
    }

    @Override
    public List<UserInfoDO> getByUserIds(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserInfoDO> queryWrapper = Wrappers.lambdaQuery(UserInfoDO.class);
        queryWrapper.in(UserInfoDO::getId, userIdList)
                .eq(UserInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }


    @Override
    public List<UserInfoDO> listByPage(int currentPage, int pageSize) {
        PageInfo<UserInfoDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }

    @Override
    public List<UserInfoDO> selectByAssociateCondition(UserDaoQuery query) {
        return userInfoMapper.selectByAssociateCondition(query);
    }

    @Override
    public List<UserArchiveDTO> userAttendanceArchive(UserArchiveQuery query) {
        return userInfoMapper.userAttendanceArchive(query);
    }

    @Override
    public List<UserInfoDO> selectLeaveUser(UserLeaveQuery query) {
        LambdaQueryWrapper<UserInfoDO> queryWrapper = Wrappers.lambdaQuery(UserInfoDO.class);
        queryWrapper.eq(UserInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.isNotNull(UserInfoDO::getUserCode);
        if (StringUtils.isNotBlank(query.getUserCode())) {
            queryWrapper.like(UserInfoDO::getUserCode, query.getUserCode());
        }
        if (StringUtils.isNotBlank(query.getUserNameOrEmail())) {
            queryWrapper.and(param -> param.like(UserInfoDO::getUserName, query.getUserNameOrEmail()).or(i -> i.like(UserInfoDO::getEmail, query.getUserNameOrEmail())));
        }
        if (StringUtils.isNotBlank(query.getUserCodeOrName())) {
            queryWrapper.and(param -> param.like(UserInfoDO::getUserName, query.getUserCodeOrName()).or(i -> i.like(UserInfoDO::getUserCode, query.getUserCodeOrName())));
        }
        if (StringUtils.isNotBlank(query.getCountry())) {
            queryWrapper.eq(UserInfoDO::getLocationCountry, query.getCountry());
        }
        if (CollectionUtils.isNotEmpty(query.getCountryList())) {
            queryWrapper.in(UserInfoDO::getLocationCountry, query.getCountryList());
        }
        if (ObjectUtil.isNotNull(query.getIsChooseDept()) && ObjectUtil.equal(query.getIsChooseDept(), Boolean.TRUE) && CollectionUtils.isNotEmpty(query.getDeptIdList())) {
            queryWrapper.in(UserInfoDO::getDeptId, query.getDeptIdList());
        }
        if ((ObjectUtil.isNotNull(query.getHasDeptPermission()) && Boolean.TRUE.equals(query.getHasDeptPermission()))
                && (ObjectUtil.isNotNull(query.getHasCountryPermission()) && Boolean.TRUE.equals(query.getHasCountryPermission()))) {
            if (CollUtil.isNotEmpty(query.getDeptIdList())) {
                queryWrapper.and(deptParam -> deptParam.in(CollUtil.isNotEmpty(query.getDeptIdList()), UserInfoDO::getDeptId, query.getDeptIdList())
                        .or(CollUtil.isNotEmpty(query.getAuthLocationCountryList()), param -> param.in(UserInfoDO::getLocationCountry, query.getAuthLocationCountryList())));
            } else {
                queryWrapper.in(CollUtil.isNotEmpty(query.getAuthLocationCountryList()), UserInfoDO::getLocationCountry, query.getAuthLocationCountryList());
            }
        }
        if ((ObjectUtil.isNotNull(query.getHasDeptPermission()) && Boolean.TRUE.equals(query.getHasDeptPermission()))
                && (ObjectUtil.isNotNull(query.getHasCountryPermission()) && Boolean.FALSE.equals(query.getHasCountryPermission()))) {
            queryWrapper.in(CollUtil.isNotEmpty(query.getDeptIdList()), UserInfoDO::getDeptId, query.getDeptIdList());
        }
        if ((ObjectUtil.isNotNull(query.getHasDeptPermission()) && Boolean.FALSE.equals(query.getHasDeptPermission()))
                && (ObjectUtil.isNotNull(query.getHasCountryPermission()) && Boolean.TRUE.equals(query.getHasCountryPermission()))) {
            queryWrapper.in(CollUtil.isNotEmpty(query.getAuthLocationCountryList()), UserInfoDO::getLocationCountry, query.getAuthLocationCountryList());
        }
        if (query.getPostId() != null) {
            queryWrapper.eq(UserInfoDO::getPostId, query.getPostId());
        }
        if (query.getIsDriver() != null) {
            queryWrapper.eq(UserInfoDO::getIsDriver, query.getIsDriver());
        }
        if (CollectionUtils.isNotEmpty(query.getEmployeeTypeList())) {
            queryWrapper.in(UserInfoDO::getEmployeeType, query.getEmployeeTypeList());
        }
        if (CollectionUtils.isNotEmpty(query.getVendorCodeList())) {
            queryWrapper.in(UserInfoDO::getVendorCode, query.getVendorCodeList());
        }
        if (CollectionUtils.isNotEmpty(query.getPostIdList())) {
            queryWrapper.in(UserInfoDO::getPostId, query.getPostIdList());
        }
        queryWrapper.in(ObjectUtil.isNotEmpty(query.getWorkStatusList()), UserInfoDO::getWorkStatus, query.getWorkStatusList());
        return list(queryWrapper);
    }

    @Override
    public List<String> selectUserLocationCountry() {
        return userInfoMapper.selectUserLocationCountry();
    }
}
