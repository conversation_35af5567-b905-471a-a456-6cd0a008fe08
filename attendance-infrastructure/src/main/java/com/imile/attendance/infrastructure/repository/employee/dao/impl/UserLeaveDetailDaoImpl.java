package com.imile.attendance.infrastructure.repository.employee.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveDetailDao;
import com.imile.attendance.infrastructure.repository.employee.mapper.UserLeaveDetailMapper;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveDetailQuery;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Component
@RequiredArgsConstructor
public class UserLeaveDetailDaoImpl extends ServiceImpl<UserLeaveDetailMapper, UserLeaveDetailDO> implements UserLeaveDetailDao {

//    @Autowired
//    private HrmsUserLeaveDetailMapper hrmsUserLeaveDetailMapper;

    @Override
    public List<UserLeaveDetailDO> selectUserLeaveDetail(UserLeaveDetailQuery query) {
        LambdaQueryWrapper<UserLeaveDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserLeaveDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        if (CollectionUtils.isNotEmpty(query.getUserIds())) {
            queryWrapper.in(UserLeaveDetailDO::getUserId, query.getUserIds());
        }
        if (query.getUserId() != null && query.getUserId() != 0) {
            queryWrapper.eq(UserLeaveDetailDO::getUserId, query.getUserId());
        }
        if (query.getConfigId() != null && query.getConfigId() != 0) {
            queryWrapper.eq(UserLeaveDetailDO::getConfigId, query.getConfigId());
        }
        if (CollectionUtils.isNotEmpty(query.getConfigIdList())) {
            queryWrapper.in(UserLeaveDetailDO::getConfigId, query.getConfigIdList());
        }
        if (StringUtils.isNotBlank(query.getLeaveName())) {
            queryWrapper.eq(UserLeaveDetailDO::getLeaveName, query.getLeaveName());
        }
        if (CollectionUtils.isNotEmpty(query.getLeaveNameList())) {
            queryWrapper.in(UserLeaveDetailDO::getLeaveName, query.getLeaveNameList());
        }
        if (StringUtils.isNotBlank(query.getLeaveType())) {
            queryWrapper.eq(UserLeaveDetailDO::getLeaveType, query.getLeaveType());
        }
        if (CollectionUtils.isNotEmpty(query.getLeaveTypeList())) {
            queryWrapper.in(UserLeaveDetailDO::getLeaveType, query.getLeaveTypeList());
        }
        if (StringUtils.isNotBlank(query.getStatus())) {
            queryWrapper.eq(UserLeaveDetailDO::getStatus, query.getStatus());
        }
        if (CollectionUtils.isNotEmpty(query.getUserIds())) {
            queryWrapper.in(UserLeaveDetailDO::getUserId, query.getUserIds());
        }
        return list(queryWrapper);
    }

    @Override
    public List<UserLeaveDetailDO> selectUserLeaveByUserId(Long userId) {
        LambdaQueryWrapper<UserLeaveDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserLeaveDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserLeaveDetailDO::getUserId, userId);
        return list(queryWrapper);
    }

//    @Override
//    public List<CommonCountPO> selectLeaveConfigBindCount(List<String> countryList) {
//        return hrmsUserLeaveDetailMapper.selectLeaveConfigBindCount(countryList);
//    }
}
