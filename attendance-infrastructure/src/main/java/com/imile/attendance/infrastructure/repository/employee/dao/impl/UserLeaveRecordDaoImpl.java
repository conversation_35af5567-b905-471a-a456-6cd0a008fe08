package com.imile.attendance.infrastructure.repository.employee.dao.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveRecordDao;
import com.imile.attendance.infrastructure.repository.employee.mapper.UserLeaveRecordMapper;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveRecordConditionQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveRecordQuery;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Component
@RequiredArgsConstructor
public class UserLeaveRecordDaoImpl extends ServiceImpl<UserLeaveRecordMapper, UserLeaveRecordDO> implements UserLeaveRecordDao {

    @Override
    public List<UserLeaveRecordDO> selectUserLeaveDetail(UserLeaveRecordQuery query) {
        LambdaQueryWrapper<UserLeaveRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(query.getUserCode())) {
            queryWrapper.eq(UserLeaveRecordDO::getUserCode, query.getUserCode());
        }
        if (Objects.nonNull(query.getUserId())) {
            queryWrapper.eq(UserLeaveRecordDO::getUserId, query.getUserId());
        }
        if (CollectionUtils.isNotEmpty(query.getUserIds())) {
            queryWrapper.in(UserLeaveRecordDO::getUserId, query.getUserIds());
        }
        if (Objects.nonNull(query.getConfigId())) {
            queryWrapper.eq(UserLeaveRecordDO::getConfigId, query.getConfigId());
        }
        if (StringUtils.isNotBlank(query.getLeaveName())) {
            queryWrapper.eq(UserLeaveRecordDO::getLeaveName, query.getLeaveName());
        }
        if (StringUtils.isNotBlank(query.getLeaveType())) {
            queryWrapper.eq(UserLeaveRecordDO::getLeaveType, query.getLeaveType());
        }
        if (StringUtils.isNotBlank(query.getType())) {
            queryWrapper.eq(UserLeaveRecordDO::getType, query.getType());
        }
        if (CollectionUtils.isNotEmpty(query.getTypeList())) {
            queryWrapper.in(UserLeaveRecordDO::getType, query.getTypeList());
        }
        if (query.getLeaveStartDay() != null && query.getLeaveEndDay() != null) {
            queryWrapper.ge(UserLeaveRecordDO::getLeaveStartDay, query.getLeaveStartDay());
            queryWrapper.le(UserLeaveRecordDO::getLeaveEndDay, query.getLeaveEndDay());
        }
        if (query.getBeginDate() != null && query.getEndDate() != null) {
            queryWrapper.ge(UserLeaveRecordDO::getDate, query.getBeginDate());
            queryWrapper.le(UserLeaveRecordDO::getDate, query.getEndDate());
        }
        if (StringUtils.isNotBlank(query.getOperationUserCode())) {
            queryWrapper.eq(UserLeaveRecordDO::getOperationUserCode, query.getOperationUserCode());
        }
        queryWrapper.eq(UserLeaveRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(UserLeaveRecordDO::getCreateDate);
        return list(queryWrapper);
    }

    @Override
    public List<UserLeaveRecordDO> selectRecordByUserIdList(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<UserLeaveRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserLeaveRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(UserLeaveRecordDO::getUserId, userIdList);
        return list(queryWrapper);
    }

    @Override
    public List<UserLeaveRecordDO> selectUserLeaveRecordByCondition(UserLeaveRecordConditionQuery query) {
        if (ObjectUtil.isNull(query)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<UserLeaveRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserLeaveRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(CollUtil.isNotEmpty(query.getUserIdList()), UserLeaveRecordDO::getUserId, query.getUserIdList());
        queryWrapper.in(CollUtil.isNotEmpty(query.getDayIdList()), UserLeaveRecordDO::getDayId, query.getDayIdList());
        queryWrapper.eq(ObjectUtil.isNotNull(query.getDayId()), UserLeaveRecordDO::getDayId, query.getDayId());
        queryWrapper.eq(StringUtils.isNotBlank(query.getLeaveName()), UserLeaveRecordDO::getLeaveName, query.getLeaveName());
        queryWrapper.eq(StringUtils.isNotBlank(query.getLeaveType()), UserLeaveRecordDO::getLeaveType, query.getLeaveType());
        queryWrapper.eq(StringUtils.isNotBlank(query.getType()), UserLeaveRecordDO::getType, query.getType());
        queryWrapper.eq(StringUtils.isNotBlank(query.getOperationUserCode()), UserLeaveRecordDO::getOperationUserCode, query.getOperationUserCode());
        queryWrapper.eq(StringUtils.isNotBlank(query.getOperationUserName()), UserLeaveRecordDO::getOperationUserName, query.getOperationUserName());
        return list(queryWrapper);
    }

    @Override
    public List<UserLeaveRecordDO> selectRecordByDayId(Long userId, String usrCode, Long dayId) {
        LambdaQueryWrapper<UserLeaveRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserLeaveRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        if (StringUtils.isNotBlank(usrCode)) {
            queryWrapper.eq(UserLeaveRecordDO::getUserCode, usrCode);
        }
        queryWrapper.eq(UserLeaveRecordDO::getUserId, userId);
        queryWrapper.eq(UserLeaveRecordDO::getDayId, dayId);
        return list(queryWrapper);
    }

    @Override
    public List<UserLeaveRecordDO> selectRecordByUserId(Long userId, String leaveType) {
        LambdaQueryWrapper<UserLeaveRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserLeaveRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        if (userId != null) {
            queryWrapper.eq(UserLeaveRecordDO::getUserId, userId);
        }
        if (StringUtils.isNotBlank(leaveType)) {
            queryWrapper.eq(UserLeaveRecordDO::getLeaveType, leaveType);
        }
        queryWrapper.orderByDesc(UserLeaveRecordDO::getCreateDate);
        return list(queryWrapper);
    }

}
