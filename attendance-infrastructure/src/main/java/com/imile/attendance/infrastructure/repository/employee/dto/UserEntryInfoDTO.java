package com.imile.attendance.infrastructure.repository.employee.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 用户入职信息DTO
 * 用于根据国家和入职确认时间查询用户的返回结果
 * 
 * <AUTHOR> chen
 * @since 2025/7/9
 */
@Data
public class UserEntryInfoDTO {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户编码
     */
    private String userCode;
    
    /**
     * 用户姓名
     */
    private String userName;
    
    /**
     * 入职确认日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date confirmDate;
    
    /**
     * 常驻地国家
     */
    private String locationCountry;
}
