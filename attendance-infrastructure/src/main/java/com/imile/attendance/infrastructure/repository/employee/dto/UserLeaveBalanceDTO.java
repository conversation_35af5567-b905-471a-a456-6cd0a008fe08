package com.imile.attendance.infrastructure.repository.employee.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/5/27
 * @Description 员工假期余额DTO
 */
@Data
public class UserLeaveBalanceDTO {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 员工编码
     */
    private String userCode;

    /**
     * 是否失效
     */
    private Integer isInvalid;

    /**
     * 假期发放日期
     */
    private String issueDate;

    /**
     * 假期失效日期
     */
    private String invalidDate;

    /**
     * 是否派遣假
     */
    private Integer isDispatch;

    /**
     * 国家
     */
    private String country;

    /**
     * 假期类型
     */
    private String leaveType;

    /**
     * 假期名称
     */
    private String leaveName;

    /**
     * 是否结转
     */
    private Integer leaveMark;

    /**
     * 使用条件
     */
    private Integer useCondition;

    /**
     * 假期额度（分钟数）
     */
    private BigDecimal leaveTotalMinutes;

    /**
     * 已使用额度（分钟数）
     */
    private BigDecimal leaveUsedMinutes;

    /**
     * 假期剩余时间(分钟) 余额
     */
    private BigDecimal leaveResidueMinutes;

    /**
     * 发薪比例
     */
    private BigDecimal percentSalary;
}
