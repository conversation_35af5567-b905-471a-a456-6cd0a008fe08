package com.imile.attendance.infrastructure.repository.employee.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/5/30
 * @Description 员工假期记录导入DTO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserLeaveRecordImportDTO {

    private Long id;

    /**
     * 假期配置id
     */
    private Long configId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 日期
     */
    private Date date;
    private Long dayId;

    /**
     * 假期名称
     */
    private String leaveName;

    /**
     * 假期类型
     */
    private String leaveType;

    /**
     * 操作类型(对应LeaveTypeEnum)
     */
    private String type;

    /**
     * 请假开始日期
     */
    private Date leaveStartDay;

    /**
     * 请假结束日期
     */
    private Date leaveEndDay;

    /**
     * 请假分钟
     */
    private BigDecimal leaveMinutes;

    /**
     * 请假时长
     */
    private BigDecimal hours;

    /**
     * 上传图片路径
     */
    private String picturePath;

    /**
     * 假期剩余天数
     */
    private BigDecimal leaveResidueDay;

    /**
     * 操作人编码
     */
    private String operationUserCode;

    /**
     * 操作人名称
     */
    private String operationUserName;

    /**
     * 假期减少天数
     */
    private BigDecimal reduceDay;

    /**
     * 是否结转
     */
    private Integer leaveMark;

    /**
     * 备注
     */
    private String remark;

    /**
     * 发薪比例
     */
    private BigDecimal percentSalary;
}
