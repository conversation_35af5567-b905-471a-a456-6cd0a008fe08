package com.imile.attendance.infrastructure.repository.employee.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.employee.dto.UserEntryInfoDTO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserEntryRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/1/20
 * @Description
 */
@Mapper
@Repository
public interface UserEntryRecordMapper extends BaseMapper<UserEntryRecordDO> {

    /**
     * 根据国家和入职确认时间查找用户
     *
     * @param locationCountry 国家代码，如'CHN'
     * @param confirmDate 入职确认时间的起始日期
     * @return 符合条件的用户列表
     */
    List<UserEntryInfoDTO> findUsersByCountryAndConfirmDate(@Param("locationCountry") String locationCountry,
                                                            @Param("confirmDate") Date confirmDate);
}
