package com.imile.attendance.infrastructure.repository.employee.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.employee.dto.UserLeaveBalanceDTO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Mapper
public interface UserLeaveDetailMapper extends BaseMapper<UserLeaveDetailDO> {

    /**
     * 查询假期配置绑定员工数
     *
     * @param countryList 适用国家列表
     * @return List<CommonCountPO>
     */
//    List<CommonCountPO> selectLeaveConfigBindCount(@Param("countryList") List<String> countryList);

    /**
     * 联合查询员工假期余额
     *
     * @param userIds 员工集合
     * @return List<UserLeaveResidualDTO>
     */
    List<UserLeaveBalanceDTO> selectBatchUserResidual(@Param("userIds") List<Long> userIds);
}
