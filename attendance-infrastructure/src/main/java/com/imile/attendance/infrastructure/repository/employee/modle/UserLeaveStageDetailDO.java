package com.imile.attendance.infrastructure.repository.employee.modle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 用户假期余额表
 *
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_leave_stage_detail")
public class UserLeaveStageDetailDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户假期id
     */
    @ApiModelProperty(value = "用户假期详情表id")
    private Long leaveId;

    /**
     * 假期标记: 0：表示非结转，1：结转，...
     */
    @ApiModelProperty(value = "假期标记")
    private Integer leaveMark;

    /**
     * 是否失效: 0：表示未失效，1：失效，...
     */
    @ApiModelProperty(value = "是否失效")
    private Integer isInvalid;

    /**
     * 假期发放日期：yyyyMMdd
     */
    @ApiModelProperty(value = "假期发放日期")
    private String issueDate;

    /**
     * 假期失效日期：yyyyMMdd
     */
    @ApiModelProperty(value = "假期失效日期")
    private String invalidDate;

    /**
     * 假期剩余分钟数
     */
    @ApiModelProperty(value = "假期剩余分钟数")
    private BigDecimal leaveResidueMinutes;

    /**
     * 假期已使用分钟数
     */
    @ApiModelProperty(value = "假期已使用分钟数")
    private BigDecimal leaveUsedMinutes;

    /**
     * 阶段
     */
    @ApiModelProperty(value = "阶段")
    private Integer stage;

    /**
     * 百分比日薪
     */
    @ApiModelProperty(value = "百分比日薪")
    private BigDecimal percentSalary;

    /**
     * 状态 ACTIVE、DISABLED
     */
    @ApiModelProperty(value = "状态")
    private String status;


}
