package com.imile.attendance.infrastructure.repository.employee.query;

import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/6
 */
@Data
public class UserArchiveQuery {

    /**
     * 工作状态
     */
    private String workStatus;

    /**
     * 用工类型
     */
    private List<String> employeeTypeList;

    /**
     * 部门
     */
    private List<Long> deptIdList;

    /**
     * 常驻国
     */
    private String locationCountry;

    /**
     * 岗位
     */
    private List<Long> postIdList;

    /**
     * 是否司机
     */
    private Integer isDriver;

    /**
     * 是否派遣
     */
    private Integer isGlobalRelocation;

    /**
     * 班次类型
     */
    private String classNature;

    /**
     * 日历ID
     */
    private Long calendarId;

    /**
     * 打卡方式
     */
    private String punchCardType;

    /**
     * 开始日期
     */
    private Long startDayId;

    /**
     * 结束日期
     */
    private Long endDayId;

    /**
     * 用户ID列表
     */
    private List<Long> userIdList;

    /**
     * 当用户ID超过1000条的处理列表
     */
    private List<List<Long>> batchUserIdList;

    /**
     * 权限常驻国
     */
    private List<String> authLocationCountryList;

    /**
     * 权限部门
     */
    private List<Long> authDeptIdList;

    /**
     * 前端是否选择了部门
     */
    private Boolean isChooseDept = Boolean.FALSE;

    /**
     * 是否需要查询特殊的国家，影响用工类型
     */
    private Boolean isNeedQuerySpecialCountry = false;

    /**
     * 特殊国家集合
     */
    private List<String> specialCountryList;

    /**
     * 常规国家集合
     */
    private List<String> normalCountryList;

    /**
     * 特殊的用工类型集合
     */
    private List<String> specialEmployeeTypeList;

    /**
     * 常规用工类型集合
     */
    private List<String> normalEmployeeTypeList;

    private List<Long> specialDeptList;

    private List<Long> normalDeptList;
}
