package com.imile.attendance.infrastructure.repository.employee.query;

import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/9/12 10:58
 * @version: 1.0
 */
@Data
public class UserLeaveQuery {
    /**
     * 用户账号
     */
    private String userCode;
    /**
     * 用户姓名或邮箱
     */
    private String userNameOrEmail;
    /**
     * 部门id列表
     */
    private List<Long> deptIdList;

    /**
     * 岗位id
     */
    private Long postId;
    /**
     * 用户账号或姓名
     */
    private String userCodeOrName;

    private String country;

    /**
     * 权限常驻国
     */
    private List<String> authLocationCountryList;

    /**
     * 是否有部门权限
     */
    private Boolean hasDeptPermission;

    /**
     * 是否有国家权限
     */
    private Boolean hasCountryPermission;
    /**
     * 是否有与国家、部门权限
     */
    private Boolean hasAndDeptAndCountryPermission;

    /**
     * 是否有或国家、部门权限
     */
    private Boolean hasOrDeptAndCountryPermission;


    private Integer isDriver;

    private List<String> employeeTypeList;

    private List<String> vendorCodeList;

    private List<Long> postIdList;

    List<String> countryList;
    /**
     * 工作状态
     */
    private List<String> workStatusList;

    /**
     * 前端是否选择了部门
     */
    private Boolean isChooseDept;
}
