package com.imile.attendance.infrastructure.repository.employee.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/27
 * @Description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserLeaveRecordQuery implements Serializable {
    private static final long serialVersionUID = 3549892451966006875L;

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户id集合
     */
    private List<Long> userIds;
    /**
     * 用户账号
     */
    private String userCode;
    /**
     * 假期规则主键
     */
    private Long configId;
    /**
     * 假期名称
     */
    private String leaveName;
    /**
     * 假期类型
     */
    private String leaveType;
    /**
     * 类型(请假、销假)
     */
    private String type;
    /**
     * 类型集合
     */
    private List<String> typeList;
    /**
     * 请假开始日期
     */
    private Date leaveStartDay;
    /**
     * 请假结束日期
     */
    private Date leaveEndDay;
    /**
     * 发放开始日期
     */
    private Date beginDate;
    /**
     * 发放结束日期
     */
    private Date endDate;
    /**
     * 操作人编码
     */
    private String operationUserCode;
}
