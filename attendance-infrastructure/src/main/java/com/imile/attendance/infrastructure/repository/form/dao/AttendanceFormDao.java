package com.imile.attendance.infrastructure.repository.form.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.query.ApplicationFormQuery;
import com.imile.attendance.infrastructure.repository.form.query.AttendanceApprovalInfoQuery;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/19
 * @Description
 */
public interface AttendanceFormDao extends IService<AttendanceFormDO> {

    List<AttendanceFormDO> selectByIds(List<Long> formIdList);

    List<AttendanceFormDO> selectForm(ApplicationFormQuery query);

    List<AttendanceFormDO> selectAttendanceApprovalInfo(AttendanceApprovalInfoQuery query);

    List<AttendanceFormDO> selectAttendanceApprovalInfoCustom(AttendanceApprovalInfoQuery query);
}

