package com.imile.attendance.infrastructure.repository.form.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.form.model.UserCycleReissueCardCountDO;


import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/19
 * @Description
 */
public interface UserCycleReissueCardCountDao extends IService<UserCycleReissueCardCountDO> {

    /**
     * 查询用户的所有考勤周期的打卡配置
     */
    List<UserCycleReissueCardCountDO> selectByUserIdList(List<Long> userIdList);

    /**
     * 根据条件删除用户周期补卡次数记录
     * @param cycleStartDate 周期开始时间（删除条件：>= 该时间）
     * @param cycleEndDate 周期结束时间（删除条件：>= 该时间）
     * @param createDate 创建时间（删除条件：= 该时间）
     * @return 删除的记录数量
     */
    Integer deleteByCondition(Date cycleStartDate, Date cycleEndDate, Date createDate);

}

