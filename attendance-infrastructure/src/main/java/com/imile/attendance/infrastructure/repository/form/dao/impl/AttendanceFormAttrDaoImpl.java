package com.imile.attendance.infrastructure.repository.form.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormAttrDao;
import com.imile.attendance.infrastructure.repository.form.mapper.AttendanceFormAttrMapper;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/19
 * @Description
 */
@Component
@RequiredArgsConstructor
public class AttendanceFormAttrDaoImpl extends ServiceImpl<AttendanceFormAttrMapper, AttendanceFormAttrDO> implements AttendanceFormAttrDao {


    @Override
    public List<AttendanceFormAttrDO> selectFormAttrByFormIdList(List<Long> formIdList) {
        if (CollectionUtils.isEmpty(formIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AttendanceFormAttrDO> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(AttendanceFormAttrDO::getIsDelete, BusinessConstant.N);
        if (CollectionUtils.isNotEmpty(formIdList)) {
            queryWrapper.in(AttendanceFormAttrDO::getFormId, formIdList);
        }

        return this.list(queryWrapper);
    }

    @Override
    public List<AttendanceFormAttrDO> selectFormAttrByFormId(Long formId) {
        LambdaQueryWrapper<AttendanceFormAttrDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AttendanceFormAttrDO::getIsDelete, BusinessConstant.N);
        queryWrapper.eq(AttendanceFormAttrDO::getFormId, formId);

        return this.list(queryWrapper);
    }
}

