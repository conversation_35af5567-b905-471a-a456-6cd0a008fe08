package com.imile.attendance.infrastructure.repository.form.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormDao;
import com.imile.attendance.infrastructure.repository.form.mapper.AttendanceFormMapper;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.query.ApplicationFormQuery;
import com.imile.attendance.infrastructure.repository.form.query.AttendanceApprovalInfoQuery;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/3/19
 * @Description
 */
@Component
@RequiredArgsConstructor
public class AttendanceFormDaoImpl extends ServiceImpl<AttendanceFormMapper, AttendanceFormDO> implements AttendanceFormDao {

    @Resource
    private AttendanceFormMapper attendanceFormMapper;

    @Override
    public List<AttendanceFormDO> selectByIds(List<Long> formIdList) {
        if (CollectionUtils.isEmpty(formIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AttendanceFormDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AttendanceFormDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(AttendanceFormDO::getId, formIdList);
        return this.list(queryWrapper);
    }

    @Override
    public List<AttendanceFormDO> selectForm(ApplicationFormQuery query) {
        LambdaQueryWrapper<AttendanceFormDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AttendanceFormDO::getIsDelete, BusinessConstant.N);
        if (query.getUserId() != null) {
            queryWrapper.eq(AttendanceFormDO::getUserId, query.getUserId());
        }
        if (StringUtils.isNotBlank(query.getApplicationFormCode())) {
            queryWrapper.eq(AttendanceFormDO::getApplicationCode, query.getApplicationFormCode());
        }
        if (CollectionUtils.isNotEmpty(query.getUserIdList())) {
            queryWrapper.in(AttendanceFormDO::getUserId, query.getUserIdList());
        }
        if (CollectionUtils.isNotEmpty(query.getStatusList())) {
            queryWrapper.in(AttendanceFormDO::getFormStatus, query.getStatusList());
        }
        if (CollectionUtils.isNotEmpty(query.getFromTypeList())) {
            queryWrapper.in(AttendanceFormDO::getFormType, query.getFromTypeList());
        }
        return this.list(queryWrapper);
    }

    @Override
    public List<AttendanceFormDO> selectAttendanceApprovalInfo(AttendanceApprovalInfoQuery query) {
        LambdaQueryWrapper<AttendanceFormDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AttendanceFormDO::getIsDelete, BusinessConstant.N);
        if (query.getApplyUserId() != null) {
            queryWrapper.eq(AttendanceFormDO::getApplyUserId, query.getApplyUserId());
        }
        if (CollectionUtils.isNotEmpty(query.getUserIdList())) {
            queryWrapper.in(AttendanceFormDO::getUserId, query.getUserIdList());
        }
        if (CollectionUtils.isNotEmpty(query.getFormStatusList())) {
            queryWrapper.in(AttendanceFormDO::getFormStatus, query.getFormStatusList());
        }
        if (StringUtils.isNotBlank(query.getUserName())) {
            queryWrapper.like(AttendanceFormDO::getUserName, query.getUserName());
        }
        if (StringUtils.isNotBlank(query.getUserCodeOrName())) {
            queryWrapper.and(wrapper -> wrapper.like(AttendanceFormDO::getUserCode, query.getUserCodeOrName())
                    .or()
                    .like(AttendanceFormDO::getUserName, query.getUserCodeOrName()));
        }
        // 同时具有部门和国家权限
        if (CollectionUtils.isNotEmpty(query.getAuthLocationCountryList())
                && CollectionUtils.isNotEmpty(query.getAuthDeptIdList())) {
            queryWrapper.and(wrapper -> wrapper
                    .in(AttendanceFormDO::getCountry, query.getAuthLocationCountryList())
                    .or()
                    .in(AttendanceFormDO::getDeptId, query.getAuthDeptIdList()));
        }
        // 只有国家权限
        if (CollectionUtils.isNotEmpty(query.getAuthLocationCountryList())
                && CollectionUtils.isEmpty(query.getAuthDeptIdList())) {
            queryWrapper.in(AttendanceFormDO::getCountry, query.getAuthLocationCountryList());
        }
        // 只有部门权限
        if (CollectionUtils.isNotEmpty(query.getAuthDeptIdList())
                && CollectionUtils.isEmpty(query.getAuthLocationCountryList())) {
            queryWrapper.in(AttendanceFormDO::getDeptId, query.getAuthDeptIdList());
        }
        if (query.getDeptId() != null) {
            queryWrapper.eq(AttendanceFormDO::getDeptId, query.getDeptId());
        }
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getDeptIds()), AttendanceFormDO::getDeptId, query.getDeptIds());
        if (query.getPostId() != null) {
            queryWrapper.eq(AttendanceFormDO::getPostId, query.getPostId());
        }
        if (CollectionUtils.isNotEmpty(query.getFormTypeList())) {
            queryWrapper.in(AttendanceFormDO::getFormType, query.getFormTypeList());
        }
        if (query.getStartDate() != null) {
            queryWrapper.ge(AttendanceFormDO::getCreateDate, query.getStartDate());
        }
        if (query.getEndDate() != null) {
            queryWrapper.le(AttendanceFormDO::getCreateDate, query.getEndDate());
        }
        if (StringUtils.isNotBlank(query.getApplicationFormCode())) {
            queryWrapper.like(AttendanceFormDO::getApplicationCode, query.getApplicationFormCode());
        }
        if (StringUtils.isNotBlank(query.getExcludeApplicationDataSource())) {
            //TODO ne有问题，为空无法判断
            queryWrapper.isNull(AttendanceFormDO::getDataSource);
            //queryWrapper.ne(HrmsApplicationFormDO::getDataSource, query.getExcludeApplicationDataSource());
        }
        if (StringUtils.isNotBlank(query.getCountry())) {
            queryWrapper.eq(AttendanceFormDO::getCountry, query.getCountry());
        }
        queryWrapper.orderByDesc(AttendanceFormDO::getLastUpdDate);
        return this.list(queryWrapper);
    }

    @Override
    public List<AttendanceFormDO> selectAttendanceApprovalInfoCustom(AttendanceApprovalInfoQuery query) {
        return attendanceFormMapper.selectAttendanceApprovalInfo(query);
    }
}

