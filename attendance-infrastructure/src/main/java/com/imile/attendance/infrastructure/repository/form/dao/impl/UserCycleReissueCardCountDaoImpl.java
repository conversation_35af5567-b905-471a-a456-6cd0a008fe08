package com.imile.attendance.infrastructure.repository.form.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.form.dao.UserCycleReissueCardCountDao;
import com.imile.attendance.infrastructure.repository.form.mapper.UserCycleReissueCardCountMapper;
import com.imile.attendance.infrastructure.repository.form.model.UserCycleReissueCardCountDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/19
 * @Description
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserCycleReissueCardCountDaoImpl extends ServiceImpl<UserCycleReissueCardCountMapper, UserCycleReissueCardCountDO>
        implements UserCycleReissueCardCountDao {

    @Override
    public List<UserCycleReissueCardCountDO> selectByUserIdList(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserCycleReissueCardCountDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(UserCycleReissueCardCountDO::getUserId, userIdList);
        wrapper.eq(UserCycleReissueCardCountDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(wrapper);
    }

    @Override
    public Integer deleteByCondition(Date cycleStartDate, Date cycleEndDate, Date createDate) {
        log.info("开始执行用户周期补卡次数删除操作，条件：cycleStartDate >= {}, cycleEndDate >= {}, createDate = {}",
                cycleStartDate, cycleEndDate, createDate);

        // 构建删除条件
        LambdaQueryWrapper<UserCycleReissueCardCountDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(UserCycleReissueCardCountDO::getCycleStartDate, cycleStartDate);
        wrapper.ge(UserCycleReissueCardCountDO::getCycleEndDate, cycleEndDate);
        wrapper.eq(UserCycleReissueCardCountDO::getCreateDate, createDate);
        wrapper.eq(UserCycleReissueCardCountDO::getIsDelete, IsDeleteEnum.NO.getCode());

        // 先查询符合条件的记录数量
        int count = this.count(wrapper);
        log.info("符合删除条件的记录数量：{}", count);

        if (count == 0) {
            log.info("没有符合条件的记录，删除操作结束");
            return 0;
        }

        // 执行物理删除
        boolean result = this.remove(wrapper);
        if (result) {
            log.info("删除操作成功，删除记录数量：{}", count);
            return count;
        } else {
            log.error("删除操作失败");
            return 0;
        }
    }
}

