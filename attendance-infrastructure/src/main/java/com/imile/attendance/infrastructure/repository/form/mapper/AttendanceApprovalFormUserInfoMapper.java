package com.imile.attendance.infrastructure.repository.form.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.form.dto.OverTimeApprovalListDTO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormUserInfoDO;
import com.imile.attendance.infrastructure.repository.form.query.OverTimeListQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/19
 * @Description
 */
@Mapper
@Repository
public interface AttendanceApprovalFormUserInfoMapper extends BaseMapper<AttendanceApprovalFormUserInfoDO> {

    List<OverTimeApprovalListDTO> selectListByCondition(@Param("query") OverTimeListQuery query);

}

