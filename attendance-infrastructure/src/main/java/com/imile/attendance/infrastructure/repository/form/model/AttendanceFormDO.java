package com.imile.attendance.infrastructure.repository.form.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> chen
 * @Date 2025/3/19
 * @Description
 */
@ApiModel(description = "考勤申请单")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("attendance_form")
public class AttendanceFormDO extends BaseDO {

    @ApiModelProperty(value = "")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 申请人ID
     */
    @ApiModelProperty(value = "申请人ID")
    private Long applyUserId;

    /**
     * 被申请人ID
     */
    @ApiModelProperty(value = "被申请人ID")
    private Long userId;

    /**
     * 被申请人编码
     */
    @ApiModelProperty(value = "被申请人编码")
    private String userCode;

    /**
     * 被申请人姓名
     */
    @ApiModelProperty(value = "被申请人姓名")
    private String userName;

    /**
     * 被申请人部门
     */
    @ApiModelProperty(value = "被申请人部门")
    private Long deptId;

    /**
     * 被申请人岗位
     */
    @ApiModelProperty(value = "被申请人岗位")
    private Long postId;

    /**
     * 被申请人所在国
     */
    @ApiModelProperty(value = "被申请人所在国")
    private String country;

    /**
     * 被申请人结算国
     */
    @ApiModelProperty(value = "被申请人结算国")
    private String originCountry;

    /**
     * 是否仓内员工
     */
    @ApiModelProperty(value = "是否仓内员工")
    private Integer isWarehouseStaff;

    /**
     * 申请单号
     */
    @ApiModelProperty(value = "申请单号")
    private String applicationCode;

    /**
     * 单据类型
     */
    @ApiModelProperty(value = "单据类型")
    private String formType;

    /**
     * 单据状态
     */
    @ApiModelProperty(value = "单据状态")
    private String formStatus;

    /**
     * 审批单ID
     */
    @ApiModelProperty(value = "审批单ID")
    private Long approvalId;

    /**
     * 审批节点信息
     */
    @ApiModelProperty(value = "审批节点信息")
    private String approvalProcessInfo;

    /**
     * 单据来源
     */
    @ApiModelProperty(value = "单据来源")
    private String dataSource;
}

