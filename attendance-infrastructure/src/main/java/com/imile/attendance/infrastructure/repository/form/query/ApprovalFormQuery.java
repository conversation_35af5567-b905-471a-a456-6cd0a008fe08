package com.imile.attendance.infrastructure.repository.form.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} ApprovalFormQuery
 * {@code @since:} 2024-06-14 15:39
 * {@code @description:}
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApprovalFormQuery {
    /**
     * 审批单据id集合
     */
    private List<Long> formIdList;

    /**
     * 单据状态
     */
    private String formStatus;

    /**
     * 单据状态集合
     */
    private List<String> formStatusList;

    /**
     * 审批单据类型
     */
    private List<String> formTypeList;

    /**
     * 数据来源
     */
    private Integer dataSource;

    /**
     * 提交开始时间
     */
    private String startDate;

    /**
     * 提交结束时间
     */
    private String endDate;

    /**
     * 单据编码
     */
    private String applicationCode;

}
