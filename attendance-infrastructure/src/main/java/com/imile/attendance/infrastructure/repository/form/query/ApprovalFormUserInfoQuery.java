package com.imile.attendance.infrastructure.repository.form.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} ApprovalFormUserInfoQuery
 * {@code @since:} 2024-06-12 17:58
 * {@code @description:}
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApprovalFormUserInfoQuery {

    /**
     * 被申请人
     */
    private String userCode;

    /**
     * 被申请人集合
     */
    private List<String> userCodes;

    /**
     * 日期
     */
    private Long dayId;

    /**
     * 审批单id
     */
    private Long approvalFormId;

    private List<Long> approvalFormIdList;

    /**
     * 国家
     */
    private String country;

    /**
     * 岗位id集合
     */
    private List<Long> postIdList;

    /**
     * 部门id集合
     */
    private List<Long> deptIdList;

    /**
     * 用户账号或名称模糊查询
     */
    private String userCodeOrName;

    /**
     * 权限常驻国
     */
    private List<String> authLocationCountryList;

    /**
     * 权限部门
     */
    private List<Long> authDeptIdList;
}
