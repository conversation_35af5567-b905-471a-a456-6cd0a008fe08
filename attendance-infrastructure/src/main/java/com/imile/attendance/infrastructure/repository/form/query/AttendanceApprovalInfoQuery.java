package com.imile.attendance.infrastructure.repository.form.query;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-26
 * @version: 1.0
 */
@Data
public class AttendanceApprovalInfoQuery {
    /**
     * 申请人ID
     */
    private Long applyUserId;

    /**
     * 被申请人ID
     */
    private List<Long> userIdList;

    /**
     * 被申请人姓名
     */
    private String userName;

    /**
     * 被申请人姓名/账号
     */
    private String userCodeOrName;

    /**
     * 被申请人部门
     */
    private Long deptId;

    /**
     * 被申请人部门列表
     */
    private List<Long> deptIds;

    /**
     * 被申请人岗位
     */
    private Long postId;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 考勤审批类型
     */
    private List<String> formTypeList;

    /**
     * 单据状态
     */
    private List<String> formStatusList;

    /**
     * 单据编码(模糊查询)
     */
    private String applicationFormCode;

    /**
     * 单据来源(排除)
     */
    private String excludeApplicationDataSource;

    private String country;

    private String leaveType;

    private String reissueCardType;


    /**
     * 权限常驻国
     */
    private List<String> authLocationCountryList;

    /**
     * 权限部门
     */
    private List<Long> authDeptIdList;

}
