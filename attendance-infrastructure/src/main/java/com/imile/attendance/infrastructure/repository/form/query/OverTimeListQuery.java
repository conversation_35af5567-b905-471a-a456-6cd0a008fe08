package com.imile.attendance.infrastructure.repository.form.query;

import com.imile.attendance.query.ResourceQuery;
import com.imile.common.constant.ValidCodeConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} OverTimeListQuery
 * {@code @since:} 2024-06-14 11:20
 * {@code @description:}
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OverTimeListQuery extends ResourceQuery implements Serializable {

    private static final long serialVersionUID = -7258303143034535466L;

    /**
     * 被申请人code
     */
    private String userCode;

    /**
     * 被申请人code集合
     */
    private List<String> userCodeList;

    /**
     * 申请人编码或姓名
     */
    private String userCodeOrName;

    /**
     * 单据编码
     */
    private String applicationCode;

    /**
     * 国家
     */
    private String country;

    /**
     * 审批类型（现在是加班：OVER_TIME，后面可能有其他的）
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<String> formTypeList;

    /**
     * 单据状态
     */
    private String formStatus;

    /**
     * 单据状态集合
     */
    private List<String> formStatusList;

    /**
     * 部门集合
     */
    private List<Long> deptIdList;

    /**
     * 岗位集合
     */
    private List<Long> postIdList;


    /**
     * 数据来源：0：手动添加 1:导入
     */
    private Integer dataSource;

    /**
     * 提交开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String startDate;

    /**
     * 提交结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String endDate;

    /**
     * 加班日期
     */
    private Long dayId;

    /**
     * 权限常驻国
     */
    private List<String> authLocationCountryList;

    /**
     * 权限部门
     */
    private List<Long> authDeptIdList;
}
