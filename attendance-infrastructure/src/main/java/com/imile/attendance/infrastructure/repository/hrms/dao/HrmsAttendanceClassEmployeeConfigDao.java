package com.imile.attendance.infrastructure.repository.hrms.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendanceClassEmployeeConfigDO;

import java.util.List;

/**
 * HRMS员工排班表 DAO 接口
 *
 * <AUTHOR> chen
 * @Date 2025/6/18
 * @Description HRMS员工排班表数据访问对象
 */
public interface HrmsAttendanceClassEmployeeConfigDao extends IService<HrmsAttendanceClassEmployeeConfigDO> {

    /**
     * 根据用户ID查询排班记录
     *
     * @param userId 用户ID
     * @return 排班记录列表
     */
    List<HrmsAttendanceClassEmployeeConfigDO> listByUserId(Long userId);

    /**
     * 根据用户ID和时间范围查询排班记录
     *
     * @param userId     用户ID
     * @param startDayId 开始dayId
     * @param endDayId   结束dayId
     * @return 排班记录列表
     */
    List<HrmsAttendanceClassEmployeeConfigDO> listByUserIdAndDateRange(Long userId, Long startDayId, Long endDayId);

    /**
     * 根据用户ID列表和时间范围查询排班记录
     *
     * @param userIdList 用户ID列表
     * @param startDayId 开始dayId
     * @param endDayId   结束dayId
     * @return 排班记录列表
     */
    List<HrmsAttendanceClassEmployeeConfigDO> listByUserIdListAndDateRange(List<Long> userIdList, Long startDayId, Long endDayId);

    /**
     * 根据dayId范围查询排班记录
     *
     * @param startDayId 开始dayId
     * @param endDayId   结束dayId
     * @return 排班记录列表
     */
    List<HrmsAttendanceClassEmployeeConfigDO> listByDayIdRange(Long startDayId, Long endDayId);

    /**
     * 根据用户ID和dayId列表查询排班记录
     *
     * @param userId    用户ID
     * @param dayIdList dayId列表
     * @return 排班记录列表
     */
    List<HrmsAttendanceClassEmployeeConfigDO> listByUserIdAndDayIds(Long userId, List<Long> dayIdList);

    /**
     * 根据班次ID查询排班记录
     *
     * @param classId 班次ID
     * @return 排班记录列表
     */
    List<HrmsAttendanceClassEmployeeConfigDO> listByClassId(Long classId);

    /**
     * 根据打卡方案ID查询排班记录
     *
     * @param punchConfigId 打卡方案ID
     * @return 排班记录列表
     */
    List<HrmsAttendanceClassEmployeeConfigDO> listByPunchConfigId(Long punchConfigId);

    /**
     * 根据数据来源查询排班记录
     *
     * @param dataSource 数据来源
     * @return 排班记录列表
     */
    List<HrmsAttendanceClassEmployeeConfigDO> listByDataSource(String dataSource);

    /**
     * 根据公司ID查询排班记录
     *
     * @param companyId 公司ID
     * @return 排班记录列表
     */
    List<HrmsAttendanceClassEmployeeConfigDO> listByCompanyId(Long companyId);

    /**
     * 查询最新的排班记录
     *
     * @return 最新排班记录列表
     */
    List<HrmsAttendanceClassEmployeeConfigDO> listLatest();

    /**
     * 根据用户ID查询最新的排班记录
     *
     * @param userId 用户ID
     * @return 最新排班记录列表
     */
    List<HrmsAttendanceClassEmployeeConfigDO> listLatestByUserId(Long userId);

    /**
     * 根据用户ID列表查询最新的排班记录
     *
     * @param userIdList 用户ID列表
     * @return 最新排班记录列表
     */
    List<HrmsAttendanceClassEmployeeConfigDO> listLatestByUserIdList(List<Long> userIdList);

    /**
     * 按国家和日期范围分页查询排班记录（用于数据迁移）
     *
     * @param page 分页参数
     * @param country 国家代码
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 分页结果
     */
    IPage<HrmsAttendanceClassEmployeeConfigDO> pageByCountryAndDateRange(Page<HrmsAttendanceClassEmployeeConfigDO> page,
                                                                          String country,
                                                                          Long startDayId,
                                                                          Long endDayId);

    /**
     * 统计按国家和日期范围的排班记录数量
     *
     * @param country 国家代码
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 记录数量
     */
    Long countByCountryAndDateRange(String country, Long startDayId, Long endDayId);

    /**
     * 按考勤组ID列表和日期范围分页查询排班记录
     * 使用PageHelper进行分页
     *
     * @param punchConfigIds 考勤组ID列表
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 排班记录列表
     */
    List<HrmsAttendanceClassEmployeeConfigDO> pageByPunchConfigIdsAndDateRange(List<Long> punchConfigIds,
                                                                               Long startDayId,
                                                                               Long endDayId);

    /**
     * 统计按考勤组ID列表和日期范围的排班记录数量
     *
     * @param punchConfigIds 考勤组ID列表
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 记录数量
     */
    Long countByPunchConfigIdsAndDateRange(List<Long> punchConfigIds,
                                           Long startDayId,
                                           Long endDayId);

    /**
     * 按用户ID列表和日期范围分页查询排班记录
     *
     * @param userIdList 用户ID列表
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 排班记录列表
     */
    List<HrmsAttendanceClassEmployeeConfigDO> pageByUserIdListAndDateRange(List<Long> userIdList,
                                                                            Long startDayId,
                                                                            Long endDayId);

    /**
     * 统计按用户ID列表和日期范围的排班记录数量
     *
     * @param userIdList 用户ID列表
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 记录数量
     */
    Long countByUserIdListAndDateRange(List<Long> userIdList,
                                       Long startDayId,
                                       Long endDayId);

    /**
     * 按日期范围分页查询历史数据（用于历史数据迁移，不按国家分组）
     *
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 排班记录列表
     */
    List<HrmsAttendanceClassEmployeeConfigDO> pageByDateRangeForHistory(Long startDayId, Long endDayId);

    /**
     * 统计按日期范围的历史数据数量（用于历史数据迁移，不按国家分组）
     *
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 记录数量
     */
    Long countByDateRangeForHistory(Long startDayId, Long endDayId);
}
