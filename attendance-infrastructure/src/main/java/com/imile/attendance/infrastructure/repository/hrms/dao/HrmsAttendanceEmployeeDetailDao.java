package com.imile.attendance.infrastructure.repository.hrms.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.hrms.query.AbnormalMigrationQuery;

import java.util.List;


/**
 * <p>
 * 员工出勤明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-14
 */
public interface HrmsAttendanceEmployeeDetailDao extends IService<HrmsAttendanceEmployeeDetailDO> {

    List<HrmsAttendanceEmployeeDetailDO> selectByCondition(AbnormalMigrationQuery query,Long lastId);

}
