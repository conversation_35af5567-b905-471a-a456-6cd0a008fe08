package com.imile.attendance.infrastructure.repository.hrms.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendanceEmployeeDetailSnapshotDO;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} HrmsAttendanceEmployeeDetailSnapshotService
 * {@code @since:} 2024-11-27 14:13
 * {@code @description:} 
 */
public interface HrmsAttendanceEmployeeDetailSnapshotDao extends IService<HrmsAttendanceEmployeeDetailSnapshotDO> {


    List<HrmsAttendanceEmployeeDetailSnapshotDO> selectByUserIdListAndDayIdList(List<Long> userIds, List<Long> preDayIdList);

    List<HrmsAttendanceEmployeeDetailSnapshotDO> selectByIds(List<Long> idList);
}
