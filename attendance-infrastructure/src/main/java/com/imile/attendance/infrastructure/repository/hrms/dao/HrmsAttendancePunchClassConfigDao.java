package com.imile.attendance.infrastructure.repository.hrms.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendancePunchClassConfigDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11 
 * @Description 考勤打卡规则班次配置表DAO接口
 */
public interface HrmsAttendancePunchClassConfigDao extends IService<HrmsAttendancePunchClassConfigDO> {

    /**
     * 根据打卡规则ID查询班次配置
     * 
     * @param punchConfigId 打卡规则ID
     * @return 班次配置列表
     */
    List<HrmsAttendancePunchClassConfigDO> listByPunchConfigId(Long punchConfigId);

    /**
     * 根据班次名称查询班次配置
     * 
     * @param className 班次名称
     * @return 班次配置
     */
    HrmsAttendancePunchClassConfigDO getByClassName(String className);

    /**
     * 根据班次类型查询班次配置
     * 
     * @param classType 班次类型
     * @return 班次配置列表
     */
    List<HrmsAttendancePunchClassConfigDO> listByClassType(Integer classType);

    /**
     * 根据状态查询班次配置
     * 
     * @param status 状态
     * @return 班次配置列表
     */
    List<HrmsAttendancePunchClassConfigDO> listByStatus(String status);

    /**
     * 根据是否跨天查询班次配置
     * 
     * @param isAcross 是否跨天
     * @return 班次配置列表
     */
    List<HrmsAttendancePunchClassConfigDO> listByIsAcross(Integer isAcross);

    /**
     * 根据打卡规则ID和班次类型查询班次配置
     * 
     * @param punchConfigId 打卡规则ID
     * @param classType 班次类型
     * @return 班次配置列表
     */
    List<HrmsAttendancePunchClassConfigDO> listByPunchConfigIdAndClassType(Long punchConfigId, Integer classType);

    /**
     * 根据打卡规则ID列表查询班次配置
     * 
     * @param punchConfigIds 打卡规则ID列表
     * @return 班次配置列表
     */
    List<HrmsAttendancePunchClassConfigDO> listByPunchConfigIds(List<Long> punchConfigIds);

    /**
     * 根据班次名称列表查询班次配置
     * 
     * @param classNames 班次名称列表
     * @return 班次配置列表
     */
    List<HrmsAttendancePunchClassConfigDO> listByClassNames(List<String> classNames);

    /**
     * 根据打卡规则ID查询最新的班次配置
     * 
     * @param punchConfigId 打卡规则ID
     * @return 班次配置列表
     */
    List<HrmsAttendancePunchClassConfigDO> listLatestByPunchConfigId(Long punchConfigId);

    /**
     * 根据打卡规则ID查询最新且启用的班次配置
     * 
     * @param punchConfigId 打卡规则ID
     * @return 班次配置列表
     */
    List<HrmsAttendancePunchClassConfigDO> listLatestAndActiveByPunchConfigId(Long punchConfigId);

    /**
     * 根据打卡规则ID列表查询最新且启用的班次配置
     * 
     * @param punchConfigIds 打卡规则ID列表
     * @return 班次配置列表
     */
    List<HrmsAttendancePunchClassConfigDO> listLatestAndActiveByPunchConfigIds(List<Long> punchConfigIds);

    /**
     * 根据班次名称查询最新的班次配置
     * 
     * @param className 班次名称
     * @return 班次配置
     */
    HrmsAttendancePunchClassConfigDO getLatestByClassName(String className);

    /**
     * 根据班次名称查询最新且启用的班次配置
     * 
     * @param className 班次名称
     * @return 班次配置
     */
    HrmsAttendancePunchClassConfigDO getLatestAndActiveByClassName(String className);
}
