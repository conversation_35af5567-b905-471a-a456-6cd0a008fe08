package com.imile.attendance.infrastructure.repository.hrms.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendancePunchClassItemConfigDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11
 * @Description 考勤打卡规则班次时间配置表DAO接口
 */
public interface HrmsAttendancePunchClassItemConfigDao extends IService<HrmsAttendancePunchClassItemConfigDO> {

    /**
     * 根据班次规则ID查询班次时间配置
     *
     * @param punchClassId 班次规则ID
     * @return 班次时间配置列表
     */
    List<HrmsAttendancePunchClassItemConfigDO> listByPunchClassId(Long punchClassId);

    /**
     * 根据状态查询班次时间配置
     *
     * @param status 状态
     * @return 班次时间配置列表
     */
    List<HrmsAttendancePunchClassItemConfigDO> listByStatus(String status);

    /**
     * 根据班次规则ID和序号查询班次时间配置
     *
     * @param punchClassId 班次规则ID
     * @param sortNo       序号
     * @return 班次时间配置
     */
    HrmsAttendancePunchClassItemConfigDO getByPunchClassIdAndSortNo(Long punchClassId, Integer sortNo);

    /**
     * 根据班次规则ID列表查询班次时间配置
     *
     * @param punchClassIds 班次规则ID列表
     * @return 班次时间配置列表
     */
    List<HrmsAttendancePunchClassItemConfigDO> listByPunchClassIds(List<Long> punchClassIds);

    /**
     * 根据班次规则ID查询最新的班次时间配置
     *
     * @param punchClassId 班次规则ID
     * @return 班次时间配置列表
     */
    List<HrmsAttendancePunchClassItemConfigDO> listLatestByPunchClassId(Long punchClassId);

    /**
     * 根据班次规则ID查询最新且启用的班次时间配置
     *
     * @param punchClassId 班次规则ID
     * @return 班次时间配置列表
     */
    List<HrmsAttendancePunchClassItemConfigDO> listLatestAndActiveByPunchClassId(Long punchClassId);

    /**
     * 根据班次规则ID列表查询最新且启用的班次时间配置
     *
     * @param punchClassIds 班次规则ID列表
     * @return 班次时间配置列表
     */
    List<HrmsAttendancePunchClassItemConfigDO> listLatestAndActiveByPunchClassIds(List<Long> punchClassIds);

    /**
     * 根据是否跨天查询班次时间配置
     *
     * @param isAcross 是否跨天
     * @return 班次时间配置列表
     */
    List<HrmsAttendancePunchClassItemConfigDO> listByIsAcross(Integer isAcross);

    /**
     * 根据班次规则ID和是否跨天查询班次时间配置
     *
     * @param punchClassId 班次规则ID
     * @param isAcross     是否跨天
     * @return 班次时间配置列表
     */
    List<HrmsAttendancePunchClassItemConfigDO> listByPunchClassIdAndIsAcross(Long punchClassId, Integer isAcross);


    HrmsAttendancePunchClassItemConfigDO selectByClassItemId(Long punchClassItemId);
}
