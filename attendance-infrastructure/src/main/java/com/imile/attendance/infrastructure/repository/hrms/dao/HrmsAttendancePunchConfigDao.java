package com.imile.attendance.infrastructure.repository.hrms.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendancePunchConfigDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11 
 * @Description 考勤打卡规则表DAO接口
 */
public interface HrmsAttendancePunchConfigDao extends IService<HrmsAttendancePunchConfigDO> {

    /**
     * 根据国家查询打卡规则,不关注是否为最新和启用的
     * 
     * @param country 国家
     * @return 打卡规则列表
     */
    List<HrmsAttendancePunchConfigDO> listByCountry(String country);

    /**
     * 根据打卡规则编码查询打卡规则
     * 
     * @param punchConfigNo 打卡规则编码
     * @return 打卡规则
     */
    HrmsAttendancePunchConfigDO getByPunchConfigNo(String punchConfigNo);

    /**
     * 根据打卡规则名称查询打卡规则
     * 
     * @param punchConfigName 打卡规则名称
     * @return 打卡规则
     */
    HrmsAttendancePunchConfigDO getByPunchConfigName(String punchConfigName);

    /**
     * 根据打卡规则类型查询打卡规则
     * 
     * @param punchConfigType 打卡规则类型
     * @return 打卡规则列表
     */
    List<HrmsAttendancePunchConfigDO> listByPunchConfigType(String punchConfigType);

    /**
     * 根据状态查询打卡规则
     * 
     * @param status 状态
     * @return 打卡规则列表
     */
    List<HrmsAttendancePunchConfigDO> listByStatus(String status);

    /**
     * 根据打卡规则编码列表查询打卡规则
     * 
     * @param punchConfigNos 打卡规则编码列表
     * @return 打卡规则列表
     */
    List<HrmsAttendancePunchConfigDO> listByPunchConfigNos(List<String> punchConfigNos);

    /**
     * 根据国家列表查询打卡规则
     * 
     * @param countries 国家列表
     * @return 打卡规则列表
     */
    List<HrmsAttendancePunchConfigDO> listByCountries(List<String> countries);

    /**
     * 根据国家和打卡规则类型查询打卡规则
     * 
     * @param country 国家
     * @param punchConfigType 打卡规则类型
     * @return 打卡规则列表
     */
    List<HrmsAttendancePunchConfigDO> listByCountryAndType(String country, String punchConfigType);

    /**
     * 查询默认打卡规则
     * 
     * @param country 国家
     * @return 默认打卡规则
     */
    HrmsAttendancePunchConfigDO getDefaultByCountry(String country);

    /**
     * 根据国家查询最新且启用的打卡规则
     * 
     * @param country 国家
     * @return 打卡规则列表
     */
    List<HrmsAttendancePunchConfigDO> listLatestAndActiveByCountry(String country);

    /**
     * 根据打卡规则编码查询最新的打卡规则
     * 
     * @param punchConfigNo 打卡规则编码
     * @return 打卡规则
     */
    HrmsAttendancePunchConfigDO getLatestByPunchConfigNo(String punchConfigNo);

    /**
     * 根据打卡规则编码查询最新且启用的打卡规则
     * 
     * @param punchConfigNo 打卡规则编码
     * @return 打卡规则
     */
    HrmsAttendancePunchConfigDO getLatestAndActiveByPunchConfigNo(String punchConfigNo);
}
