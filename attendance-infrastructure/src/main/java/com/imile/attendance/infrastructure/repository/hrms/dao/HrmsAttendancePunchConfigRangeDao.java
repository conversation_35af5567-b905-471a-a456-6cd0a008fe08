package com.imile.attendance.infrastructure.repository.hrms.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendancePunchConfigRangeDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11 
 * @Description 打卡规则配置适用范围表DAO接口
 */
public interface HrmsAttendancePunchConfigRangeDao extends IService<HrmsAttendancePunchConfigRangeDO> {

    /**
     * 根据打卡规则方案ID查询范围配置
     * 
     * @param punchConfigId 打卡规则方案ID
     * @return 范围配置列表
     */
    List<HrmsAttendancePunchConfigRangeDO> listByPunchConfigId(Long punchConfigId);

    /**
     * 根据打卡规则编码查询范围配置
     * 
     * @param punchConfigNo 打卡规则编码
     * @return 范围配置列表
     */
    List<HrmsAttendancePunchConfigRangeDO> listByPunchConfigNo(String punchConfigNo);

    /**
     * 根据范围类型查询范围配置
     * 
     * @param rangeType 范围类型
     * @return 范围配置列表
     */
    List<HrmsAttendancePunchConfigRangeDO> listByRangeType(String rangeType);

    /**
     * 根据业务ID查询范围配置
     * 
     * @param bizId 业务ID
     * @return 范围配置列表
     */
    List<HrmsAttendancePunchConfigRangeDO> listByBizId(Long bizId);

    /**
     * 根据业务ID和范围类型查询范围配置
     * 
     * @param bizId 业务ID
     * @param rangeType 范围类型
     * @return 范围配置列表
     */
    List<HrmsAttendancePunchConfigRangeDO> listByBizIdAndRangeType(Long bizId, String rangeType);

    /**
     * 根据打卡规则方案ID和范围类型查询范围配置
     * 
     * @param punchConfigId 打卡规则方案ID
     * @param rangeType 范围类型
     * @return 范围配置列表
     */
    List<HrmsAttendancePunchConfigRangeDO> listByPunchConfigIdAndRangeType(Long punchConfigId, String rangeType);

    /**
     * 根据打卡规则方案ID列表查询范围配置
     * 
     * @param punchConfigIds 打卡规则方案ID列表
     * @return 范围配置列表
     */
    List<HrmsAttendancePunchConfigRangeDO> listByPunchConfigIds(List<Long> punchConfigIds);

    /**
     * 根据业务ID列表查询范围配置
     * 
     * @param bizIds 业务ID列表
     * @return 范围配置列表
     */
    List<HrmsAttendancePunchConfigRangeDO> listByBizIds(List<Long> bizIds);

    /**
     * 根据业务ID列表和范围类型查询范围配置
     * 
     * @param bizIds 业务ID列表
     * @param rangeType 范围类型
     * @return 范围配置列表
     */
    List<HrmsAttendancePunchConfigRangeDO> listByBizIdsAndRangeType(List<Long> bizIds, String rangeType);

    /**
     * 查询最新的范围配置
     * 
     * @param punchConfigId 打卡规则方案ID
     * @return 范围配置列表
     */
    List<HrmsAttendancePunchConfigRangeDO> listLatestByPunchConfigId(Long punchConfigId);

    /**
     * 根据业务ID和范围类型查询最新的范围配置
     * 
     * @param bizId 业务ID
     * @param rangeType 范围类型
     * @return 范围配置列表
     */
    List<HrmsAttendancePunchConfigRangeDO> listLatestByBizIdAndRangeType(Long bizId, String rangeType);
}
