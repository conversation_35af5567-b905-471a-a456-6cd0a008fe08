package com.imile.attendance.infrastructure.repository.hrms.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.hrms.query.AbnormalMigrationQuery;

import java.util.List;


/**
 * <p>
 * 员工异常考勤数据表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-22
 */
public interface HrmsEmployeeAbnormalAttendanceDao extends IService<HrmsEmployeeAbnormalAttendanceDO> {

    List<HrmsEmployeeAbnormalAttendanceDO> selectAbnormal(AbnormalMigrationQuery query, Long lastId);
}
