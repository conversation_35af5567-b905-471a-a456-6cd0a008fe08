package com.imile.attendance.infrastructure.repository.hrms.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalAttendanceSnapshotDO;
import com.imile.attendance.infrastructure.repository.hrms.query.AbnormalMigrationQuery;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} HrmsEmployeeAbnormalAttendanceSnapshotDao
 * {@code @since:} 2024-11-27 14:45
 * {@code @description:}
 */
public interface HrmsEmployeeAbnormalAttendanceSnapshotDao extends IService<HrmsEmployeeAbnormalAttendanceSnapshotDO> {
    List<HrmsEmployeeAbnormalAttendanceSnapshotDO> selectByUserIdListAndDayIdList(List<Long> userIdList, List<Long> dayIdList);

    List<HrmsEmployeeAbnormalAttendanceSnapshotDO> selectByIds(List<Long> idList);

    List<HrmsEmployeeAbnormalAttendanceSnapshotDO> selectAbnormalSnapshot(AbnormalMigrationQuery query,Long lastId);
}
