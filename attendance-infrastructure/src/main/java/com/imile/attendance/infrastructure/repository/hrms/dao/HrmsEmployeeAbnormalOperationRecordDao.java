package com.imile.attendance.infrastructure.repository.hrms.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalOperationRecordDO;

import java.util.List;

/**
 * <p>
 * 员工异常考勤操作记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-28
 */
public interface HrmsEmployeeAbnormalOperationRecordDao extends IService<HrmsEmployeeAbnormalOperationRecordDO> {

    List<HrmsEmployeeAbnormalOperationRecordDO> selectByAbnormalList(List<Long> abnormalList);

}
