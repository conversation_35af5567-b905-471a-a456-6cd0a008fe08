package com.imile.attendance.infrastructure.repository.hrms.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.hrms.query.AbnormalMigrationQuery;
import com.imile.attendance.infrastructure.repository.punch.query.EmployeePunchCardRecordQuery;

import java.util.List;


/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
public interface HrmsEmployeePunchRecordDao extends IService<HrmsEmployeePunchRecordDO> {

    List<HrmsEmployeePunchRecordDO> listRecords(EmployeePunchCardRecordQuery recordQuery);

    List<HrmsEmployeePunchRecordDO> listReissueCard(AbnormalMigrationQuery abnormalMigrationQuery);

    void removeById(Long punchRecordId);
}
