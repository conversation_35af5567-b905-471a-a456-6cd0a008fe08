package com.imile.attendance.infrastructure.repository.hrms.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserDimissionRecordDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/1/20 
 * @Description
 */
public interface HrmsUserDimissionRecordDao extends IService<HrmsUserDimissionRecordDO> {

    /**
     * 根据用户id和离职状态查询离职记录
     */
    HrmsUserDimissionRecordDO getByUserId(Long userId, String dimissionStatus);

    /**
     * 查询用户离职记录
     */
    List<HrmsUserDimissionRecordDO> listByUserId(Long userId);

    /**
     * 查询用户离职记录
     */
    List<HrmsUserDimissionRecordDO> listByUserIds(List<Long> userIds);

    List<HrmsUserDimissionRecordDO> listByPage(int currentPage, int pageSize);

}
