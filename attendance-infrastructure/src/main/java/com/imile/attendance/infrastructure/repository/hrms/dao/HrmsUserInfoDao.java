package com.imile.attendance.infrastructure.repository.hrms.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserInfoDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/1/20 
 * @Description
 */
public interface HrmsUserInfoDao extends IService<HrmsUserInfoDO> {


    HrmsUserInfoDO getByCode(String userCode);

    List<HrmsUserInfoDO> listByCodes(List<String> userCodes);

    List<HrmsUserInfoDO> listByPage(int currentPage, int pageSize);

    HrmsUserInfoDO getByUserId(Long userId);

    HrmsUserInfoDO getByUserCodeCache(String userCode);

    List<HrmsUserInfoDO> listUsersByIds(List<Long> userIds);


}
