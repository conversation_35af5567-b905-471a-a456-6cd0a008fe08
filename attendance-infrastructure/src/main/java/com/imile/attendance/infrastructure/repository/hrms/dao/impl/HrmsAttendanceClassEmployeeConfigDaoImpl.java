package com.imile.attendance.infrastructure.repository.hrms.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsAttendanceClassEmployeeConfigDao;
import com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsAttendanceClassEmployeeConfigMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendanceClassEmployeeConfigDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * HRMS员工排班表 DAO 实现类
 *
 * <AUTHOR> chen
 * @Date 2025/6/18
 * @Description HRMS员工排班表数据访问对象实现
 */
@Component
@DS(Constants.TableSchema.hrms)
@RequiredArgsConstructor
public class HrmsAttendanceClassEmployeeConfigDaoImpl extends ServiceImpl<HrmsAttendanceClassEmployeeConfigMapper, HrmsAttendanceClassEmployeeConfigDO>
        implements HrmsAttendanceClassEmployeeConfigDao {

    @Override
    public List<HrmsAttendanceClassEmployeeConfigDO> listByUserId(Long userId) {
        if (Objects.isNull(userId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendanceClassEmployeeConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getUserId, userId);
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(HrmsAttendanceClassEmployeeConfigDO::getCreateDate);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendanceClassEmployeeConfigDO> listByUserIdAndDateRange(Long userId, Long startDayId, Long endDayId) {
        if (Objects.isNull(userId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendanceClassEmployeeConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getUserId, userId);
        queryWrapper.ge(Objects.nonNull(startDayId), HrmsAttendanceClassEmployeeConfigDO::getDayId, startDayId);
        queryWrapper.le(Objects.nonNull(endDayId), HrmsAttendanceClassEmployeeConfigDO::getDayId, endDayId);
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByAsc(HrmsAttendanceClassEmployeeConfigDO::getDayId);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendanceClassEmployeeConfigDO> listByUserIdListAndDateRange(List<Long> userIdList, Long startDayId, Long endDayId) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsAttendanceClassEmployeeConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(HrmsAttendanceClassEmployeeConfigDO::getUserId, userIdList);
        queryWrapper.ge(Objects.nonNull(startDayId), HrmsAttendanceClassEmployeeConfigDO::getDayId, startDayId);
        queryWrapper.le(Objects.nonNull(endDayId), HrmsAttendanceClassEmployeeConfigDO::getDayId, endDayId);
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByAsc(HrmsAttendanceClassEmployeeConfigDO::getUserId, HrmsAttendanceClassEmployeeConfigDO::getDayId);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendanceClassEmployeeConfigDO> listByDayIdRange(Long startDayId, Long endDayId) {
        if (Objects.isNull(endDayId)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsAttendanceClassEmployeeConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.ge(Objects.nonNull(startDayId), HrmsAttendanceClassEmployeeConfigDO::getDayId, startDayId);
        queryWrapper.le(HrmsAttendanceClassEmployeeConfigDO::getDayId, endDayId);
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByAsc(HrmsAttendanceClassEmployeeConfigDO::getDayId);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendanceClassEmployeeConfigDO> listByUserIdAndDayIds(Long userId, List<Long> dayIdList) {
        if (Objects.isNull(userId) || CollectionUtils.isEmpty(dayIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendanceClassEmployeeConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getUserId, userId);
        queryWrapper.in(HrmsAttendanceClassEmployeeConfigDO::getDayId, dayIdList);
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByAsc(HrmsAttendanceClassEmployeeConfigDO::getDayId);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendanceClassEmployeeConfigDO> listByClassId(Long classId) {
        if (Objects.isNull(classId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendanceClassEmployeeConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getClassId, classId);
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(HrmsAttendanceClassEmployeeConfigDO::getCreateDate);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendanceClassEmployeeConfigDO> listByPunchConfigId(Long punchConfigId) {
        if (Objects.isNull(punchConfigId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendanceClassEmployeeConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getPunchConfigId, punchConfigId);
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(HrmsAttendanceClassEmployeeConfigDO::getCreateDate);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendanceClassEmployeeConfigDO> listByDataSource(String dataSource) {
        if (StringUtils.isBlank(dataSource)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendanceClassEmployeeConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getDataSource, dataSource);
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(HrmsAttendanceClassEmployeeConfigDO::getCreateDate);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendanceClassEmployeeConfigDO> listByCompanyId(Long companyId) {
        if (Objects.isNull(companyId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendanceClassEmployeeConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getCompanyId, companyId);
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(HrmsAttendanceClassEmployeeConfigDO::getCreateDate);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendanceClassEmployeeConfigDO> listLatest() {
        LambdaQueryWrapper<HrmsAttendanceClassEmployeeConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(HrmsAttendanceClassEmployeeConfigDO::getCreateDate);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendanceClassEmployeeConfigDO> listLatestByUserId(Long userId) {
        if (Objects.isNull(userId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendanceClassEmployeeConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getUserId, userId);
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(HrmsAttendanceClassEmployeeConfigDO::getCreateDate);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendanceClassEmployeeConfigDO> listLatestByUserIdList(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsAttendanceClassEmployeeConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(HrmsAttendanceClassEmployeeConfigDO::getUserId, userIdList);
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(HrmsAttendanceClassEmployeeConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByAsc(HrmsAttendanceClassEmployeeConfigDO::getUserId);
        queryWrapper.orderByDesc(HrmsAttendanceClassEmployeeConfigDO::getCreateDate);
        return list(queryWrapper);
    }

    @Override
    public IPage<HrmsAttendanceClassEmployeeConfigDO> pageByCountryAndDateRange(Page<HrmsAttendanceClassEmployeeConfigDO> page,
                                                                                String country,
                                                                                Long startDayId,
                                                                                Long endDayId) {
        return baseMapper.pageByCountryAndDateRange(page, country, startDayId, endDayId);
    }

    @Override
    public Long countByCountryAndDateRange(String country, Long startDayId, Long endDayId) {
        return baseMapper.countByCountryAndDateRange(country, startDayId, endDayId);
    }

    @Override
    public List<HrmsAttendanceClassEmployeeConfigDO> pageByPunchConfigIdsAndDateRange(List<Long> punchConfigIds,
                                                                                      Long startDayId,
                                                                                      Long endDayId) {
        // 如果考勤组ID列表为空，直接返回空列表
        if (CollectionUtils.isEmpty(punchConfigIds)) {
            return Collections.emptyList();
        }

        // 使用PageHelper进行分页查询
        return baseMapper.pageByPunchConfigIdsAndDateRange(punchConfigIds, startDayId, endDayId);
    }

    @Override
    public Long countByPunchConfigIdsAndDateRange(List<Long> punchConfigIds,
                                                  Long startDayId,
                                                  Long endDayId) {
        // 如果考勤组ID列表为空，直接返回0
        if (CollectionUtils.isEmpty(punchConfigIds)) {
            return 0L;
        }

        return baseMapper.countByPunchConfigIdsAndDateRange(punchConfigIds, startDayId, endDayId);
    }

    @Override
    public List<HrmsAttendanceClassEmployeeConfigDO> pageByDateRangeForHistory(Long startDayId, Long endDayId) {
        return baseMapper.pageByDateRangeForHistory(startDayId, endDayId);
    }

    @Override
    public Long countByDateRangeForHistory(Long startDayId, Long endDayId) {
        return baseMapper.countByDateRangeForHistory(startDayId, endDayId);
    }

    @Override
    public List<HrmsAttendanceClassEmployeeConfigDO> pageByUserIdListAndDateRange(List<Long> userIdList,
                                                                                   Long startDayId,
                                                                                   Long endDayId) {
        // 如果用户ID列表为空，直接返回空列表
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }

        return baseMapper.pageByUserIdListAndDateRange(userIdList, startDayId, endDayId);
    }

    @Override
    public Long countByUserIdListAndDateRange(List<Long> userIdList,
                                              Long startDayId,
                                              Long endDayId) {
        // 如果用户ID列表为空，直接返回0
        if (CollectionUtils.isEmpty(userIdList)) {
            return 0L;
        }

        return baseMapper.countByUserIdListAndDateRange(userIdList, startDayId, endDayId);
    }
}
