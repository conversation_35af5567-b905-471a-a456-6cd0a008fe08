package com.imile.attendance.infrastructure.repository.hrms.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsAttendanceEmployeeDetailSnapshotDao;
import com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsAttendanceEmployeeDetailSnapshotMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendanceEmployeeDetailSnapshotDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} HrmsAttendanceEmployeeDetailSnapshotDaoImpl
 * {@code @since:} 2024-11-27 14:28
 * {@code @description:}
 */
@DS(Constants.TableSchema.hrms)
@Service
@Slf4j
public class HrmsAttendanceEmployeeDetailSnapshotDaoImpl extends ServiceImpl<HrmsAttendanceEmployeeDetailSnapshotMapper, HrmsAttendanceEmployeeDetailSnapshotDO> implements HrmsAttendanceEmployeeDetailSnapshotDao {

    @Override
    public List<HrmsAttendanceEmployeeDetailSnapshotDO> selectByUserIdListAndDayIdList(List<Long> userIdList, List<Long> dayIdList) {
        if (CollectionUtils.isEmpty(userIdList) || CollectionUtils.isEmpty(dayIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsAttendanceEmployeeDetailSnapshotDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(HrmsAttendanceEmployeeDetailSnapshotDO::getUserId, userIdList);
        queryWrapper.in(HrmsAttendanceEmployeeDetailSnapshotDO::getDayId, dayIdList);
        queryWrapper.eq(HrmsAttendanceEmployeeDetailSnapshotDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<HrmsAttendanceEmployeeDetailSnapshotDO> selectByIds(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList) ) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendanceEmployeeDetailSnapshotDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(HrmsAttendanceEmployeeDetailSnapshotDO::getId, idList);
        queryWrapper.eq(HrmsAttendanceEmployeeDetailSnapshotDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByAsc(HrmsAttendanceEmployeeDetailSnapshotDO::getCreateDate);
        return this.list(queryWrapper);
    }
}
