package com.imile.attendance.infrastructure.repository.hrms.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsAttendancePunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsAttendancePunchClassConfigMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendancePunchClassConfigDO;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11 
 * @Description 考勤打卡规则班次配置表DAO实现类
 */
@Component
@DS(Constants.TableSchema.hrms)
@RequiredArgsConstructor
public class HrmsAttendancePunchClassConfigDaoImpl extends ServiceImpl<HrmsAttendancePunchClassConfigMapper, HrmsAttendancePunchClassConfigDO>
        implements HrmsAttendancePunchClassConfigDao {

    @Override
    public List<HrmsAttendancePunchClassConfigDO> listByPunchConfigId(Long punchConfigId) {
        if (Objects.isNull(punchConfigId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchClassConfigDO::getPunchConfigId, punchConfigId)
                .eq(HrmsAttendancePunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .orderByAsc(HrmsAttendancePunchClassConfigDO::getOrderby);
        return list(queryWrapper);
    }

    @Override
    public HrmsAttendancePunchClassConfigDO getByClassName(String className) {
        if (StringUtils.isBlank(className)) {
            return null;
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchClassConfigDO::getClassName, className)
                .eq(HrmsAttendancePunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchClassConfigDO> listByClassType(Integer classType) {
        if (Objects.isNull(classType)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchClassConfigDO::getClassType, classType)
                .eq(HrmsAttendancePunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchClassConfigDO> listByStatus(String status) {
        if (StringUtils.isBlank(status)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchClassConfigDO::getStatus, status)
                .eq(HrmsAttendancePunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchClassConfigDO> listByIsAcross(Integer isAcross) {
        if (Objects.isNull(isAcross)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchClassConfigDO::getIsAcross, isAcross)
                .eq(HrmsAttendancePunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchClassConfigDO> listByPunchConfigIdAndClassType(Long punchConfigId, Integer classType) {
        if (Objects.isNull(punchConfigId) || Objects.isNull(classType)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchClassConfigDO::getPunchConfigId, punchConfigId)
                .eq(HrmsAttendancePunchClassConfigDO::getClassType, classType)
                .eq(HrmsAttendancePunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchClassConfigDO> listByPunchConfigIds(List<Long> punchConfigIds) {
        if (CollectionUtils.isEmpty(punchConfigIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(HrmsAttendancePunchClassConfigDO::getPunchConfigId, punchConfigIds)
                .eq(HrmsAttendancePunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .orderByAsc(HrmsAttendancePunchClassConfigDO::getPunchConfigId);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchClassConfigDO> listByClassNames(List<String> classNames) {
        if (CollectionUtils.isEmpty(classNames)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(HrmsAttendancePunchClassConfigDO::getClassName, classNames)
                .eq(HrmsAttendancePunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchClassConfigDO> listLatestByPunchConfigId(Long punchConfigId) {
        if (Objects.isNull(punchConfigId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchClassConfigDO::getPunchConfigId, punchConfigId)
                .eq(HrmsAttendancePunchClassConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(HrmsAttendancePunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .orderByAsc(HrmsAttendancePunchClassConfigDO::getOrderby);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchClassConfigDO> listLatestAndActiveByPunchConfigId(Long punchConfigId) {
        if (Objects.isNull(punchConfigId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchClassConfigDO::getPunchConfigId, punchConfigId)
                .eq(HrmsAttendancePunchClassConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(HrmsAttendancePunchClassConfigDO::getStatus, StatusEnum.ACTIVE.getCode())
                .eq(HrmsAttendancePunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .orderByAsc(HrmsAttendancePunchClassConfigDO::getOrderby);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchClassConfigDO> listLatestAndActiveByPunchConfigIds(List<Long> punchConfigIds) {
        if (CollectionUtils.isEmpty(punchConfigIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(HrmsAttendancePunchClassConfigDO::getPunchConfigId, punchConfigIds)
                .eq(HrmsAttendancePunchClassConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(HrmsAttendancePunchClassConfigDO::getStatus, StatusEnum.ACTIVE.getCode())
                .eq(HrmsAttendancePunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .orderByAsc(HrmsAttendancePunchClassConfigDO::getPunchConfigId)
                .orderByAsc(HrmsAttendancePunchClassConfigDO::getOrderby);
        return list(queryWrapper);
    }

    @Override
    public HrmsAttendancePunchClassConfigDO getLatestByClassName(String className) {
        if (StringUtils.isBlank(className)) {
            return null;
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchClassConfigDO::getClassName, className)
                .eq(HrmsAttendancePunchClassConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(HrmsAttendancePunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public HrmsAttendancePunchClassConfigDO getLatestAndActiveByClassName(String className) {
        if (StringUtils.isBlank(className)) {
            return null;
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchClassConfigDO::getClassName, className)
                .eq(HrmsAttendancePunchClassConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(HrmsAttendancePunchClassConfigDO::getStatus, StatusEnum.ACTIVE.getCode())
                .eq(HrmsAttendancePunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return getOne(queryWrapper);
    }
}
