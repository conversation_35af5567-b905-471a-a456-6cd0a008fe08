package com.imile.attendance.infrastructure.repository.hrms.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsAttendancePunchClassItemConfigDao;
import com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsAttendancePunchClassItemConfigMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendancePunchClassItemConfigDO;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11
 * @Description 考勤打卡规则班次时间配置表DAO实现类
 */
@Component
@DS(Constants.TableSchema.hrms)
@RequiredArgsConstructor
public class HrmsAttendancePunchClassItemConfigDaoImpl extends ServiceImpl<HrmsAttendancePunchClassItemConfigMapper, HrmsAttendancePunchClassItemConfigDO>
        implements HrmsAttendancePunchClassItemConfigDao {

    @Override
    public List<HrmsAttendancePunchClassItemConfigDO> listByPunchClassId(Long punchClassId) {
        if (Objects.isNull(punchClassId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassItemConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchClassItemConfigDO::getPunchClassId, punchClassId)
                .eq(HrmsAttendancePunchClassItemConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .orderByAsc(HrmsAttendancePunchClassItemConfigDO::getSortNo);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchClassItemConfigDO> listByStatus(String status) {
        if (StringUtils.isBlank(status)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassItemConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchClassItemConfigDO::getStatus, status)
                .eq(HrmsAttendancePunchClassItemConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public HrmsAttendancePunchClassItemConfigDO getByPunchClassIdAndSortNo(Long punchClassId, Integer sortNo) {
        if (Objects.isNull(punchClassId) || Objects.isNull(sortNo)) {
            return null;
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassItemConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchClassItemConfigDO::getPunchClassId, punchClassId)
                .eq(HrmsAttendancePunchClassItemConfigDO::getSortNo, sortNo)
                .eq(HrmsAttendancePunchClassItemConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchClassItemConfigDO> listByPunchClassIds(List<Long> punchClassIds) {
        if (CollectionUtils.isEmpty(punchClassIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassItemConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(HrmsAttendancePunchClassItemConfigDO::getPunchClassId, punchClassIds)
                .eq(HrmsAttendancePunchClassItemConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchClassItemConfigDO> listLatestByPunchClassId(Long punchClassId) {
        if (Objects.isNull(punchClassId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassItemConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchClassItemConfigDO::getPunchClassId, punchClassId)
                .eq(HrmsAttendancePunchClassItemConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(HrmsAttendancePunchClassItemConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .orderByAsc(HrmsAttendancePunchClassItemConfigDO::getSortNo);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchClassItemConfigDO> listLatestAndActiveByPunchClassId(Long punchClassId) {
        if (Objects.isNull(punchClassId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassItemConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchClassItemConfigDO::getPunchClassId, punchClassId)
                .eq(HrmsAttendancePunchClassItemConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(HrmsAttendancePunchClassItemConfigDO::getStatus, StatusEnum.ACTIVE.getCode())
                .eq(HrmsAttendancePunchClassItemConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .orderByAsc(HrmsAttendancePunchClassItemConfigDO::getSortNo);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchClassItemConfigDO> listLatestAndActiveByPunchClassIds(List<Long> punchClassIds) {
        if (CollectionUtils.isEmpty(punchClassIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassItemConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(HrmsAttendancePunchClassItemConfigDO::getPunchClassId, punchClassIds)
                .eq(HrmsAttendancePunchClassItemConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(HrmsAttendancePunchClassItemConfigDO::getStatus, StatusEnum.ACTIVE.getCode())
                .eq(HrmsAttendancePunchClassItemConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .orderByAsc(HrmsAttendancePunchClassItemConfigDO::getPunchClassId)
                .orderByAsc(HrmsAttendancePunchClassItemConfigDO::getSortNo);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchClassItemConfigDO> listByIsAcross(Integer isAcross) {
        if (Objects.isNull(isAcross)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassItemConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchClassItemConfigDO::getIsAcross, isAcross)
                .eq(HrmsAttendancePunchClassItemConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchClassItemConfigDO> listByPunchClassIdAndIsAcross(Long punchClassId, Integer isAcross) {
        if (Objects.isNull(punchClassId) || Objects.isNull(isAcross)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassItemConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchClassItemConfigDO::getPunchClassId, punchClassId)
                .eq(HrmsAttendancePunchClassItemConfigDO::getIsAcross, isAcross)
                .eq(HrmsAttendancePunchClassItemConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .orderByAsc(HrmsAttendancePunchClassItemConfigDO::getSortNo);
        return list(queryWrapper);
    }

    @Override
    public HrmsAttendancePunchClassItemConfigDO selectByClassItemId(Long punchClassItemId) {
        if (Objects.isNull(punchClassItemId)) {
            return null;
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassItemConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchClassItemConfigDO::getId, punchClassItemId)
                .eq(HrmsAttendancePunchClassItemConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return getOne(queryWrapper);
    }
}
