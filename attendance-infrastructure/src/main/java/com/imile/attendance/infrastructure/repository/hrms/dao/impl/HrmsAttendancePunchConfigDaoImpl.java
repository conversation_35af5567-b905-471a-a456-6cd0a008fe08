package com.imile.attendance.infrastructure.repository.hrms.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsAttendancePunchConfigDao;
import com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsAttendancePunchConfigMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendancePunchConfigDO;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11 
 * @Description 考勤打卡规则表DAO实现类
 */
@Component
@DS(Constants.TableSchema.hrms)
@RequiredArgsConstructor
public class HrmsAttendancePunchConfigDaoImpl extends ServiceImpl<HrmsAttendancePunchConfigMapper, HrmsAttendancePunchConfigDO>
        implements HrmsAttendancePunchConfigDao {

    @Override
    public List<HrmsAttendancePunchConfigDO> listByCountry(String country) {
        if (StringUtils.isBlank(country)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchConfigDO::getCountry, country)
                .eq(HrmsAttendancePunchConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public HrmsAttendancePunchConfigDO getByPunchConfigNo(String punchConfigNo) {
        if (StringUtils.isBlank(punchConfigNo)) {
            return null;
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchConfigDO::getPunchConfigNo, punchConfigNo)
                .eq(HrmsAttendancePunchConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public HrmsAttendancePunchConfigDO getByPunchConfigName(String punchConfigName) {
        if (StringUtils.isBlank(punchConfigName)) {
            return null;
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchConfigDO::getPunchConfigName, punchConfigName)
                .eq(HrmsAttendancePunchConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchConfigDO> listByPunchConfigType(String punchConfigType) {
        if (StringUtils.isBlank(punchConfigType)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchConfigDO::getPunchConfigType, punchConfigType)
                .eq(HrmsAttendancePunchConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchConfigDO> listByStatus(String status) {
        if (StringUtils.isBlank(status)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchConfigDO::getStatus, status)
                .eq(HrmsAttendancePunchConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchConfigDO> listByPunchConfigNos(List<String> punchConfigNos) {
        if (CollectionUtils.isEmpty(punchConfigNos)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(HrmsAttendancePunchConfigDO::getPunchConfigNo, punchConfigNos)
                .eq(HrmsAttendancePunchConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchConfigDO> listByCountries(List<String> countries) {
        if (CollectionUtils.isEmpty(countries)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(HrmsAttendancePunchConfigDO::getCountry, countries)
                .eq(HrmsAttendancePunchConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchConfigDO> listByCountryAndType(String country, String punchConfigType) {
        if (StringUtils.isBlank(country) || StringUtils.isBlank(punchConfigType)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchConfigDO::getCountry, country)
                .eq(HrmsAttendancePunchConfigDO::getPunchConfigType, punchConfigType)
                .eq(HrmsAttendancePunchConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public HrmsAttendancePunchConfigDO getDefaultByCountry(String country) {
        if (StringUtils.isBlank(country)) {
            return null;
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchConfigDO::getCountry, country)
                .eq(HrmsAttendancePunchConfigDO::getIsDefault, BusinessConstant.Y)
                .eq(HrmsAttendancePunchConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchConfigDO> listLatestAndActiveByCountry(String country) {
        if (StringUtils.isBlank(country)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchConfigDO::getCountry, country)
                .eq(HrmsAttendancePunchConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(HrmsAttendancePunchConfigDO::getStatus, StatusEnum.ACTIVE.getCode())
                .eq(HrmsAttendancePunchConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public HrmsAttendancePunchConfigDO getLatestByPunchConfigNo(String punchConfigNo) {
        if (StringUtils.isBlank(punchConfigNo)) {
            return null;
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchConfigDO::getPunchConfigNo, punchConfigNo)
                .eq(HrmsAttendancePunchConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(HrmsAttendancePunchConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public HrmsAttendancePunchConfigDO getLatestAndActiveByPunchConfigNo(String punchConfigNo) {
        if (StringUtils.isBlank(punchConfigNo)) {
            return null;
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchConfigDO::getPunchConfigNo, punchConfigNo)
                .eq(HrmsAttendancePunchConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(HrmsAttendancePunchConfigDO::getStatus, StatusEnum.ACTIVE.getCode())
                .eq(HrmsAttendancePunchConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return getOne(queryWrapper);
    }
}
