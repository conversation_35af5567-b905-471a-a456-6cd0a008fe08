package com.imile.attendance.infrastructure.repository.hrms.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsAttendancePunchConfigRangeDao;
import com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsAttendancePunchConfigRangeMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendancePunchConfigRangeDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11 
 * @Description 打卡规则配置适用范围表DAO实现类
 */
@Component
@DS(Constants.TableSchema.hrms)
@RequiredArgsConstructor
public class HrmsAttendancePunchConfigRangeDaoImpl extends ServiceImpl<HrmsAttendancePunchConfigRangeMapper, HrmsAttendancePunchConfigRangeDO>
        implements HrmsAttendancePunchConfigRangeDao {

    @Override
    public List<HrmsAttendancePunchConfigRangeDO> listByPunchConfigId(Long punchConfigId) {
        if (Objects.isNull(punchConfigId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchConfigRangeDO::getPunchConfigId, punchConfigId)
                .eq(HrmsAttendancePunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchConfigRangeDO> listByPunchConfigNo(String punchConfigNo) {
        if (StringUtils.isBlank(punchConfigNo)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchConfigRangeDO::getPunchConfigNo, punchConfigNo)
                .eq(HrmsAttendancePunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchConfigRangeDO> listByRangeType(String rangeType) {
        if (StringUtils.isBlank(rangeType)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchConfigRangeDO::getRangeType, rangeType)
                .eq(HrmsAttendancePunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchConfigRangeDO> listByBizId(Long bizId) {
        if (Objects.isNull(bizId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchConfigRangeDO::getBizId, bizId)
                .eq(HrmsAttendancePunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchConfigRangeDO> listByBizIdAndRangeType(Long bizId, String rangeType) {
        if (Objects.isNull(bizId) || StringUtils.isBlank(rangeType)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchConfigRangeDO::getBizId, bizId)
                .eq(HrmsAttendancePunchConfigRangeDO::getRangeType, rangeType)
                .eq(HrmsAttendancePunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchConfigRangeDO> listByPunchConfigIdAndRangeType(Long punchConfigId, String rangeType) {
        if (Objects.isNull(punchConfigId) || StringUtils.isBlank(rangeType)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchConfigRangeDO::getPunchConfigId, punchConfigId)
                .eq(HrmsAttendancePunchConfigRangeDO::getRangeType, rangeType)
                .eq(HrmsAttendancePunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchConfigRangeDO> listByPunchConfigIds(List<Long> punchConfigIds) {
        if (CollectionUtils.isEmpty(punchConfigIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(HrmsAttendancePunchConfigRangeDO::getPunchConfigId, punchConfigIds)
                .eq(HrmsAttendancePunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchConfigRangeDO> listByBizIds(List<Long> bizIds) {
        if (CollectionUtils.isEmpty(bizIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(HrmsAttendancePunchConfigRangeDO::getBizId, bizIds)
                .eq(HrmsAttendancePunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchConfigRangeDO> listByBizIdsAndRangeType(List<Long> bizIds, String rangeType) {
        if (CollectionUtils.isEmpty(bizIds) || StringUtils.isBlank(rangeType)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(HrmsAttendancePunchConfigRangeDO::getBizId, bizIds)
                .eq(HrmsAttendancePunchConfigRangeDO::getRangeType, rangeType)
                .eq(HrmsAttendancePunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchConfigRangeDO> listLatestByPunchConfigId(Long punchConfigId) {
        if (Objects.isNull(punchConfigId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchConfigRangeDO::getPunchConfigId, punchConfigId)
                .eq(HrmsAttendancePunchConfigRangeDO::getIsLatest, BusinessConstant.Y)
                .eq(HrmsAttendancePunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchConfigRangeDO> listLatestByBizIdAndRangeType(Long bizId, String rangeType) {
        if (Objects.isNull(bizId) || StringUtils.isBlank(rangeType)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsAttendancePunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendancePunchConfigRangeDO::getBizId, bizId)
                .eq(HrmsAttendancePunchConfigRangeDO::getRangeType, rangeType)
                .eq(HrmsAttendancePunchConfigRangeDO::getIsLatest, BusinessConstant.Y)
                .eq(HrmsAttendancePunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }
}
