package com.imile.attendance.infrastructure.repository.hrms.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsEmployeeAbnormalAttendanceDao;
import com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsEmployeeAbnormalAttendanceMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.hrms.query.AbnormalMigrationQuery;
import com.imile.common.enums.IsDeleteEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 员工异常考勤数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-22
 */
@DS(Constants.TableSchema.hrms)
@Service
public class HrmsEmployeeAbnormalAttendanceDaoImpl extends ServiceImpl<HrmsEmployeeAbnormalAttendanceMapper, HrmsEmployeeAbnormalAttendanceDO> implements HrmsEmployeeAbnormalAttendanceDao {


    @Override
    public List<HrmsEmployeeAbnormalAttendanceDO> selectAbnormal(AbnormalMigrationQuery query,Long lastId) {
        LambdaQueryWrapper<HrmsEmployeeAbnormalAttendanceDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.gt(Objects.nonNull(lastId), HrmsEmployeeAbnormalAttendanceDO::getId, lastId);
        wrapper.ge(Objects.nonNull(query.getStartDayId()), HrmsEmployeeAbnormalAttendanceDO::getDayId, query.getStartDayId());
        wrapper.le(Objects.nonNull(query.getEndDayId()), HrmsEmployeeAbnormalAttendanceDO::getDayId, query.getEndDayId());
        wrapper.in(CollectionUtils.isNotEmpty(query.getUserIdList()), HrmsEmployeeAbnormalAttendanceDO::getUserId, query.getUserIdList());
        wrapper.in(CollectionUtils.isNotEmpty(query.getDeptIdList()), HrmsEmployeeAbnormalAttendanceDO::getDeptId, query.getDeptIdList());
        wrapper.in(CollectionUtils.isNotEmpty(query.getCountryList()), HrmsEmployeeAbnormalAttendanceDO::getLocationCountry, query.getCountryList());
        wrapper.orderByAsc(HrmsEmployeeAbnormalAttendanceDO::getId);
        wrapper.last("limit 1000");
        return this.list(wrapper);
    }
}
