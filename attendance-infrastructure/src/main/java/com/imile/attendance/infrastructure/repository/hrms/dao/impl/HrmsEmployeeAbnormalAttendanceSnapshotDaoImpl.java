package com.imile.attendance.infrastructure.repository.hrms.dao.impl;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsEmployeeAbnormalAttendanceSnapshotDao;
import com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsEmployeeAbnormalAttendanceSnapshotMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalAttendanceSnapshotDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.hrms.query.AbnormalMigrationQuery;
import com.imile.common.enums.IsDeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * {@code @author:} allen
 * {@code @className:} HrmsEmployeeAbnormalAttendanceSnapshotDaoImpl
 * {@code @since:} 2024-11-27 14:46
 * {@code @description:}
 */
@DS(Constants.TableSchema.hrms)
@Service
@Slf4j
public class HrmsEmployeeAbnormalAttendanceSnapshotDaoImpl extends ServiceImpl<HrmsEmployeeAbnormalAttendanceSnapshotMapper, HrmsEmployeeAbnormalAttendanceSnapshotDO> implements HrmsEmployeeAbnormalAttendanceSnapshotDao {

    @Override
    public List<HrmsEmployeeAbnormalAttendanceSnapshotDO> selectByUserIdListAndDayIdList(List<Long> userIdList, List<Long> dayIdList) {
        if (CollectionUtils.isEmpty(userIdList) || CollectionUtils.isEmpty(dayIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsEmployeeAbnormalAttendanceSnapshotDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(HrmsEmployeeAbnormalAttendanceSnapshotDO::getUserId, userIdList);
        queryWrapper.in(HrmsEmployeeAbnormalAttendanceSnapshotDO::getDayId, dayIdList);
        queryWrapper.eq(HrmsEmployeeAbnormalAttendanceSnapshotDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<HrmsEmployeeAbnormalAttendanceSnapshotDO> selectByIds(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList) ) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsEmployeeAbnormalAttendanceSnapshotDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(HrmsEmployeeAbnormalAttendanceSnapshotDO::getId, idList);
        queryWrapper.eq(HrmsEmployeeAbnormalAttendanceSnapshotDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByAsc(HrmsEmployeeAbnormalAttendanceSnapshotDO::getCreateDate);
        return this.list(queryWrapper);
    }

    @Override
    public List<HrmsEmployeeAbnormalAttendanceSnapshotDO> selectAbnormalSnapshot(AbnormalMigrationQuery query,Long lastId) {
        LambdaQueryWrapper<HrmsEmployeeAbnormalAttendanceSnapshotDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.gt(Objects.nonNull(lastId), HrmsEmployeeAbnormalAttendanceSnapshotDO::getId, lastId);
        wrapper.ge(Objects.nonNull(query.getStartDayId()), HrmsEmployeeAbnormalAttendanceSnapshotDO::getDayId, query.getStartDayId());
        wrapper.le(Objects.nonNull(query.getEndDayId()), HrmsEmployeeAbnormalAttendanceSnapshotDO::getDayId, query.getEndDayId());
        wrapper.in(CollectionUtils.isNotEmpty(query.getUserIdList()), HrmsEmployeeAbnormalAttendanceSnapshotDO::getUserId, query.getUserIdList());
        wrapper.in(CollectionUtils.isNotEmpty(query.getDeptIdList()), HrmsEmployeeAbnormalAttendanceSnapshotDO::getDeptId, query.getDeptIdList());
        wrapper.in(CollectionUtils.isNotEmpty(query.getCountryList()), HrmsEmployeeAbnormalAttendanceSnapshotDO::getLocationCountry, query.getCountryList());
        wrapper.orderByAsc(HrmsEmployeeAbnormalAttendanceSnapshotDO::getId);
        wrapper.last("limit 1000");
        return this.list(wrapper);
    }
}
