package com.imile.attendance.infrastructure.repository.hrms.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsEmployeeAbnormalOperationRecordDao;
import com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsEmployeeAbnormalOperationRecordMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalOperationRecordDO;
import com.imile.common.enums.IsDeleteEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 员工异常考勤操作记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-28
 */
@DS(Constants.TableSchema.hrms)
@Service
public class HrmsEmployeeAbnormalOperationRecordDaoImpl extends ServiceImpl<HrmsEmployeeAbnormalOperationRecordMapper, HrmsEmployeeAbnormalOperationRecordDO> implements HrmsEmployeeAbnormalOperationRecordDao {
    @Override
    public List<HrmsEmployeeAbnormalOperationRecordDO> selectByAbnormalList(List<Long> abnormalList) {
        if (CollectionUtils.isEmpty(abnormalList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsEmployeeAbnormalOperationRecordDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(HrmsEmployeeAbnormalOperationRecordDO::getAbnormalId, abnormalList);
        wrapper.orderByAsc(HrmsEmployeeAbnormalOperationRecordDO::getCreateDate);
        return this.list(wrapper);
    }
}
