package com.imile.attendance.infrastructure.repository.hrms.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsEmployeePunchRecordDao;
import com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsEmployeePunchRecordMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.hrms.query.AbnormalMigrationQuery;
import com.imile.attendance.infrastructure.repository.punch.query.EmployeePunchCardRecordQuery;
import com.imile.common.enums.IsDeleteEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
@DS(Constants.TableSchema.hrms)
@Service
public class HrmsEmployeePunchRecordDaoImpl extends ServiceImpl<HrmsEmployeePunchRecordMapper, HrmsEmployeePunchRecordDO> implements HrmsEmployeePunchRecordDao {

    @Override
    public List<HrmsEmployeePunchRecordDO> listRecords(EmployeePunchCardRecordQuery query) {
        LambdaQueryWrapper<HrmsEmployeePunchRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        if (query.getStartTime() != null) {
            queryWrapper.ge(HrmsEmployeePunchRecordDO::getPunchTime, query.getStartTime());
        }
        if (query.getEndTime() != null) {
            queryWrapper.lt(HrmsEmployeePunchRecordDO::getPunchTime, query.getEndTime());
        }
        if (StringUtils.isNotBlank(query.getCountry())) {
            queryWrapper.eq(HrmsEmployeePunchRecordDO::getCountry, query.getCountry());
        }
        if (CollectionUtils.isNotEmpty(query.getCountryList())) {
            queryWrapper.in(HrmsEmployeePunchRecordDO::getCountry, query.getCountryList());
        }
        if (CollectionUtils.isNotEmpty(query.getDeptIdList())) {
            queryWrapper.in(HrmsEmployeePunchRecordDO::getDeptId, query.getDeptIdList());
        }
        if (StringUtils.isNotBlank(query.getUserCode())) {
            queryWrapper.eq(HrmsEmployeePunchRecordDO::getUserCode, query.getUserCode());
        }
        if (CollectionUtils.isNotEmpty(query.getUserCodes())) {
            queryWrapper.in(HrmsEmployeePunchRecordDO::getUserCode, query.getUserCodes());
        }
        if (Objects.nonNull(query.getStartDayId())) {
            queryWrapper.ge(HrmsEmployeePunchRecordDO::getDayId, query.getStartDayId());
        }
        if (Objects.nonNull(query.getEndDayId())) {
            queryWrapper.le(HrmsEmployeePunchRecordDO::getDayId, query.getEndDayId());
        }
        if (StringUtils.isNotBlank(query.getDayId())) {
            queryWrapper.eq(HrmsEmployeePunchRecordDO::getDayId, query.getDayId());
        }
        if (CollectionUtils.isNotEmpty(query.getDayIds())) {
            queryWrapper.in(HrmsEmployeePunchRecordDO::getDayId, query.getDayIds());
        }
        if (Objects.nonNull(query.getLastId())) {
            queryWrapper.gt(HrmsEmployeePunchRecordDO::getId, query.getLastId());
        }
        queryWrapper.eq(HrmsEmployeePunchRecordDO::getFromNewSystem, BusinessConstant.N);
        queryWrapper.orderByAsc(HrmsEmployeePunchRecordDO::getId);
        queryWrapper.last("limit 1000");
        return this.list(queryWrapper);
    }

    @Override
    public List<HrmsEmployeePunchRecordDO> listReissueCard(AbnormalMigrationQuery abnormalMigrationQuery) {
        return baseMapper.listReissueCard(abnormalMigrationQuery);
    }

    @Override
    public void removeById(Long punchRecordId) {
        if (Objects.isNull(punchRecordId)) {
            return;
        }
        LambdaQueryWrapper<HrmsEmployeePunchRecordDO> updateWrapper = Wrappers.lambdaQuery();
        updateWrapper.eq(HrmsEmployeePunchRecordDO::getId, punchRecordId);

        HrmsEmployeePunchRecordDO model = new HrmsEmployeePunchRecordDO();
        model.setIsDelete(IsDeleteEnum.YES.getCode());
        update(model, updateWrapper);
    }
}
