package com.imile.attendance.infrastructure.repository.hrms.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserDimissionRecordDao;
import com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsUserDimissionRecordMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserDimissionRecordDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/1/20 
 * @Description
 */
@Component
@DS(Constants.TableSchema.hrms)
@RequiredArgsConstructor
public class HrmsUserDimissionRecordDaoImpl extends ServiceImpl<HrmsUserDimissionRecordMapper, HrmsUserDimissionRecordDO> implements HrmsUserDimissionRecordDao {
    @Override
    public HrmsUserDimissionRecordDO getByUserId(Long userId, String dimissionStatus) {
        if (userId == null || StringUtils.isEmpty(dimissionStatus)) {
            return null;
        }
        LambdaQueryWrapper<HrmsUserDimissionRecordDO> queryWrapper = Wrappers.lambdaQuery(HrmsUserDimissionRecordDO.class);
        queryWrapper.eq(HrmsUserDimissionRecordDO::getUserId, userId)
                .eq(HrmsUserDimissionRecordDO::getDimissionStatus, dimissionStatus)
                .eq(HrmsUserDimissionRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.getOne(queryWrapper);
    }

    @Override
    public List<HrmsUserDimissionRecordDO> listByUserId(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsUserDimissionRecordDO> queryWrapper = Wrappers.lambdaQuery(HrmsUserDimissionRecordDO.class);
        queryWrapper.eq(HrmsUserDimissionRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(HrmsUserDimissionRecordDO::getUserId, userId);
        return this.list(queryWrapper);
    }

    @Override
    public List<HrmsUserDimissionRecordDO> listByUserIds(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsUserDimissionRecordDO> queryWrapper = Wrappers.lambdaQuery(HrmsUserDimissionRecordDO.class);
        queryWrapper.eq(HrmsUserDimissionRecordDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .in(HrmsUserDimissionRecordDO::getUserId, userIds);
        return this.list(queryWrapper);
    }

    @Override
    public List<HrmsUserDimissionRecordDO> listByPage(int currentPage, int pageSize) {
        PageInfo<HrmsUserDimissionRecordDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }
}
