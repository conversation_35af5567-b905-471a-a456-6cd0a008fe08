package com.imile.attendance.infrastructure.repository.hrms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendanceClassEmployeeConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * HRMS员工排班表 Mapper 接口
 *
 * <AUTHOR> chen
 * @Date 2025/6/18
 * @Description HRMS员工排班表数据访问层
 */
@Mapper
@Repository
public interface HrmsAttendanceClassEmployeeConfigMapper extends BaseMapper<HrmsAttendanceClassEmployeeConfigDO> {

    /**
     * 按国家和日期范围分页查询排班记录（用于数据迁移）
     *
     * @param page 分页参数
     * @param country 国家代码
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 分页结果
     */
    IPage<HrmsAttendanceClassEmployeeConfigDO> pageByCountryAndDateRange(Page<HrmsAttendanceClassEmployeeConfigDO> page,
                                                                          @Param("country") String country,
                                                                          @Param("startDayId") Long startDayId,
                                                                          @Param("endDayId") Long endDayId);

    /**
     * 统计按国家和日期范围的排班记录数量
     *
     * @param country 国家代码
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 记录数量
     */
    Long countByCountryAndDateRange(@Param("country") String country,
                                    @Param("startDayId") Long startDayId,
                                    @Param("endDayId") Long endDayId);

    /**
     * 按考勤组ID列表和日期范围分页查询排班记录（优化版本，避免大表联查）
     *
     * @param punchConfigIds 考勤组ID列表
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 排班记录列表
     */
    List<HrmsAttendanceClassEmployeeConfigDO> pageByPunchConfigIdsAndDateRange(@Param("punchConfigIds") List<Long> punchConfigIds,
                                                                                @Param("startDayId") Long startDayId,
                                                                                @Param("endDayId") Long endDayId);

    /**
     * 统计按考勤组ID列表和日期范围的排班记录数量
     *
     * @param punchConfigIds 考勤组ID列表
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 记录数量
     */
    Long countByPunchConfigIdsAndDateRange(@Param("punchConfigIds") List<Long> punchConfigIds,
                                           @Param("startDayId") Long startDayId,
                                           @Param("endDayId") Long endDayId);

    /**
     * 按日期范围分页查询历史数据（用于历史数据迁移，不按国家分组）
     *
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 排班记录列表
     */
    List<HrmsAttendanceClassEmployeeConfigDO> pageByDateRangeForHistory(@Param("startDayId") Long startDayId,
                                                                         @Param("endDayId") Long endDayId);

    /**
     * 统计按日期范围的历史数据数量（用于历史数据迁移，不按国家分组）
     *
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 记录数量
     */
    Long countByDateRangeForHistory(@Param("startDayId") Long startDayId,
                                    @Param("endDayId") Long endDayId);

    /**
     * 按用户ID列表和日期范围分页查询排班记录
     *
     * @param userIdList 用户ID列表
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 排班记录列表
     */
    List<HrmsAttendanceClassEmployeeConfigDO> pageByUserIdListAndDateRange(@Param("userIdList") List<Long> userIdList,
                                                                            @Param("startDayId") Long startDayId,
                                                                            @Param("endDayId") Long endDayId);

    /**
     * 统计按用户ID列表和日期范围的排班记录数量
     *
     * @param userIdList 用户ID列表
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 记录数量
     */
    Long countByUserIdListAndDateRange(@Param("userIdList") List<Long> userIdList,
                                       @Param("startDayId") Long startDayId,
                                       @Param("endDayId") Long endDayId);
}
