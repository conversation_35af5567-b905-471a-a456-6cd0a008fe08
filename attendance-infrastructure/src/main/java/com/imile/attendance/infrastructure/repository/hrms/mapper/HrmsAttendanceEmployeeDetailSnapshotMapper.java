package com.imile.attendance.infrastructure.repository.hrms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendanceEmployeeDetailSnapshotDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * {@code @author:} allen
 * {@code @className:} HrmsAttendanceEmployeeDetailSnapshotMapper
 * {@code @since:} 2024-11-27 14:13
 * {@code @description:} 
 */
@Mapper
@Repository
public interface HrmsAttendanceEmployeeDetailSnapshotMapper extends BaseMapper<HrmsAttendanceEmployeeDetailSnapshotDO> {

}