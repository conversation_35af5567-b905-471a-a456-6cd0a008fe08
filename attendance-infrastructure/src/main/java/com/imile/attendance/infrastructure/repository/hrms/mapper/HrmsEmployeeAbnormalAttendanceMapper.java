package com.imile.attendance.infrastructure.repository.hrms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.abnormal.dto.EmployeeAbnormalAttendanceDTO;
import com.imile.attendance.infrastructure.repository.abnormal.query.EmployeeAbnormalAttendancePageQuery;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalAttendanceDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 员工异常考勤数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-22
 */
@Mapper
public interface HrmsEmployeeAbnormalAttendanceMapper extends BaseMapper<HrmsEmployeeAbnormalAttendanceDO> {


    /**
     * 异常考勤分页查询
     */
    List<EmployeeAbnormalAttendanceDTO> list(EmployeeAbnormalAttendancePageQuery query);

}
