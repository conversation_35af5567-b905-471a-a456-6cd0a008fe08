package com.imile.attendance.infrastructure.repository.hrms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalAttendanceSnapshotDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;


/**
 * {@code @author:} allen
 * {@code @className:} HrmsEmployeeAbnormalAttendanceSnapshotMapper
 * {@code @since:} 2024-11-27 14:36
 * {@code @description:} 
 */
@Mapper
@Repository
public interface HrmsEmployeeAbnormalAttendanceSnapshotMapper extends BaseMapper<HrmsEmployeeAbnormalAttendanceSnapshotDO> {

}