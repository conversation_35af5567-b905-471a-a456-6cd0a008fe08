package com.imile.attendance.infrastructure.repository.hrms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.hrms.query.AbnormalMigrationQuery;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
@Mapper
@Repository
public interface HrmsEmployeePunchRecordMapper extends BaseMapper<HrmsEmployeePunchRecordDO> {
    List<HrmsEmployeePunchRecordDO> listReissueCard(AbnormalMigrationQuery abnormalMigrationQuery);
}
