package com.imile.attendance.infrastructure.repository.hrms.modle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * HRMS员工排班表
 *
 * <AUTHOR> chen
 * @Date 2025/6/18
 * @Description HRMS系统中的员工排班配置表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_attendance_class_employee_config")
public class HrmsAttendanceClassEmployeeConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 考勤日历方案id
     */
    private Long attendanceConfigId;

    /**
     * 打卡方案ID
     */
    private Long punchConfigId;

    /**
     * 班次时间
     */
    private Date classTime;

    /**
     * 排班次的dayId
     */
    private Long dayId;

    /**
     * 班次id
     */
    private Long classId;

    /**
     * 当天的打卡规则，可以为早班，晚班，也可以是OFF PH 等
     */
    private String dayPunchType;

    /**
     * 数据来源:页面操作/批量排班/系统排班/循环排班/排班导入
     */
    private String dataSource;

    /**
     * 是否最新
     */
    private Integer isLatest;

    /**
     * 排序
     */
    private BigDecimal orderby;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 判断是否有班次
     *
     * @return true-有班次，false-无班次
     */
    public Boolean haveClassId() {
        return Objects.nonNull(this.classId) && this.classId > 0;
    }

    /**
     * 判断是否有打卡方案
     *
     * @return true-有打卡方案，false-无打卡方案
     */
    public Boolean havePunchConfigId() {
        return Objects.nonNull(this.punchConfigId) && this.punchConfigId > 0;
    }
}
