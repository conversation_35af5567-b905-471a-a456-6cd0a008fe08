package com.imile.attendance.infrastructure.repository.hrms.modle;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 员工出勤明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_attendance_employee_detail")
public class HrmsAttendanceEmployeeDetailDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户(员工)id
     */
    private Long userId;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 年
     */
    private Long year;

    /**
     * 月份
     */
    private Long month;

    /**
     * 日
     */
    private Integer day;

    /**
     * 日期
     */
    private Date date;

    private Long dayId;

    /**
     * 数据来源 页面手动添加：add  系统定时任务获取：system
     */
    private String dataSource;

    /**
     * 出勤类型 PRESENT 应出勤日，WEEKEND 休息日 ，HOLIDAY 节假日
     */
    private String attendanceType;

    /**
     * 具体类型
     */
    private String concreteType;

    /**
     * 是否出勤
     */
    private Integer isAttendance;

    /**
     * 加班小时数
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal overtimeHours;

    /**
     * 出勤小时数
     */
    private BigDecimal attendanceHours;

    /**
     * 考勤开始时间
     */
    private Date attendanceStartTime;

    /**
     * 考勤结束时间
     */
    private Date attendanceEndTime;


    /**
     * 司机派件量
     */
    private Integer deliveryCount;

    /**
     * 派件签收数量
     */
    private Integer deliveredCount;
    /**
     * 收件数量
     */
    private Integer pickUpCount;


    /**
     * 备注说明
     */
    private String remark;

    /**
     * 排序
     */
    private BigDecimal orderby;
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal attendanceRate;

    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 岗位id
     */
    private Long postId;

    /**
     * 请假类型
     */
    private String leaveType;

    /**
     * 请假类型-百分比日薪
     */
    private BigDecimal leavePercentSalary;

    /**
     * 请假小时数
     */
    private BigDecimal leaveHours;

    /**
     * 阶段
     */
    private Integer stage;

    /**
     * 派件类型(司机和仓内员工)
     */
    private String scanType;

    /**
     * 图片储存路径
     */
    private String picturePath;

    /**
     * 备注
     */
    private String attendanceRemark;


    /**
     * 请假时长(分钟)
     */
    private BigDecimal leaveMinutes;

    /**
     * 出勤时长(分钟)
     */
    private BigDecimal attendanceMinutes;

    /**
     * 加班时长(分钟)
     */
    private BigDecimal overtimeMinutes;

    /**
     * 法定工作时长
     */
    private BigDecimal legalWorkingHours;

    /**
     * 请假/外勤时用到，通过哪个申请单生成
     */
    private Long formId;

    /**
     * 班次id
     */
    private Long classId;

    /**
     * 实际打卡时长(分钟)
     * 目前在多时段场景有所应用
     */
    private BigDecimal actualPunchMinutes;

    /**
     * 多时段补的时长(分钟)
     */
    private BigDecimal addDurationMinutes;
}
