package com.imile.attendance.infrastructure.repository.hrms.modle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * {@code @author:} allen
 * {@code @className:} HrmsAttendanceEmployeeDetailSnapshot
 * {@code @since:} 2024-11-27 14:13
 * {@code @description:} 
 */
/**
 * 员工出勤明细表快照
 */
@ApiModel(description="员工出勤明细表快照")
@Data
@EqualsAndHashCode(callSuper=true)
@TableName("hrms_attendance_employee_detail_snapshot")
public class HrmsAttendanceEmployeeDetailSnapshotDO extends BaseDO {

    @ApiModelProperty(value="")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
    * 用户(员工)id
    */
    @ApiModelProperty(value="用户(员工)id")
    private Long userId;

    /**
     * 常驻地国家
     */
    @ApiModelProperty(value="常驻地国家")
    private String locationCountry;

    /**
    * 年
    */
    @ApiModelProperty(value="年")
    private Integer year;

    /**
    * 月份
    */
    @ApiModelProperty(value="月份")
    private Integer month;

    /**
    * 日
    */
    @ApiModelProperty(value="日")
    private Integer day;

    /**
    * 日期
    */
    @ApiModelProperty(value="日期")
    private Date date;

    @ApiModelProperty(value="")
    private Long dayId;

    /**
    * 数据来源
    */
    @ApiModelProperty(value="数据来源")
    private String dataSource;

    /**
    * 出勤类型 PRESENT 应出勤日，WEEKEND 休息日 ，HOLIDAY 节假日
    */
    @ApiModelProperty(value="出勤类型 PRESENT 应出勤日，WEEKEND 休息日 ，HOLIDAY 节假日")
    private String attendanceType;

    /**
    * 具体类型
    */
    @ApiModelProperty(value="具体类型")
    private String concreteType;

    /**
    * 是否出勤
    */
    @ApiModelProperty(value="是否出勤")
    private Integer isAttendance;

    /**
    * 加班小时数
    */
    @ApiModelProperty(value="加班小时数")
    private BigDecimal overtimeHours;

    /**
    * 出勤小时数
    */
    @ApiModelProperty(value="出勤小时数")
    private BigDecimal attendanceHours;

    /**
    * 考勤开始时间
    */
    @ApiModelProperty(value="考勤开始时间")
    private Date attendanceStartTime;

    /**
    * 考勤结束时间
    */
    @ApiModelProperty(value="考勤结束时间")
    private Date attendanceEndTime;

    /**
    * 司机派件量
    */
    @ApiModelProperty(value="司机派件量")
    private Integer deliveryCount;

    /**
    * 派件类型(司机和仓内员工)
    */
    @ApiModelProperty(value="派件类型(司机和仓内员工)")
    private String scanType;

    /**
    * 派件签收数量
    */
    @ApiModelProperty(value="派件签收数量")
    private Integer deliveredCount;

    /**
    * 收件数量
    */
    @ApiModelProperty(value="收件数量")
    private Integer pickUpCount;

    /**
    * 备注说明
    */
    @ApiModelProperty(value="备注说明")
    private String remark;

    /**
    * 排序
    */
    @ApiModelProperty(value="排序")
    private BigDecimal orderby;

    /**
    * 当天出勤率
    */
    @ApiModelProperty(value="当天出勤率")
    private BigDecimal attendanceRate;

    /**
    * 公司id
    */
    @ApiModelProperty(value="公司id")
    private Long companyId;

    /**
    * 部门id
    */
    @ApiModelProperty(value="部门id")
    private Long deptId;

    /**
    * 岗位id
    */
    @ApiModelProperty(value="岗位id")
    private Long postId;

    /**
    * 请假类型
    */
    @ApiModelProperty(value="请假类型")
    private String leaveType;

    /**
    * 假期阶段
    */
    @ApiModelProperty(value="假期阶段")
    private Integer stage;

    /**
    * 请假类型-百分比日薪
    */
    @ApiModelProperty(value="请假类型-百分比日薪")
    private BigDecimal leavePercentSalary;

    /**
    * 请假小时数
    */
    @ApiModelProperty(value="请假小时数")
    private BigDecimal leaveHours;

    /**
    * 图片储存路径
    */
    @ApiModelProperty(value="图片储存路径")
    private String picturePath;

    /**
    * 备注
    */
    @ApiModelProperty(value="备注")
    private String attendanceRemark;

    /**
    * 请假时长(分钟)
    */
    @ApiModelProperty(value="请假时长(分钟)")
    private BigDecimal leaveMinutes;

    /**
    * 出勤时长(分钟)
    */
    @ApiModelProperty(value="出勤时长(分钟)")
    private BigDecimal attendanceMinutes;

    /**
    * 加班时长(分钟)
    */
    @ApiModelProperty(value="加班时长(分钟)")
    private BigDecimal overtimeMinutes;

    /**
    * 当天的法定工作小时
    */
    @ApiModelProperty(value="当天的法定工作小时")
    private BigDecimal legalWorkingHours;

    /**
    * 请假/外勤时用到，通过哪个申请单生成
    */
    @ApiModelProperty(value="请假/外勤时用到，通过哪个申请单生成")
    private Long formId;

    @ApiModelProperty(value="")
    private Date lastModifyTime;
}