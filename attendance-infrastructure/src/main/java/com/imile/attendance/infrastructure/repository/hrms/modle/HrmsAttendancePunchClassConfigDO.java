package com.imile.attendance.infrastructure.repository.hrms.modle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 考勤打卡规则班次配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_attendance_punch_class_config")
public class HrmsAttendancePunchClassConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 打卡规则ID
     */
    private Long punchConfigId;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 班次类型: 0表示未选择，1:早班,2:晚班
     */
    private Integer classType;

    /**
     * 上班时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private Date punchInTime;

    /**
     * 下班时间 仅有时分秒信息，形如：1970-01-01 18:00:00
     */
    private Date punchOutTime;

    /**
     * 最早上班打卡时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private Date earliestPunchInTime;

    /**
     * 最晚下班打卡时间 仅有时分秒信息，形如：1970-01-01 19:00:00
     */
    private Date latestPunchOutTime;

    /**
     * 是否跨天 跨天是指打卡时间是第二天：0：不跨天，1：跨天
     */
    private Integer isAcross;

    /**
     * 上下班打卡时间间隔 单位：小时
     */
    private BigDecimal punchTimeInterval;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否最新
     */
    private Integer isLatest;

    /**
     * 排序
     */
    private BigDecimal orderby;

    /**
     * 上班和最早打卡时间间隔
     */
    private BigDecimal punchInTimeInterval;

    /**
     * 出勤时长
     */
    private BigDecimal attendanceHours;


    /**
     * 法定工作时长
     */
    private BigDecimal legalWorkingHours;
}
