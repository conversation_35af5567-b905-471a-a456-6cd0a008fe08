package com.imile.attendance.infrastructure.repository.hrms.modle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 考勤打卡规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_attendance_punch_config")
public class HrmsAttendancePunchConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 国家
     */
    private String country;

    /**
     * 打卡规则编码
     */
    private String punchConfigNo;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;

    /**
     * 打卡规则类型 自由上下班、固定上下班、按班次上班
     */
    private String punchConfigType;

    /**
     * 是否为默认
     */
    private Integer isDefault;

    /**
     * 加班配置 JSON格式
     */
    private String overtimeConfig;

    /**
     * 最大补卡天数
     */
    private Long maxRepunchDays;

    /**
     * 最大补卡次数
     */
    private Long maxRepunchNumber;

    /**
     * 生效时间
     */
    private Date effectTime;

    /**
     * 失效时间 2099-12-31 23:23:59
     */
    private Date expireTime;

    /**
     * 状态 ACTIVE、DISABLED
     */
    private String status;

    /**
     * 使用部门
     */
    private String deptIds;

    /**
     * 打卡方式
     */
    private String punchCardType;

    /**
     * 是否最新
     */
    private Integer isLatest;

    /**
     * 排序
     */
    private BigDecimal orderby;

    /**
     * 主负责人
     */
    private String principalUserCode;

    /**
     * 子负责人
     */
    private String subUserCodes;

    public List<Long> listDeptIds() {
        if (StringUtils.isNotBlank(this.deptIds)) {
            return Arrays.asList((Long[]) ConvertUtils.convert(this.deptIds.split(","), Long.class));
        }
        return new ArrayList<>();
    }
}
