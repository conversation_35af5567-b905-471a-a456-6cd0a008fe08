package com.imile.attendance.infrastructure.repository.hrms.modle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 打卡规则配置适用范围表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_attendance_punch_config_range")
public class HrmsAttendancePunchConfigRangeDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 业务ID 部门id、用户ID
     */
    private Long bizId;

    /**
     * 打卡规则方案ID
     */
    private Long punchConfigId;

    /**
     * 打卡规则编码
     */
    private String punchConfigNo;

    /**
     * 范围类型 DEPT,USER
     */
    private String rangeType;

    /**
     * 是否需要打卡 0:false,1:true
     */
    private Integer isNeedPunch;

    /**
     * 生效时间
     */
    private Date effectTime;

    /**
     * 失效时间
     */
    private Date expireTime;

    /**
     * 是否为最新
     */
    private Integer isLatest;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 排序
     */
    private BigDecimal orderby;


}
