package com.imile.attendance.infrastructure.repository.hrms.modle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 员工异常考勤数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_employee_abnormal_attendance")
public class HrmsEmployeeAbnormalAttendanceDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 岗位id
     */
    private Long postId;

    /**
     * 考勤日期
     */
    private Date date;

    /**
     * 考勤日期
     */
    private Long dayId;

    /**
     * 汇报上级id
     */
    private Long leaderId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 员工工种 司机：driver 仓内：warehouse 其他：other
     */
    private String staffType;

    /**
     * 员工类型
     */
    private String employeeType;

    /**
     * 扫描数量 (司机ofd数量/仓内扫描数量)
     */
    private Integer scanCount;

    /**
     * 扫描类型(司机和仓内员工)
     */
    private String scanType;

    /**
     * 异常类型
     */
    private String abnormalType;

    /**
     * 状态 (待处理：pending 已处理：processed)
     */
    private String status;

    /**
     * 考勤日类型 工作日:presentDay 休息日:offDay 节假日:holiday 休息日:leaveDay
     */
    private String attendanceType;

    /**
     * 打卡规则id
     */
    private Long punchConfigId;

    /**
     * 出勤时长(分钟)
     */
    private BigDecimal attendanceDuration;

    /**
     * 缺勤时长(分钟)
     */
    private BigDecimal absenceDuration;

    /**
     * 打卡班次id
     */
    private Long punchClassConfigId;

    /**
     * 打卡班次时段id
     */
    private Long punchClassItemConfigId;

    /*    *//**
     * 迟到时长(分钟)
     *//*
        private BigDecimal lateDuration;

    *//**
     * 早退时长(分钟)
     *//*
    private BigDecimal earlyLeaveDuration;*/

    /**
     * 扩展字段
     */
    private String extend;

}
