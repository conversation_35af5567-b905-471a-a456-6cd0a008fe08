package com.imile.attendance.infrastructure.repository.hrms.modle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * {@code @author:} allen
 * {@code @className:} HrmsEmployeeAbnormalAttendanceSnapshot
 * {@code @since:} 2024-11-27 14:36
 * {@code @description:} 
 */
/**
 * 员工异常考勤数据表快照
 */
@ApiModel(description="员工异常考勤数据表快照")
@Data
@EqualsAndHashCode(callSuper=true)
@TableName("hrms_employee_abnormal_attendance_snapshot")
public class HrmsEmployeeAbnormalAttendanceSnapshotDO extends BaseDO {

    @ApiModelProperty(value="")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
    * 公司id
    */
    @ApiModelProperty(value="公司id")
    private Long companyId;

    /**
    * 部门id
    */
    @ApiModelProperty(value="部门id")
    private Long deptId;

    /**
    * 岗位id
    */
    @ApiModelProperty(value="岗位id")
    private Long postId;

    /**
    * 考勤日期
    */
    @ApiModelProperty(value="考勤日期")
    private Date date;

    /**
    * 考勤日期
    */
    @ApiModelProperty(value="考勤日期")
    private Long dayId;

    /**
    * 汇报上级id
    */
    @ApiModelProperty(value="汇报上级id")
    private Long leaderId;

    /**
    * 用户id
    */
    @ApiModelProperty(value="用户id")
    private Long userId;

    /**
     * 常驻地国家
     */
    @ApiModelProperty(value="常驻地国家")
    private String locationCountry;

    /**
    * 员工工种 司机：driver 仓内：warehouse 其他：other
    */
    @ApiModelProperty(value="员工工种 司机：driver 仓内：warehouse 其他：other")
    private String staffType;

    /**
    * 员工类型
    */
    @ApiModelProperty(value="员工类型")
    private String employeeType;

    /**
    * 扫描数量 (司机ofd数量/仓内扫描数量)
    */
    @ApiModelProperty(value="扫描数量 (司机ofd数量/仓内扫描数量)")
    private String scanCount;

    /**
    * 扫描类型(司机和仓内员工)
    */
    @ApiModelProperty(value="扫描类型(司机和仓内员工)")
    private String scanType;

    /**
    * 状态 (待处理：pending 已处理：processed)
    */
    @ApiModelProperty(value="状态 (待处理：pending 已处理：processed)")
    private String status;

    /**
    * 异常类型
    */
    @ApiModelProperty(value="异常类型")
    private String abnormalType;

    /**
    * 出勤类型
    */
    @ApiModelProperty(value="出勤类型")
    private String attendanceType;

    /**
    * 打卡规则id
    */
    @ApiModelProperty(value="打卡规则id")
    private Long punchConfigId;

    /**
    * 打卡班次id
    */
    @ApiModelProperty(value="打卡班次id")
    private Long punchClassConfigId;

    /**
    * 打卡班次时段id
    */
    @ApiModelProperty(value="打卡班次时段id")
    private Long punchClassItemConfigId;

    @ApiModelProperty(value="")
    private String extend;

    /**
    * 出勤时长(分钟)
    */
    @ApiModelProperty(value="出勤时长(分钟)")
    private BigDecimal attendanceDuration;

    /**
    * 缺勤时长(分钟)
    */
    @ApiModelProperty(value="缺勤时长(分钟)")
    private BigDecimal absenceDuration;

    @ApiModelProperty(value="")
    private Date lastModifyTime;
}