package com.imile.attendance.infrastructure.repository.hrms.modle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 员工异常考勤操作记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_employee_abnormal_operation_record")
public class HrmsEmployeeAbnormalOperationRecordDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 异常考勤id
     */
    private Long abnormalId;

    /**
     * 操作类型(请假/补卡/外勤/确认异常)AttendanceAbnormalOperationTypeEnum
     */
    private String operationType;

    /**
     * 申请单据ID
     */
    private Long formId;

    /**
     * 原因说明
     */
    private String reason;

    /**
     * 附件
     */
    private String attachment;

}
