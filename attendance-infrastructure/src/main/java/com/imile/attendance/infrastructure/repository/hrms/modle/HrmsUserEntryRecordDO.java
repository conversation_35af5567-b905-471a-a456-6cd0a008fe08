package com.imile.attendance.infrastructure.repository.hrms.modle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 用户入职记录表 系统-员工
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_user_entry_record")
public class HrmsUserEntryRecordDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 预计入职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entryDate;

    /**
     * 试用期月数
     */
    private Integer probationMonths;

    /**
     * 实际入职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date confirmDate;

    /**
     * 入职状态 入职状态：待发送邀请、待员工登记、待确认入职、已入职、已放弃入职
     */
    private String entryStatus;

    /**
     * 入职快照（JSON格式）
     */
    private String entrySnapshot;

    /**
     * 备注说明
     */
    private String remark;


    /**
     * 排序
     */
    private BigDecimal orderby;


}
