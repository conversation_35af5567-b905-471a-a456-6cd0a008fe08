package com.imile.attendance.infrastructure.repository.hrms.query;

import com.imile.common.enums.IsDeleteEnum;
import com.imile.genesis.api.enums.WorkStatusEnum;
import lombok.Builder;
import lombok.Data;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/21 
 * @Description
 */
@Data
@Builder
public class UserDaoQuery {

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户id
     */
    private List<Long> userIds;

    /**
     * 企业邮箱
     */
    private String email;

    /**
     * 用户名
     */
    private String userName;
    /**
     * 用户名英文（公司花名）
     */
    private String userNameEn;
    /**
     * 用户id
     */
    private Long leaderUserId;
    /**
     * leaderIds
     */
    private List<Long> leaderUserIds;
    /**
     * 工号
     */
    private String workNo;
    private List<String> workNos;
    /**
     * 用户编码
     */
    private String userCode;
    /**
     * 是否删除数据
     */
    private Integer isDelete;
    /**
     * 员工工作状态 在职/离职
     */
    private String workStatus;
    /**
     * 员工工作状态 在职/离职
     */
    private List<String> workStatusList;
    /**
     * 账号启用状态
     */
    private String status;
    /**
     * 是否需要做子公司隔离
     */
    private Boolean isNeedSeparate;
    /**
     * 用户编码
     */
    private List<String> userCodes;
    /**
     * 部门id
     */
    private Collection<Long> deptIds;
    /**
     * 网点id
     */
    private Collection<Long> ocIds;
    /**
     * 邮箱列表
     */
    private Collection<String> emails;
    /**
     * 岗位id
     */
    private Collection<Long> postIds;

    /**
     * 是否司机 0：否 1：是
     */
    private Integer isDriver;
    /**
     * 是否仓内 0：否 1：是
     */
    private Integer isWarehouseStaff;
    /**
     * 供应商id
     */
    private Long vendorId;


    private List<Long> vendorIdList;

    private List<String> vendorCodeList;


    /**
     * 账号或姓名
     */
    private String codeOrName;

    /**
     * 账号或姓名(模糊搜索)
     */
    private String codeOrNameLike;

    /**
     * 业务节点id
     */
    private Long bizModelId;

    /**
     * 结算主体列表
     */
    private List<String> settlementCenterCodes;

    /**
     * 结算主体列表
     */
    private List<String> employeeTypes;


    /**
     * 职级id
     */
    private Long gradeId;

    /**
     * 停用时间
     */
    private Date disabledDate;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 省份
     */
    private List<String> localProviceList;

    /**
     * 来源
     */
    private String dataSource;

    /**
     * 国籍
     */
    private List<String> countryCodeList;

    /**
     * 是否全球派遣
     */
    private Integer isGlobalRelocation;


    private String originCountry;
    private List<String> originCountryList;

    private String locationCountry;
    private List<String> locationCountryList;
    private List<String> locationCityList;



    /**
     * 获取默认工作状态为ON_JOB
     *
     * @return
     */
    public String getWorkStatus() {
        return workStatus == null ? WorkStatusEnum.ON_JOB.getCode() : workStatus;
    }


    public Integer getIsDelete() {
        return isDelete == null ? IsDeleteEnum.NO.getCode() : isDelete;
    }
}
