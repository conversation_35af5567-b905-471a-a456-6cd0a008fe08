package com.imile.attendance.infrastructure.repository.log.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.log.model.LogOperationRecordDO;
import com.imile.attendance.infrastructure.repository.log.query.LogRecordPageQuery;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31 
 * @Description
 */
@Mapper
@Repository
public interface LogOperationRecordMapper extends BaseMapper<LogOperationRecordDO> {

    List<LogOperationRecordDO> listPage(LogRecordPageQuery logRecordPageQuery);
}

