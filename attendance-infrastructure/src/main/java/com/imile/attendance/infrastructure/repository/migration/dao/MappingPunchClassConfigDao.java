package com.imile.attendance.infrastructure.repository.migration.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11 
 * @Description 考勤班次映射表DAO接口
 */
public interface MappingPunchClassConfigDao extends IService<MappingPunchClassConfigDO> {

    /**
     * 根据国家查询班次映射
     * 
     * @param country 国家
     * @return 班次映射列表
     */
    List<MappingPunchClassConfigDO> listByCountry(String country);

    /**
     * 根据HR考勤组ID查询班次映射
     * 
     * @param hrPunchConfigId HR考勤组ID
     * @return 班次映射列表
     */
    List<MappingPunchClassConfigDO> listByHrPunchConfigId(Long hrPunchConfigId);

    /**
     * 根据HR班次ID查询班次映射
     * 
     * @param hrPunchClassId HR班次ID
     * @return 班次映射
     */
    MappingPunchClassConfigDO getByHrPunchClassId(Long hrPunchClassId);

    /**
     * 根据新考勤班次ID查询班次映射
     * 
     * @param punchClassConfigId 新考勤班次ID
     * @return 班次映射
     */
    MappingPunchClassConfigDO getByPunchClassConfigId(Long punchClassConfigId);

    /**
     * 根据HR考勤组ID列表查询班次映射
     * 
     * @param hrPunchConfigIds HR考勤组ID列表
     * @return 班次映射列表
     */
    List<MappingPunchClassConfigDO> listByHrPunchConfigIds(List<Long> hrPunchConfigIds);

    /**
     * 根据HR班次ID列表查询班次映射
     * 
     * @param hrPunchClassIds HR班次ID列表
     * @return 班次映射列表
     */
    List<MappingPunchClassConfigDO> listByHrPunchClassIds(List<Long> hrPunchClassIds);

    /**
     * 根据新考勤班次ID列表查询班次映射
     * 
     * @param punchClassConfigIds 新考勤班次ID列表
     * @return 班次映射列表
     */
    List<MappingPunchClassConfigDO> listByPunchClassConfigIds(List<Long> punchClassConfigIds);

    boolean removeByPunchClassConfigId(Long punchClassConfigId);
}
