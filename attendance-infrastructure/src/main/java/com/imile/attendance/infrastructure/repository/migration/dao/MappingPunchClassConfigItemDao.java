package com.imile.attendance.infrastructure.repository.migration.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigItemDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11 
 * @Description 考勤班次时间配置映射表DAO接口
 */
public interface MappingPunchClassConfigItemDao extends IService<MappingPunchClassConfigItemDO> {

    /**
     * 根据国家查询班次时间配置映射
     * 
     * @param country 国家
     * @return 班次时间配置映射列表
     */
    List<MappingPunchClassConfigItemDO> listByCountry(String country);

    /**
     * 根据HR考勤组ID查询班次时间配置映射
     * 
     * @param hrPunchConfigId HR考勤组ID
     * @return 班次时间配置映射列表
     */
    List<MappingPunchClassConfigItemDO> listByHrPunchConfigId(Long hrPunchConfigId);

    /**
     * 根据HR班次ID查询班次时间配置映射
     * 
     * @param hrPunchClassId HR班次ID
     * @return 班次时间配置映射列表
     */
    List<MappingPunchClassConfigItemDO> listByHrPunchClassId(Long hrPunchClassId);

    /**
     * 根据HR班次时间配置ID查询班次时间配置映射
     * 
     * @param hrPunchClassItemId HR班次时间配置ID
     * @return 班次时间配置映射
     */
    MappingPunchClassConfigItemDO getByHrPunchClassItemId(Long hrPunchClassItemId);

    /**
     * 根据新考勤班次ID查询班次时间配置映射
     * 
     * @param punchClassConfigId 新考勤班次ID
     * @return 班次时间配置映射列表
     */
    List<MappingPunchClassConfigItemDO> listByPunchClassConfigId(Long punchClassConfigId);

    /**
     * 根据新考勤班次时间ID查询班次时间配置映射
     * 
     * @param punchClassConfigItemId 新考勤班次时间ID
     * @return 班次时间配置映射
     */
    MappingPunchClassConfigItemDO getByPunchClassConfigItemId(Long punchClassConfigItemId);

    /**
     * 根据HR班次时间配置ID列表查询班次时间配置映射
     * 
     * @param hrPunchClassItemIds HR班次时间配置ID列表
     * @return 班次时间配置映射列表
     */
    List<MappingPunchClassConfigItemDO> listByHrPunchClassItemIds(List<Long> hrPunchClassItemIds);

    /**
     * 根据新考勤班次ID列表查询班次时间配置映射
     * 
     * @param punchClassConfigIds 新考勤班次ID列表
     * @return 班次时间配置映射列表
     */
    List<MappingPunchClassConfigItemDO> listByPunchClassConfigIds(List<Long> punchClassConfigIds);

    boolean removeByPunchClassConfigId(Long punchClassConfigId);
}
