package com.imile.attendance.infrastructure.repository.migration.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigRangeDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11 
 * @Description 考勤班次范围映射表DAO接口
 */
public interface MappingPunchClassConfigRangeDao extends IService<MappingPunchClassConfigRangeDO> {

    /**
     * 根据HR考勤组范围ID查询班次范围映射
     * 
     * @param hrPunchConfigRangeId HR考勤组范围ID
     * @return 班次范围映射
     */
    MappingPunchClassConfigRangeDO getByHrPunchConfigRangeId(Long hrPunchConfigRangeId);

    /**
     * 根据HR考勤组ID查询班次范围映射
     * 
     * @param hrPunchConfigId HR考勤组ID
     * @return 班次范围映射列表
     */
    List<MappingPunchClassConfigRangeDO> listByHrPunchConfigId(Long hrPunchConfigId);

    /**
     * 根据新考勤班次范围ID查询班次范围映射
     * 
     * @param punchClassConfigRangeId 新考勤班次范围ID
     * @return 班次范围映射
     */
    MappingPunchClassConfigRangeDO getByPunchClassConfigRangeId(Long punchClassConfigRangeId);

    /**
     * 根据新考勤班次ID查询班次范围映射
     * 
     * @param punchClassConfigId 新考勤班次ID
     * @return 班次范围映射列表
     */
    List<MappingPunchClassConfigRangeDO> listByPunchClassConfigId(Long punchClassConfigId);

    /**
     * 根据HR考勤组ID列表查询班次范围映射
     * 
     * @param hrPunchConfigIds HR考勤组ID列表
     * @return 班次范围映射列表
     */
    List<MappingPunchClassConfigRangeDO> listByHrPunchConfigIds(List<Long> hrPunchConfigIds);

    /**
     * 根据新考勤班次ID列表查询班次范围映射
     * 
     * @param punchClassConfigIds 新考勤班次ID列表
     * @return 班次范围映射列表
     */
    List<MappingPunchClassConfigRangeDO> listByPunchClassConfigIds(List<Long> punchClassConfigIds);

    /**
     * 根据HR范围类型查询班次范围映射
     * 
     * @param hrRangeType HR范围类型
     * @return 班次范围映射列表
     */
    List<MappingPunchClassConfigRangeDO> listByHrRangeType(String hrRangeType);

    /**
     * 根据HR范围状态查询班次范围映射
     * 
     * @param hrRangeStatus HR范围状态
     * @return 班次范围映射列表
     */
    List<MappingPunchClassConfigRangeDO> listByHrRangeStatus(String hrRangeStatus);

    /**
     * 根据新考勤班次ID删除班次范围映射
     *
     * @param punchClassConfigId 新考勤班次ID
     * @return 删除是否成功
     */
    boolean removeByPunchClassConfigId(Long punchClassConfigId);
}
