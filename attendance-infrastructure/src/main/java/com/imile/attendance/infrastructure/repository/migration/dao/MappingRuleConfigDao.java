package com.imile.attendance.infrastructure.repository.migration.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.migration.model.MappingRuleConfigDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11
 * @Description 考勤规则映射表DAO接口
 */
public interface MappingRuleConfigDao extends IService<MappingRuleConfigDO> {

    /**
     * 根据国家查询规则映射
     *
     * @param country 国家
     * @return 规则映射列表
     */
    List<MappingRuleConfigDO> listByCountry(String country);

    /**
     * 根据HR考勤配置ID查询规则映射
     *
     * @param hrPunchConfigId HR考勤配置ID
     * @return 规则映射列表
     */
    List<MappingRuleConfigDO> listByHrPunchConfigId(Long hrPunchConfigId);

    /**
     * 根据规则类型查询规则映射
     *
     * @param ruleType 规则类型
     * @return 规则映射列表
     */
    List<MappingRuleConfigDO> listByRuleType(String ruleType);

    /**
     * 根据新考勤规则ID查询规则映射
     *
     * @param ruleId 新考勤规则ID
     * @return 规则映射
     */
    MappingRuleConfigDO getByRuleId(Long ruleId);

    /**
     * 根据状态查询规则映射
     *
     * @param status 状态
     * @return 规则映射列表
     */
    List<MappingRuleConfigDO> listByStatus(String status);

    /**
     * 根据HR考勤配置ID和规则类型查询规则映射
     *
     * @param hrPunchConfigId HR考勤配置ID
     * @param ruleType 规则类型
     * @return 规则映射
     */
    MappingRuleConfigDO getByHrPunchConfigIdAndRuleType(Long hrPunchConfigId, String ruleType);

    /**
     * 根据HR考勤配置ID列表查询规则映射
     *
     * @param hrPunchConfigIds HR考勤配置ID列表
     * @return 规则映射列表
     */
    List<MappingRuleConfigDO> listByHrPunchConfigIds(List<Long> hrPunchConfigIds);

    /**
     * 根据新考勤规则ID列表查询规则映射
     *
     * @param ruleIds 新考勤规则ID列表
     * @return 规则映射列表
     */
    List<MappingRuleConfigDO> listByRuleIds(List<Long> ruleIds);

    /**
     * 根据国家和规则类型查询最新且启用的规则映射
     *
     * @param country 国家
     * @param ruleType 规则类型
     * @return 规则映射列表
     */
    List<MappingRuleConfigDO> listLatestAndActiveByCountryAndRuleType(String country, String ruleType);

    /**
     * 根据HR考勤配置ID查询最新且启用的规则映射
     *
     * @param hrPunchConfigId HR考勤配置ID
     * @return 规则映射列表
     */
    List<MappingRuleConfigDO> listLatestAndActiveByHrPunchConfigId(Long hrPunchConfigId);
}