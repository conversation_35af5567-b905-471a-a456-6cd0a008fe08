package com.imile.attendance.infrastructure.repository.migration.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.migration.model.MappingRuleConfigRangeDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11 
 * @Description 考勤规则范围映射表DAO接口
 */
public interface MappingRuleConfigRangeDao extends IService<MappingRuleConfigRangeDO> {

    /**
     * 根据HR考勤组ID查询范围映射
     * 
     * @param hrPunchConfigId HR考勤组ID
     * @return 范围映射列表
     */
    List<MappingRuleConfigRangeDO> listByHrPunchConfigId(Long hrPunchConfigId);

    /**
     * 根据HR考勤组范围ID查询范围映射
     * 
     * @param hrPunchConfigRangeId HR考勤组范围ID
     * @return 范围映射
     */
    MappingRuleConfigRangeDO getByHrPunchConfigRangeId(Long hrPunchConfigRangeId);

    /**
     * 根据新考勤规则ID查询范围映射
     * 
     * @param ruleConfigId 新考勤规则ID
     * @return 范围映射列表
     */
    List<MappingRuleConfigRangeDO> listByRuleConfigId(Long ruleConfigId);

    /**
     * 根据新考勤规则范围ID查询范围映射
     * 
     * @param ruleConfigRangeId 新考勤规则范围ID
     * @return 范围映射
     */
    MappingRuleConfigRangeDO getByRuleConfigRangeId(Long ruleConfigRangeId);

    /**
     * 根据HR考勤组ID列表查询范围映射
     * 
     * @param hrPunchConfigIds HR考勤组ID列表
     * @return 范围映射列表
     */
    List<MappingRuleConfigRangeDO> listByHrPunchConfigIds(List<Long> hrPunchConfigIds);

    /**
     * 根据新考勤规则ID列表查询范围映射
     * 
     * @param ruleConfigIds 新考勤规则ID列表
     * @return 范围映射列表
     */
    List<MappingRuleConfigRangeDO> listByRuleConfigIds(List<Long> ruleConfigIds);
}
