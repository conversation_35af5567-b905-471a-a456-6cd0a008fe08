package com.imile.attendance.infrastructure.repository.migration.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingPunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.migration.mapper.MappingPunchClassConfigMapper;
import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11 
 * @Description 考勤班次映射表DAO实现类
 */
@Component
@RequiredArgsConstructor
public class MappingPunchClassConfigDaoImpl extends ServiceImpl<MappingPunchClassConfigMapper, MappingPunchClassConfigDO>
        implements MappingPunchClassConfigDao {

    @Override
    public List<MappingPunchClassConfigDO> listByCountry(String country) {
        if (StringUtils.isBlank(country)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingPunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingPunchClassConfigDO::getCountry, country)
                .eq(MappingPunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<MappingPunchClassConfigDO> listByHrPunchConfigId(Long hrPunchConfigId) {
        if (Objects.isNull(hrPunchConfigId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingPunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingPunchClassConfigDO::getHrPunchConfigId, hrPunchConfigId)
                .eq(MappingPunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public MappingPunchClassConfigDO getByHrPunchClassId(Long hrPunchClassId) {
        if (Objects.isNull(hrPunchClassId)) {
            return null;
        }
        LambdaQueryWrapper<MappingPunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingPunchClassConfigDO::getHrPunchClassId, hrPunchClassId)
                .eq(MappingPunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public MappingPunchClassConfigDO getByPunchClassConfigId(Long punchClassConfigId) {
        if (Objects.isNull(punchClassConfigId)) {
            return null;
        }
        LambdaQueryWrapper<MappingPunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingPunchClassConfigDO::getPunchClassConfigId, punchClassConfigId)
                .eq(MappingPunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public List<MappingPunchClassConfigDO> listByHrPunchConfigIds(List<Long> hrPunchConfigIds) {
        if (CollectionUtils.isEmpty(hrPunchConfigIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingPunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(MappingPunchClassConfigDO::getHrPunchConfigId, hrPunchConfigIds)
                .eq(MappingPunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<MappingPunchClassConfigDO> listByHrPunchClassIds(List<Long> hrPunchClassIds) {
        if (CollectionUtils.isEmpty(hrPunchClassIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingPunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(MappingPunchClassConfigDO::getHrPunchClassId, hrPunchClassIds)
                .eq(MappingPunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<MappingPunchClassConfigDO> listByPunchClassConfigIds(List<Long> punchClassConfigIds) {
        if (CollectionUtils.isEmpty(punchClassConfigIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingPunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(MappingPunchClassConfigDO::getPunchClassConfigId, punchClassConfigIds)
                .eq(MappingPunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public boolean removeByPunchClassConfigId(Long punchClassConfigId) {
        if (Objects.isNull(punchClassConfigId)) {
            return true;
        }
        LambdaQueryWrapper<MappingPunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingPunchClassConfigDO::getPunchClassConfigId, punchClassConfigId);
        return remove(queryWrapper);
    }
}
