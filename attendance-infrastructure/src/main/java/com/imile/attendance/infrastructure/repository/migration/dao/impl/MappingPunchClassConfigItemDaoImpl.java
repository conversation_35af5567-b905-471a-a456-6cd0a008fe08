package com.imile.attendance.infrastructure.repository.migration.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingPunchClassConfigItemDao;
import com.imile.attendance.infrastructure.repository.migration.mapper.MappingPunchClassConfigItemMapper;
import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigItemDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11 
 * @Description 考勤班次时间配置映射表DAO实现类
 */
@Component
@RequiredArgsConstructor
public class MappingPunchClassConfigItemDaoImpl extends ServiceImpl<MappingPunchClassConfigItemMapper, MappingPunchClassConfigItemDO>
        implements MappingPunchClassConfigItemDao {

    @Override
    public List<MappingPunchClassConfigItemDO> listByCountry(String country) {
        if (StringUtils.isBlank(country)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingPunchClassConfigItemDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingPunchClassConfigItemDO::getCountry, country)
                .eq(MappingPunchClassConfigItemDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<MappingPunchClassConfigItemDO> listByHrPunchConfigId(Long hrPunchConfigId) {
        if (Objects.isNull(hrPunchConfigId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingPunchClassConfigItemDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingPunchClassConfigItemDO::getHrPunchConfigId, hrPunchConfigId)
                .eq(MappingPunchClassConfigItemDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<MappingPunchClassConfigItemDO> listByHrPunchClassId(Long hrPunchClassId) {
        if (Objects.isNull(hrPunchClassId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingPunchClassConfigItemDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingPunchClassConfigItemDO::getHrPunchClassId, hrPunchClassId)
                .eq(MappingPunchClassConfigItemDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public MappingPunchClassConfigItemDO getByHrPunchClassItemId(Long hrPunchClassItemId) {
        if (Objects.isNull(hrPunchClassItemId)) {
            return null;
        }
        LambdaQueryWrapper<MappingPunchClassConfigItemDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingPunchClassConfigItemDO::getHrPunchClassItemId, hrPunchClassItemId)
                .eq(MappingPunchClassConfigItemDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public List<MappingPunchClassConfigItemDO> listByPunchClassConfigId(Long punchClassConfigId) {
        if (Objects.isNull(punchClassConfigId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingPunchClassConfigItemDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingPunchClassConfigItemDO::getPunchClassConfigId, punchClassConfigId)
                .eq(MappingPunchClassConfigItemDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public MappingPunchClassConfigItemDO getByPunchClassConfigItemId(Long punchClassConfigItemId) {
        if (Objects.isNull(punchClassConfigItemId)) {
            return null;
        }
        LambdaQueryWrapper<MappingPunchClassConfigItemDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingPunchClassConfigItemDO::getPunchClassConfigItemId, punchClassConfigItemId)
                .eq(MappingPunchClassConfigItemDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public List<MappingPunchClassConfigItemDO> listByHrPunchClassItemIds(List<Long> hrPunchClassItemIds) {
        if (CollectionUtils.isEmpty(hrPunchClassItemIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingPunchClassConfigItemDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(MappingPunchClassConfigItemDO::getHrPunchClassItemId, hrPunchClassItemIds)
                .eq(MappingPunchClassConfigItemDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<MappingPunchClassConfigItemDO> listByPunchClassConfigIds(List<Long> punchClassConfigIds) {
        if (CollectionUtils.isEmpty(punchClassConfigIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingPunchClassConfigItemDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(MappingPunchClassConfigItemDO::getPunchClassConfigId, punchClassConfigIds)
                .eq(MappingPunchClassConfigItemDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public boolean removeByPunchClassConfigId(Long punchClassConfigId) {
        if (Objects.isNull(punchClassConfigId)) {
            return true;
        }
        LambdaQueryWrapper<MappingPunchClassConfigItemDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingPunchClassConfigItemDO::getPunchClassConfigId, punchClassConfigId);
        return remove(queryWrapper);
    }
}
