package com.imile.attendance.infrastructure.repository.migration.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingPunchClassConfigRangeDao;
import com.imile.attendance.infrastructure.repository.migration.mapper.MappingPunchClassConfigRangeMapper;
import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigRangeDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11 
 * @Description 考勤班次范围映射表DAO实现类
 */
@Component
@RequiredArgsConstructor
public class MappingPunchClassConfigRangeDaoImpl extends ServiceImpl<MappingPunchClassConfigRangeMapper, MappingPunchClassConfigRangeDO>
        implements MappingPunchClassConfigRangeDao {

    @Override
    public MappingPunchClassConfigRangeDO getByHrPunchConfigRangeId(Long hrPunchConfigRangeId) {
        if (Objects.isNull(hrPunchConfigRangeId)) {
            return null;
        }
        LambdaQueryWrapper<MappingPunchClassConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingPunchClassConfigRangeDO::getHrPunchConfigRangeId, hrPunchConfigRangeId)
                .eq(MappingPunchClassConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public List<MappingPunchClassConfigRangeDO> listByHrPunchConfigId(Long hrPunchConfigId) {
        if (Objects.isNull(hrPunchConfigId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingPunchClassConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingPunchClassConfigRangeDO::getHrPunchConfigId, hrPunchConfigId)
                .eq(MappingPunchClassConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public MappingPunchClassConfigRangeDO getByPunchClassConfigRangeId(Long punchClassConfigRangeId) {
        if (Objects.isNull(punchClassConfigRangeId)) {
            return null;
        }
        LambdaQueryWrapper<MappingPunchClassConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingPunchClassConfigRangeDO::getPunchClassConfigRangeId, punchClassConfigRangeId)
                .eq(MappingPunchClassConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public List<MappingPunchClassConfigRangeDO> listByPunchClassConfigId(Long punchClassConfigId) {
        if (Objects.isNull(punchClassConfigId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingPunchClassConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingPunchClassConfigRangeDO::getPunchClassConfigId, punchClassConfigId)
                .eq(MappingPunchClassConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<MappingPunchClassConfigRangeDO> listByHrPunchConfigIds(List<Long> hrPunchConfigIds) {
        if (CollectionUtils.isEmpty(hrPunchConfigIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingPunchClassConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(MappingPunchClassConfigRangeDO::getHrPunchConfigId, hrPunchConfigIds)
                .eq(MappingPunchClassConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<MappingPunchClassConfigRangeDO> listByPunchClassConfigIds(List<Long> punchClassConfigIds) {
        if (CollectionUtils.isEmpty(punchClassConfigIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingPunchClassConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(MappingPunchClassConfigRangeDO::getPunchClassConfigId, punchClassConfigIds)
                .eq(MappingPunchClassConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<MappingPunchClassConfigRangeDO> listByHrRangeType(String hrRangeType) {
        if (StringUtils.isBlank(hrRangeType)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingPunchClassConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingPunchClassConfigRangeDO::getHrRangeType, hrRangeType)
                .eq(MappingPunchClassConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<MappingPunchClassConfigRangeDO> listByHrRangeStatus(String hrRangeStatus) {
        if (StringUtils.isBlank(hrRangeStatus)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingPunchClassConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingPunchClassConfigRangeDO::getHrRangeStatus, hrRangeStatus)
                .eq(MappingPunchClassConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public boolean removeByPunchClassConfigId(Long punchClassConfigId) {
        if (Objects.isNull(punchClassConfigId)) {
            return true;
        }
        LambdaQueryWrapper<MappingPunchClassConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingPunchClassConfigRangeDO::getPunchClassConfigId, punchClassConfigId);
        return remove(queryWrapper);
    }
}
