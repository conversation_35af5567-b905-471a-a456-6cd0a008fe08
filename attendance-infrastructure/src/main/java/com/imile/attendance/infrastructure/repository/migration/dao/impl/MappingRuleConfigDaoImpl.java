package com.imile.attendance.infrastructure.repository.migration.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingRuleConfigDao;
import com.imile.attendance.infrastructure.repository.migration.mapper.MappingRuleConfigMapper;
import com.imile.attendance.infrastructure.repository.migration.model.MappingRuleConfigDO;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11
 * @Description 考勤规则映射表DAO实现类
 */
@Component
@RequiredArgsConstructor
public class MappingRuleConfigDaoImpl extends ServiceImpl<MappingRuleConfigMapper, MappingRuleConfigDO> implements MappingRuleConfigDao {

    @Override
    public List<MappingRuleConfigDO> listByCountry(String country) {
        if (StringUtils.isBlank(country)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingRuleConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingRuleConfigDO::getCountry, country)
                .eq(MappingRuleConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<MappingRuleConfigDO> listByHrPunchConfigId(Long hrPunchConfigId) {
        if (Objects.isNull(hrPunchConfigId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingRuleConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingRuleConfigDO::getHrPunchConfigId, hrPunchConfigId)
                .eq(MappingRuleConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<MappingRuleConfigDO> listByRuleType(String ruleType) {
        if (StringUtils.isBlank(ruleType)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingRuleConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingRuleConfigDO::getRuleType, ruleType)
                .eq(MappingRuleConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public MappingRuleConfigDO getByRuleId(Long ruleId) {
        if (Objects.isNull(ruleId)) {
            return null;
        }
        LambdaQueryWrapper<MappingRuleConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingRuleConfigDO::getRuleId, ruleId)
                .eq(MappingRuleConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public List<MappingRuleConfigDO> listByStatus(String status) {
        if (StringUtils.isBlank(status)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingRuleConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingRuleConfigDO::getStatus, status)
                .eq(MappingRuleConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public MappingRuleConfigDO getByHrPunchConfigIdAndRuleType(Long hrPunchConfigId, String ruleType) {
        if (Objects.isNull(hrPunchConfigId) || StringUtils.isBlank(ruleType)) {
            return null;
        }
        LambdaQueryWrapper<MappingRuleConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingRuleConfigDO::getHrPunchConfigId, hrPunchConfigId)
                .eq(MappingRuleConfigDO::getRuleType, ruleType)
                .eq(MappingRuleConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public List<MappingRuleConfigDO> listByHrPunchConfigIds(List<Long> hrPunchConfigIds) {
        if (CollectionUtils.isEmpty(hrPunchConfigIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingRuleConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(MappingRuleConfigDO::getHrPunchConfigId, hrPunchConfigIds)
                .eq(MappingRuleConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<MappingRuleConfigDO> listByRuleIds(List<Long> ruleIds) {
        if (CollectionUtils.isEmpty(ruleIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingRuleConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(MappingRuleConfigDO::getRuleId, ruleIds)
                .eq(MappingRuleConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<MappingRuleConfigDO> listLatestAndActiveByCountryAndRuleType(String country, String ruleType) {
        if (StringUtils.isBlank(country) || StringUtils.isBlank(ruleType)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingRuleConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingRuleConfigDO::getCountry, country)
                .eq(MappingRuleConfigDO::getRuleType, ruleType)
                .eq(MappingRuleConfigDO::getHrIsLatest, BusinessConstant.Y)
                .eq(MappingRuleConfigDO::getStatus, StatusEnum.ACTIVE.getCode())
                .eq(MappingRuleConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<MappingRuleConfigDO> listLatestAndActiveByHrPunchConfigId(Long hrPunchConfigId) {
        if (Objects.isNull(hrPunchConfigId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingRuleConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingRuleConfigDO::getHrPunchConfigId, hrPunchConfigId)
                .eq(MappingRuleConfigDO::getHrIsLatest, BusinessConstant.Y)
                .eq(MappingRuleConfigDO::getStatus, StatusEnum.ACTIVE.getCode())
                .eq(MappingRuleConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }
}
