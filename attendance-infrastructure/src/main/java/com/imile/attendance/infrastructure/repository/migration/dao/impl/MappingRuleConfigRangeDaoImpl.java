package com.imile.attendance.infrastructure.repository.migration.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingRuleConfigRangeDao;
import com.imile.attendance.infrastructure.repository.migration.mapper.MappingRuleConfigRangeMapper;
import com.imile.attendance.infrastructure.repository.migration.model.MappingRuleConfigRangeDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11 
 * @Description 考勤规则范围映射表DAO实现类
 */
@Component
@RequiredArgsConstructor
public class MappingRuleConfigRangeDaoImpl extends ServiceImpl<MappingRuleConfigRangeMapper, MappingRuleConfigRangeDO>
        implements MappingRuleConfigRangeDao {

    @Override
    public List<MappingRuleConfigRangeDO> listByHrPunchConfigId(Long hrPunchConfigId) {
        if (Objects.isNull(hrPunchConfigId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingRuleConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingRuleConfigRangeDO::getHrPunchConfigId, hrPunchConfigId)
                .eq(MappingRuleConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public MappingRuleConfigRangeDO getByHrPunchConfigRangeId(Long hrPunchConfigRangeId) {
        if (Objects.isNull(hrPunchConfigRangeId)) {
            return null;
        }
        LambdaQueryWrapper<MappingRuleConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingRuleConfigRangeDO::getHrPunchConfigRangeId, hrPunchConfigRangeId)
                .eq(MappingRuleConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public List<MappingRuleConfigRangeDO> listByRuleConfigId(Long ruleConfigId) {
        if (Objects.isNull(ruleConfigId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingRuleConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingRuleConfigRangeDO::getRuleConfigId, ruleConfigId)
                .eq(MappingRuleConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public MappingRuleConfigRangeDO getByRuleConfigRangeId(Long ruleConfigRangeId) {
        if (Objects.isNull(ruleConfigRangeId)) {
            return null;
        }
        LambdaQueryWrapper<MappingRuleConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MappingRuleConfigRangeDO::getRuleConfigRangeId, ruleConfigRangeId)
                .eq(MappingRuleConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public List<MappingRuleConfigRangeDO> listByHrPunchConfigIds(List<Long> hrPunchConfigIds) {
        if (CollectionUtils.isEmpty(hrPunchConfigIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingRuleConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(MappingRuleConfigRangeDO::getHrPunchConfigId, hrPunchConfigIds)
                .eq(MappingRuleConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<MappingRuleConfigRangeDO> listByRuleConfigIds(List<Long> ruleConfigIds) {
        if (CollectionUtils.isEmpty(ruleConfigIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MappingRuleConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(MappingRuleConfigRangeDO::getRuleConfigId, ruleConfigIds)
                .eq(MappingRuleConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }
}
