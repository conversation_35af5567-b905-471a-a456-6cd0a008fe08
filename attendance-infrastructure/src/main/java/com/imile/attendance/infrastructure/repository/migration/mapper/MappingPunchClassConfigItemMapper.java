package com.imile.attendance.infrastructure.repository.migration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.common.AttendanceBaseMapper;
import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigItemDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11 
 * @Description 考勤班次时间配置映射表Mapper
 */
@Mapper
@Repository
public interface MappingPunchClassConfigItemMapper extends AttendanceBaseMapper<MappingPunchClassConfigItemDO> {

}
