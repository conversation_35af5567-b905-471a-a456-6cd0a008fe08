package com.imile.attendance.infrastructure.repository.migration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.common.AttendanceBaseMapper;
import com.imile.attendance.infrastructure.repository.migration.model.MappingRuleConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11 
 * @Description
 */
@Mapper
@Repository
public interface MappingRuleConfigMapper extends AttendanceBaseMapper<MappingRuleConfigDO> {

}
