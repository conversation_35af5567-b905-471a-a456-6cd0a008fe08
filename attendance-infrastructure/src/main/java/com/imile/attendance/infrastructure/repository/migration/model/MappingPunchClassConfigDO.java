package com.imile.attendance.infrastructure.repository.migration.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import com.imile.common.annotation.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11
 * @Description 考勤班次映射表
 */
@ApiModel(description = "考勤班次映射表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mapping_punch_class_config")
public class MappingPunchClassConfigDO extends BaseDO {

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "hr考勤组id")
    private Long hrPunchConfigId;

    @ApiModelProperty(value = "hr考勤组班次id")
    private Long hrPunchClassId;

    @ApiModelProperty(value = "hr班次名称")
    private String hrClassName;

    @ApiModelProperty(value = "hr班次类型")
    private Integer hrClassType;

    @ApiModelProperty(value = "新考勤班次id")
    private Long punchClassConfigId;

    @ApiModelProperty(value = "新考勤班次名称")
    private String punchClassConfigName;

}
