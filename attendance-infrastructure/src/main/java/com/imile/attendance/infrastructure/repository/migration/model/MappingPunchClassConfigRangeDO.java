package com.imile.attendance.infrastructure.repository.migration.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import com.imile.common.annotation.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11
 * @Description 考勤班次范围映射表
 */
@ApiModel(description = "考勤班次范围映射表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mapping_punch_class_config_range")
public class MappingPunchClassConfigRangeDO extends BaseDO {

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "hr考勤组范围id")
    private Long hrPunchConfigRangeId;

    @ApiModelProperty(value = "hr考勤组id")
    private Long hrPunchConfigId;

    @ApiModelProperty(value = "hr范围类型")
    private String hrRangeType;

    @ApiModelProperty(value = "hr是否需要打卡")
    private Integer hrIsNeedPunch;

    @ApiModelProperty(value = "hr范围生效时间")
    private Date hrEffectTime;

    @ApiModelProperty(value = "hr范围失效时间")
    private Date hrExpireTime;

    @ApiModelProperty(value = "hr范围是否最新")
    private Integer hrIsLatest;

    @ApiModelProperty(value = "hr范围状态")
    private String hrRangeStatus;

    @ApiModelProperty(value = "新考勤班次范围id")
    private Long punchClassConfigRangeId;

    @ApiModelProperty(value = "新考勤班次id")
    private Long punchClassConfigId;

    @ApiModelProperty(value = "新考勤范围类型id")
    private Long ruleBizId;
}
