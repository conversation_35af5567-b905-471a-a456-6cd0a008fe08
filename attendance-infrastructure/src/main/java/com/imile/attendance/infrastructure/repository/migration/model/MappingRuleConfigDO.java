package com.imile.attendance.infrastructure.repository.migration.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import com.imile.common.annotation.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11
 * @Description 考勤规则映射表（打卡，补卡，加班）
 */
@ApiModel(description = "考勤规则映射表（打卡，补卡，加班）")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mapping_rule_config")
public class MappingRuleConfigDO extends BaseDO {

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "考勤打卡配置ID")
    private Long hrPunchConfigId;

    @ApiModelProperty(value = "生效时间")
    private Date hrEffectTime;

    @ApiModelProperty(value = "失效时间")
    private Date hrExpireTime;

    @ApiModelProperty(value = "是否为最新")
    private Integer hrIsLatest;

    @ApiModelProperty(value = "状态 ACTIVE、DISABLED")
    private String status;

    @ApiModelProperty(value = "新考勤规则类型打卡，补卡，加班")
    private String ruleType;

    @ApiModelProperty(value = "新考勤规则id")
    private Long ruleId;
}
