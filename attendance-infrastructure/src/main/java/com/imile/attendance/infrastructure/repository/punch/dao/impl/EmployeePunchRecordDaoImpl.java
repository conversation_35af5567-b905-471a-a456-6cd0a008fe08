package com.imile.attendance.infrastructure.repository.punch.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.punch.dao.EmployeePunchRecordDao;
import com.imile.attendance.infrastructure.repository.punch.dto.PunchCardRecordDTO;
import com.imile.attendance.infrastructure.repository.punch.mapper.EmployeePunchRecordMapper;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.punch.query.EmployeePunchCardRecordQuery;
import com.imile.attendance.infrastructure.repository.punch.query.UserPunchCardRecordQuery;
import com.imile.common.enums.IsDeleteEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
@Service
public class EmployeePunchRecordDaoImpl extends ServiceImpl<EmployeePunchRecordMapper, EmployeePunchRecordDO> implements EmployeePunchRecordDao {

    @Resource
    private EmployeePunchRecordMapper employeePunchRecordMapper;

    @Override
    public List<EmployeePunchRecordDO> getPunchList(Long dayId, String userCode, String punchType) {
        LambdaQueryWrapper<EmployeePunchRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        if (dayId != null) {
            queryWrapper.eq(EmployeePunchRecordDO::getDayId, dayId);
        }
        if (userCode != null) {
            queryWrapper.eq(EmployeePunchRecordDO::getUserCode, userCode);
        }
        if (punchType != null) {
            queryWrapper.eq(EmployeePunchRecordDO::getPunchType, punchType);
        }
        queryWrapper.eq(EmployeePunchRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<EmployeePunchRecordDO> listRecords(EmployeePunchCardRecordQuery query) {
        LambdaQueryWrapper<EmployeePunchRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        if (query.getStartTime() != null) {
            queryWrapper.ge(EmployeePunchRecordDO::getPunchTime, query.getStartTime());
        }
        if (query.getEndTime() != null) {
            queryWrapper.lt(EmployeePunchRecordDO::getPunchTime, query.getEndTime());
        }
        if (StringUtils.isNotBlank(query.getUserCode())) {
            queryWrapper.eq(EmployeePunchRecordDO::getUserCode, query.getUserCode());
        }
        if (StringUtils.isNotBlank(query.getCountry())) {
            queryWrapper.eq(EmployeePunchRecordDO::getCountry, query.getCountry());
        }
        if (CollectionUtils.isNotEmpty(query.getCountryList())) {
            queryWrapper.in(EmployeePunchRecordDO::getCountry, query.getCountryList());
        }
        if (CollectionUtils.isNotEmpty(query.getUserCodes())) {
            queryWrapper.in(EmployeePunchRecordDO::getUserCode, query.getUserCodes());
        }
        if (StringUtils.isNotBlank(query.getDayId())) {
            queryWrapper.eq(EmployeePunchRecordDO::getDayId, query.getDayId());
        }
        if (CollectionUtils.isNotEmpty(query.getDayIds())) {
            queryWrapper.in(EmployeePunchRecordDO::getDayId, query.getDayIds());
        }
        if (Objects.nonNull(query.getStartDayId())) {
            queryWrapper.ge(EmployeePunchRecordDO::getDayId, query.getStartDayId());
        }
        if (StringUtils.isNotBlank(query.getSourceType())) {
            queryWrapper.eq(EmployeePunchRecordDO::getSourceType, query.getSourceType());
        }
        if (query.getFormId() != null) {
            queryWrapper.eq(EmployeePunchRecordDO::getFormId, query.getFormId());
        }

        if (CollUtil.isNotEmpty(query.getGpsOrWifiConfigIds())) {
            // 拼接或的条件，gps配置id或wifi配置id在gpsOrWifiConfigIds里面即可
            queryWrapper.and(wrapper -> {
                wrapper.in(EmployeePunchRecordDO::getGpsConfigId, query.getGpsOrWifiConfigIds());
                wrapper.or();
                wrapper.in(EmployeePunchRecordDO::getWifiConfigId, query.getGpsOrWifiConfigIds());
            });
        }
        if (query.getCreateStartTime() != null) {
            queryWrapper.ge(EmployeePunchRecordDO::getCreateDate, query.getCreateStartTime());
        }
        if (query.getCreateEndTime() != null) {
            queryWrapper.lt(EmployeePunchRecordDO::getCreateDate, query.getCreateEndTime());
        }

        queryWrapper.eq(EmployeePunchRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(EmployeePunchRecordDO::getCreateDate);

        return this.list(queryWrapper);
    }

    @Override
    public List<EmployeePunchRecordDO> selectListByCondition(Long dayId, String country, List<String> userCodes) {
        LambdaQueryWrapper<EmployeePunchRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(dayId != null, EmployeePunchRecordDO::getDayId, dayId);
        queryWrapper.in(CollectionUtils.isNotEmpty(userCodes), EmployeePunchRecordDO::getUserCode, userCodes);
        queryWrapper.eq(country != null, EmployeePunchRecordDO::getCountry, country);
        queryWrapper.eq(EmployeePunchRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<EmployeePunchRecordDO> getPunchListByMobileConfigId(List<Long> mobileConfigId) {
        LambdaQueryWrapper<EmployeePunchRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(mobileConfigId), EmployeePunchRecordDO::getMobileConfigId, mobileConfigId);
        queryWrapper.eq(EmployeePunchRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(EmployeePunchRecordDO::getPunchTime);
        queryWrapper.last("limit 1");
        return this.list(queryWrapper);
    }

    /**
     * 根据条件获取打卡记录
     */
    @Override
    public List<EmployeePunchRecordDO> selectListByCondition(List<String> countryList, Date startTime, Date endTime) {
        LambdaQueryWrapper<EmployeePunchRecordDO> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(EmployeePunchRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(CollectionUtils.isNotEmpty(countryList), EmployeePunchRecordDO::getCountry, countryList);
        queryWrapper.ge(ObjectUtil.isNotNull(startTime), EmployeePunchRecordDO::getCreateDate, startTime);
        queryWrapper.le(ObjectUtil.isNotNull(endTime), EmployeePunchRecordDO::getCreateDate, endTime);
        queryWrapper.isNotNull(EmployeePunchRecordDO::getCountry);
        return this.list(queryWrapper);
    }

    @Override
//    @DS(HrmsProperties.Database.HRMS_RO_URL)
    public List<PunchCardRecordDTO> listRecord(UserPunchCardRecordQuery query) {
        return employeePunchRecordMapper.listRecord(query);
    }

    @Override
    public List<Long> selectUserIdByPunchCardTypes(Long startDayId, Long endDayId, List<String> punchCardTypeList) {
        return employeePunchRecordMapper.selectUserIdByPunchCardTypes(startDayId, endDayId, punchCardTypeList);
    }

    @Override
    public List<String> selectPunchCardTypeByUserCode(String userCode, Long startDayId, Long endDayId) {
        return employeePunchRecordMapper.selectPunchCardTypeByUserCode(userCode, startDayId, endDayId);
    }

    @Override
    public List<EmployeePunchRecordDO> listBySourceTypeAndDayId(String sourceType, List<String> dayIds) {
        LambdaQueryWrapper<EmployeePunchRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeePunchRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(EmployeePunchRecordDO::getDayId, dayIds);
        queryWrapper.eq(EmployeePunchRecordDO::getSourceType, sourceType);
        return list(queryWrapper);
    }

    @Override
    public void removeByCountry(String country, Long id) {
        if (StringUtils.isEmpty(country) && Objects.isNull(id)) {
            return;
        }
        LambdaQueryWrapper<EmployeePunchRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(country), EmployeePunchRecordDO::getCountry, country);
        queryWrapper.eq(Objects.nonNull(id), EmployeePunchRecordDO::getId, id);
        remove(queryWrapper);
    }

    @Override
    public List<Long> listHrPunchRecord(List<Long> hrPunchRecordList, Long startDayId) {
        return baseMapper.listHrPunchRecord(hrPunchRecordList, startDayId);
    }
}
