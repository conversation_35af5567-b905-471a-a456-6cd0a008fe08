package com.imile.attendance.infrastructure.repository.punch.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.aliyun.opensearch.sdk.dependencies.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.punch.dao.EmployeePunchRecordInspectionDao;
import com.imile.attendance.infrastructure.repository.punch.mapper.EmployeePunchRecordInspectionMapper;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordInspectionDO;
import com.imile.attendance.infrastructure.repository.punch.query.EmployeePunchRecordInspectionQuery;
import com.imile.common.enums.IsDeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} EmployeePunchRecordInspectionDaoImpl
 * {@code @since:} 2025-01-09 14:21
 * {@code @description:}
 */
@Service
@Slf4j
public class EmployeePunchRecordInspectionDaoImpl extends ServiceImpl<EmployeePunchRecordInspectionMapper, EmployeePunchRecordInspectionDO> implements EmployeePunchRecordInspectionDao {

    /**
     * 根据条件查询打卡记录
     *
     * @param query 查询条件
     * @return 打卡记录
     */
    @Override
    public List<EmployeePunchRecordInspectionDO> queryByCondition(EmployeePunchRecordInspectionQuery query) {
        if (ObjectUtil.isNull(query) || CollUtil.isEmpty(query.getPunchRecordIdList())) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<EmployeePunchRecordInspectionDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CollUtil.isNotEmpty(query.getPunchRecordIdList()), EmployeePunchRecordInspectionDO::getPunchRecordId, query.getPunchRecordIdList());
        queryWrapper.eq(EmployeePunchRecordInspectionDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }
}
