package com.imile.attendance.infrastructure.repository.punch.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class PunchCardRecordDTO {

    private Long id;
    /**
     * 日期
     */
    private String dayId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户Codes
     */
    private List<String> userCodes;
    /**
     * 用户编码
     */
    private String userCode;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 组织编码
     */
    private String organizationCode;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 数据来源
     */
    private String sourceType;
    /**
     * 打卡区域
     */
    private String punchArea;
    /**
     * 打卡方式
     */
    private String punchCardType;
    /**
     * 打卡时间
     */
    private Date punchTime;

    /**
     * 最后修改时间
     */
    private Date lastUpdDate;

    /**
     * 最后修改人名称
     */
    private String lastUpdUserName;

    /**
     * 日历名称
     */
    private String attendanceConfigName;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;
}
