package com.imile.attendance.infrastructure.repository.punch.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-03023
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmployeePunchCardRecordQuery {
    /**
     * 国家
     */
    private String country;

    /**
     * 国家
     */
    private List<String> countryList;

    /**
     * 部门
     */
    private List<Long> deptIdList;

    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 用户编码
     */
    private String userCode;

    private List<String> userCodes;

    /**
     * dayid 示例：20220124
     */
    private String dayId;

    private List<String> dayIds;

    private Long startDayId;

    private Long endDayId;

    /**
     * 数据来源 司机打卡：driver 微信：wechat
     */
    private String sourceType;

    private Long formId;

    /**
     * gps或wifi配置id集合
     */
    private List<Long> gpsOrWifiConfigIds;

    /**
     * 创建开始时间
     */
    private Date createStartTime;

    /**
     * 创建结束时间
     */
    private Date createEndTime;

    private Long lastId;
}
