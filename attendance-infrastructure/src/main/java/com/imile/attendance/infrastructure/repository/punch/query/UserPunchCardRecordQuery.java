package com.imile.attendance.infrastructure.repository.punch.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.query.ResourceQuery;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class UserPunchCardRecordQuery extends ResourceQuery {

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 用户账号或姓名
     */
    private String userCodeOrName;

    /**
     * 用户Codes
     */
    private List<String> userCodes;

    private String userCodeString;

    /**
     * 用户姓名或邮箱
     */
    private String userNameOrEmail;

    /**
     * 导出开始时间(对应考勤打卡时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;
    /**
     * 导出截止时间(对应考勤打卡时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /**
     * 国家
     */
    private String country;

    /**
     * 权限常驻国
     */
    private List<String> authLocationCountryList;

    /**
     * 是否有部门权限
     */
    private Boolean hasDeptPermission;

    /**
     * 是否有国家权限
     */
    private Boolean hasCountryPermission;
    /**
     * 是否有与国家、部门权限
     */
    private Boolean hasAndDeptAndCountryPermission;

    /**
     * 是否有或国家、部门权限
     */
    private Boolean hasOrDeptAndCountryPermission;

    /**
     * 数据来源
     */
    private String sourceType;

    /**
     * 部门id
     */
    private Long deptId;

    private String deptIdString;

    /**
     * 部门ids
     */
    private List<Long> deptIdList;

    /**
     * 考勤日历编码
     */
    private String attendanceConfigNo;

    /**
     * 配置打卡No
     */
    private String punchConfigNo;

    /**
     * 打卡方式
     */
    private String punchCardType;

    /**
     * 打卡方式集合
     */
    private List<String> punchCardTypeList;

    /**
     * 日期排序 升序0/降序1
     */
    private Integer daySort;

    /**
     * 打卡时间排序 升序0/降序1
     */
    private Integer punchDateSort;

    /**
     * 前端是否选择了部门
     */
    private Boolean isChooseDept;
}
