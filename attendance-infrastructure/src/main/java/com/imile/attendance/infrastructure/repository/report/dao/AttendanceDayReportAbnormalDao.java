package com.imile.attendance.infrastructure.repository.report.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportAbnormalDO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/17
 * @Description
 */
public interface AttendanceDayReportAbnormalDao extends IService<AttendanceDayReportAbnormalDO> {
    List<AttendanceDayReportAbnormalDO> selectByReportId(Long reportId);

    List<AttendanceDayReportAbnormalDO> selectByReportIds(List<Long> reportIds);
}
