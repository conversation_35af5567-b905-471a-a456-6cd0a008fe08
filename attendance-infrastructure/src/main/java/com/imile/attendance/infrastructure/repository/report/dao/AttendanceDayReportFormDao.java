package com.imile.attendance.infrastructure.repository.report.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportFormDO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/17
 * @Description
 */
public interface AttendanceDayReportFormDao extends IService<AttendanceDayReportFormDO> {
    List<AttendanceDayReportFormDO> selectByReportId(Long reportId);

    List<AttendanceDayReportFormDO> selectByReportIds(List<Long> reportIds);
}
