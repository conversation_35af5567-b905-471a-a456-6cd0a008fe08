package com.imile.attendance.infrastructure.repository.report.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.report.dao.AttendanceDayReportAbnormalDao;
import com.imile.attendance.infrastructure.repository.report.mapper.AttendanceDayReportAbnormalMapper;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportAbnormalDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/6/17
 * @Description
 */
@Component
@RequiredArgsConstructor
public class AttendanceDayReportAbnormalDaoImpl extends ServiceImpl<AttendanceDayReportAbnormalMapper, AttendanceDayReportAbnormalDO> implements AttendanceDayReportAbnormalDao {

    @Override
    public List<AttendanceDayReportAbnormalDO> selectByReportId(Long reportId) {
        if (Objects.isNull(reportId)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<AttendanceDayReportAbnormalDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .eq(AttendanceDayReportAbnormalDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(AttendanceDayReportAbnormalDO::getDayReportId, reportId);
        return this.list(queryWrapper);
    }

    @Override
    public List<AttendanceDayReportAbnormalDO> selectByReportIds(List<Long> reportIds) {
        if (CollectionUtils.isEmpty(reportIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<AttendanceDayReportAbnormalDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .eq(AttendanceDayReportAbnormalDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .in(AttendanceDayReportAbnormalDO::getDayReportId, reportIds);
        return this.list(queryWrapper);
    }
}
