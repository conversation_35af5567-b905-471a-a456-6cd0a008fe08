package com.imile.attendance.infrastructure.repository.report.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormUserInfoDO;
import com.imile.attendance.infrastructure.repository.report.dao.AttendanceDayReportDao;
import com.imile.attendance.infrastructure.repository.report.dto.UserMonthReportBaseDTO;
import com.imile.attendance.infrastructure.repository.report.mapper.AttendanceDayReportMapper;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportDO;
import com.imile.attendance.infrastructure.repository.report.query.MonthReportListQuery;
import com.imile.attendance.infrastructure.repository.report.query.UserDayReportQuery;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @Date 2025/6/17
 * @Description
 */
@Component
@RequiredArgsConstructor
public class AttendanceDayReportDaoImpl extends ServiceImpl<AttendanceDayReportMapper, AttendanceDayReportDO> implements AttendanceDayReportDao {

    @Override
    public AttendanceDayReportDO selectByUserCodeAndDayId(String userCode, Long dayId) {
        if (StringUtils.isBlank(userCode) || Objects.isNull(dayId)) {
            return null;
        }
        LambdaQueryWrapper<AttendanceDayReportDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(AttendanceDayReportDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(AttendanceDayReportDO::getUserCode, userCode)
                .eq(AttendanceDayReportDO::getDayId, dayId)
                .last("limit 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public List<AttendanceDayReportDO> selectDayReportList(UserDayReportQuery query) {
        LambdaQueryWrapper<AttendanceDayReportDO> queryWrapper = Wrappers.lambdaQuery(AttendanceDayReportDO.class);
        queryWrapper.eq(AttendanceDayReportDO::getIsDelete, IsDeleteEnum.NO.getCode());

        if (CollectionUtils.isNotEmpty(query.getUserIds())) {
            queryWrapper.in(AttendanceDayReportDO::getUserId, query.getUserIds());
        }
        if (CollectionUtils.isNotEmpty(query.getEmployeeTypeList())) {
            queryWrapper.in(AttendanceDayReportDO::getEmployeeType, query.getEmployeeTypeList());
        }
        if (CollectionUtils.isNotEmpty(query.getDeptIds())) {
            queryWrapper.in(AttendanceDayReportDO::getDeptId, query.getDeptIds());
        }
        if (CollectionUtils.isNotEmpty(query.getPostIds())) {
            queryWrapper.in(AttendanceDayReportDO::getPostId, query.getPostIds());
        }
        if (StringUtils.isNotBlank(query.getLocationCountry())) {
            queryWrapper.eq(AttendanceDayReportDO::getLocationCountry, query.getLocationCountry());
        }
        if (StringUtils.isNotBlank(query.getLocationProvince())) {
            queryWrapper.eq(AttendanceDayReportDO::getLocationProvince, query.getLocationProvince());
        }
        if (StringUtils.isNotBlank(query.getLocationCity())) {
            queryWrapper.eq(AttendanceDayReportDO::getLocationCity, query.getLocationCity());
        }
        if (StringUtils.isNotBlank(query.getPunchType())) {
            queryWrapper.eq(AttendanceDayReportDO::getPunchConfigType, query.getPunchType());
        }
        if (StringUtils.isNotBlank(query.getDayShiftRule())) {
            queryWrapper.eq(AttendanceDayReportDO::getDayShiftRule, query.getDayShiftRule());
        }
        if (StringUtils.isNotBlank(query.getInitResult())) {
            queryWrapper.eq(AttendanceDayReportDO::getInitResult, query.getInitResult());
            queryWrapper.isNotNull(AttendanceDayReportDO::getInitResult);
        }
        if (StringUtils.isNotBlank(query.getFinalResult())) {
            queryWrapper.eq(AttendanceDayReportDO::getFinalResult, query.getFinalResult());
            queryWrapper.isNotNull(AttendanceDayReportDO::getInitResult);
        }
        if (CollectionUtils.isNotEmpty(query.getDayIds())) {
            queryWrapper.in(AttendanceDayReportDO::getDayId, query.getDayIds());
        }
        if (Objects.nonNull(query.getStartDay()) && Objects.nonNull(query.getEndDay())) {
            queryWrapper.ge(AttendanceDayReportDO::getDayId, query.getStartDay());
            queryWrapper.le(AttendanceDayReportDO::getDayId, query.getEndDay());
        }
        // 拼接权限
        // 同时具有部门和国家权限
        if (CollectionUtils.isNotEmpty(query.getAuthLocationCountryList())
                && CollectionUtils.isNotEmpty(query.getAuthDeptIdList())) {
            queryWrapper.and(wrapper -> wrapper
                    .in(AttendanceDayReportDO::getLocationCountry, query.getAuthLocationCountryList())
                    .or()
                    .in(AttendanceDayReportDO::getDeptId, query.getAuthDeptIdList()));
        }
        // 只有国家权限
        if (CollectionUtils.isNotEmpty(query.getAuthLocationCountryList())
                && CollectionUtils.isEmpty(query.getAuthDeptIdList())) {
            queryWrapper.in(AttendanceDayReportDO::getLocationCountry, query.getAuthLocationCountryList());
        }
        // 只有部门权限
        if (CollectionUtils.isNotEmpty(query.getAuthDeptIdList())
                && CollectionUtils.isEmpty(query.getAuthLocationCountryList())) {
            queryWrapper.in(AttendanceDayReportDO::getDeptId, query.getAuthDeptIdList());
        }
        // 筛选条件处理
        if (StringUtils.isNotBlank(query.getLocationCountry())) {
            queryWrapper.eq(AttendanceDayReportDO::getLocationCountry, query.getLocationCountry());
        }
        if (CollectionUtils.isNotEmpty(query.getDeptIds())) {
            queryWrapper.in(AttendanceDayReportDO::getDeptId, query.getDeptIds());
        }
        queryWrapper
                .orderByDesc(AttendanceDayReportDO::getDayId)
                .orderByAsc(AttendanceDayReportDO::getLocationCountry)
                .orderByAsc(AttendanceDayReportDO::getPunchClassConfigId);
        return list(queryWrapper);
    }

    @Override
    public List<AttendanceDayReportDO> selectByUserIdsAndDayIds(List<Long> userIds, List<Long> dayIds) {
        LambdaQueryWrapper<AttendanceDayReportDO> queryWrapper = Wrappers.lambdaQuery(AttendanceDayReportDO.class);
        queryWrapper.eq(AttendanceDayReportDO::getIsDelete, IsDeleteEnum.NO.getCode());

        if (CollectionUtils.isNotEmpty(userIds)) {
            queryWrapper.in(AttendanceDayReportDO::getUserId, userIds);
        }
        if (CollectionUtils.isNotEmpty(dayIds)) {
            queryWrapper.in(AttendanceDayReportDO::getDayId, dayIds);
        }
        return list(queryWrapper);
    }

    @Override
    public List<UserMonthReportBaseDTO> page(MonthReportListQuery monthReportListQuery) {
        return this.baseMapper.page(monthReportListQuery);
    }
}
