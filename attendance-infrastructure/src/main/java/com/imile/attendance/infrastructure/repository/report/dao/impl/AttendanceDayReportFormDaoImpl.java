package com.imile.attendance.infrastructure.repository.report.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.report.dao.AttendanceDayReportFormDao;
import com.imile.attendance.infrastructure.repository.report.mapper.AttendanceDayReportFormMapper;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportAbnormalDO;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportFormDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/6/17
 * @Description
 */
@Component
@RequiredArgsConstructor
public class AttendanceDayReportFormDaoImpl extends ServiceImpl<AttendanceDayReportFormMapper, AttendanceDayReportFormDO> implements AttendanceDayReportFormDao {

    @Override
    public List<AttendanceDayReportFormDO> selectByReportId(Long reportId) {
        if (Objects.isNull(reportId)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<AttendanceDayReportFormDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .eq(AttendanceDayReportFormDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(AttendanceDayReportFormDO::getDayReportId, reportId);
        return this.list(queryWrapper);
    }

    @Override
    public List<AttendanceDayReportFormDO> selectByReportIds(List<Long> reportIds) {
        if (CollectionUtils.isEmpty(reportIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<AttendanceDayReportFormDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .eq(AttendanceDayReportFormDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .in(AttendanceDayReportFormDO::getDayReportId, reportIds);
        return this.list(queryWrapper);
    }
}
