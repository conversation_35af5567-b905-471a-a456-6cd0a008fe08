package com.imile.attendance.infrastructure.repository.report.dto;

import lombok.Data;

/**
 * 员工日报DTO
 *
 * <AUTHOR>
 * @date 2025/6/9
 */
@Data
public class UserDayReportDTO {
    /**
     * 员工姓名
     */
    private String userName;

    /**
     * 员工帐号
     */
    private String userCode;

    /**
     * 用工类型
     */
    private String employeeType;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 常驻地
     */
    private String locationCountry;

    /**
     * 常驻省
     */
    private String locationProvince;

    /**
     * 常驻市
     */
    private String locationCity;

    /**
     * dayId
     */
    private Long dayId;

    /**
     * 打卡规则Id
     */
    private Long punchConfigId;

    /**
     * 打卡类型
     */
    private String punchType;

    /**
     * 排班Id
     */
    private Long shiftId;

    /**
     * 排班计划
     */
    private String dayShiftRule;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 是否有正常表记录
     */
    private Integer hasNormal;

    /**
     * 是否有异常表记录
     */
    private Integer hasAbnormal;

    /**
     * 是否有正常表快照记录
     */
    private Integer hasNormalSnap;

    /**
     * 是否有异常表快照记录
     */
    private Integer hasAbnormalSnap;
}
