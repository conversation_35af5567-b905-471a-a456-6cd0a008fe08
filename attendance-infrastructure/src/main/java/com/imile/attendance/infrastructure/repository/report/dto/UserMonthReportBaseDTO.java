package com.imile.attendance.infrastructure.repository.report.dto;

import lombok.Data;

/**
 * <AUTHOR> chen
 * @Date 2025/6/23 
 * @Description
 */
@Data
public class UserMonthReportBaseDTO {

    /**
     * 员工id
     */
    private Long userId;

    /**
     * 员工姓名
     */
    private String userName;

    /**
     * 员工帐号
     */
    private String userCode;

    /**
     * 工作状态
     */
    private String workStatus;

    /**
     * 账号状态
     */
    private String accountStatus;

    /**
     * 用工类型
     */
    private String employeeType;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 岗位名称
     */
    private Long postId;

    /**
     * 常驻地
     */
    private String locationCountry;

    /**
     * 常驻省
     */
    private String locationProvince;

    /**
     * 常驻市
     */
    private String locationCity;
}
