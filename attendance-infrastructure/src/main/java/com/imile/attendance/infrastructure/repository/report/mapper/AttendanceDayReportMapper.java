package com.imile.attendance.infrastructure.repository.report.mapper;

import com.imile.attendance.infrastructure.common.AttendanceBaseMapper;
import com.imile.attendance.infrastructure.repository.report.dto.UserMonthReportBaseDTO;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportDO;
import com.imile.attendance.infrastructure.repository.report.query.MonthReportListQuery;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/17
 * @Description
 */
@Mapper
@Repository
public interface AttendanceDayReportMapper extends AttendanceBaseMapper<AttendanceDayReportDO> {


    List<UserMonthReportBaseDTO> page(MonthReportListQuery monthReportListQuery);

}
