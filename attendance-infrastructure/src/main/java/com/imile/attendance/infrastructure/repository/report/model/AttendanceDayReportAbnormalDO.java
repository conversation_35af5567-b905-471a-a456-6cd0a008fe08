package com.imile.attendance.infrastructure.repository.report.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 考勤日报统计-关联异常子表
 *
 * <AUTHOR>
 * @Date 2025/6/17
 * @Description
 */
@ApiModel(description = "考勤日报统计-关联异常子表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("attendance_day_report_abnormal")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceDayReportAbnormalDO extends BaseDO {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "日报主键")
    private Long dayReportId;

    @ApiModelProperty(value = "员工异常表主键")
    private Long abnormalId;

    @ApiModelProperty(value = "异常类型")
    private String abnormalType;

    @ApiModelProperty(value = "异常状态")
    private String abnormalStatus;

    @ApiModelProperty(value = "异常班次主键")
    private Long punchClassId;

    @ApiModelProperty(value = "异常时段主键")
    private Long punchClassItemConfigId;
}
