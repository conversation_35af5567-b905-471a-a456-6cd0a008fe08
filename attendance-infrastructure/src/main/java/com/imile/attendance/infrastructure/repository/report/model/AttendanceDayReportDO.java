package com.imile.attendance.infrastructure.repository.report.model;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import com.imile.attendance.enums.shift.DayShiftRuleEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 考勤日报统计表
 *
 * <AUTHOR>
 * @Date 2025/6/17
 * @Description
 */
@ApiModel(description = "考勤日报统计表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("attendance_day_report")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceDayReportDO extends BaseDO {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "考勤日期")
    private Long dayId;

    @ApiModelProperty(value = "员工主键")
    private Long userId;

    @ApiModelProperty(value = "员工姓名")
    private String userName;

    @ApiModelProperty(value = "员工帐号")
    private String userCode;

    @ApiModelProperty(value = "用工类型")
    private String employeeType;

    @ApiModelProperty(value = "帐号状态")
    private String status;

    @ApiModelProperty(value = "工作状态")
    private String workStatus;

    @ApiModelProperty(value = "部门ID")
    private Long deptId;

    @ApiModelProperty(value = "岗位ID")
    private Long postId;

    @ApiModelProperty(value = "常驻地国家")
    private String locationCountry;

    @ApiModelProperty(value = "常驻地省份")
    private String locationProvince;

    @ApiModelProperty(value = "常驻地城市")
    private String locationCity;

    @ApiModelProperty(value = "班次性质（FIXED_CLASS,MULTIPLE_CLASS）")
    private String classNature;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "日历主键")
    private Long calendarId;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "打卡规则主键")
    private Long punchConfigId;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "打卡规则类型：1.免打卡 2.班次固定打卡 3灵活打卡一次 4.灵活打卡两次")
    private String punchConfigType;

    @ApiModelProperty(value = "班次计划 (OFF,H,CLASS,NO_CLASS)")
    private String dayShiftRule;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "班次规则主键")
    private Long punchClassConfigId;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "补卡规则主键")
    private Long reissueCardConfigId;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "加班规则主键")
    private Long overTimeConfigId;

    @ApiModelProperty(value = "请假时长(分钟)")
    private BigDecimal leaveMinutes;

    @ApiModelProperty(value = "外勤时长(分钟)")
    private BigDecimal oooMinutes;

    @ApiModelProperty(value = "加班时长(分钟)")
    private BigDecimal overTimeMinutes;

    @ApiModelProperty(value = "延时时长(分钟)")
    private BigDecimal delayMinutes;

    @ApiModelProperty(value = "迟到时长(分钟)")
    private BigDecimal lateMinutes;

    @ApiModelProperty(value = "早退时长(分钟)")
    private BigDecimal leaveEarlyMinutes;

    @ApiModelProperty(value = "初始考勤结果 0: 异常 1: 正常")
    private Integer initResult;

    @ApiModelProperty(value = "最终考勤结果 0: 异常 1: 正常")
    private Integer finalResult;

    @ApiModelProperty(value = "实际出勤时长(不含休息)-min")
    private BigDecimal actualAttendanceMinutes;

    @ApiModelProperty(value = "工时异常时长-min")
    private BigDecimal abnormalWorkMinutes;

    @ApiModelProperty(value = "最终工作时长(不含休息)-min")
    private BigDecimal finalWorkMinutes;

    @ApiModelProperty(value = "员工出勤结果")
    private String attendanceResult;

    @ApiModelProperty(value = "是否缺勤 0: 未缺勤 1: 缺勤")
    private Integer absentResult;

    /**
     * 是否为工作日
     */
    public Boolean areWorkDay(){
        return DayShiftRuleEnum.CLASS.getCode().equals(this.dayShiftRule);
    }

    /**
     * 是否为休息日或节假日
     */
    public Boolean areOffOrHDay(){
        return DayShiftRuleEnum.OFF.getCode().equals(this.dayShiftRule) ||
                DayShiftRuleEnum.H.getCode().equals(this.dayShiftRule);
    }
}
