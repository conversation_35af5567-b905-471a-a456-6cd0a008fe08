package com.imile.attendance.infrastructure.repository.report.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 考勤日报统计-关联单据子表
 *
 * <AUTHOR>
 * @Date 2025/6/17
 * @Description
 */
@ApiModel(description = "考勤日报统计-关联单据子表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("attendance_day_report_form")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceDayReportFormDO extends BaseDO {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "日报主键")
    private Long dayReportId;

    @ApiModelProperty(value = "单据主键")
    private Long formId;

    @ApiModelProperty(value = "单据编码")
    private String applicationCode;

    @ApiModelProperty(value = "审批单id")
    private Long approvalId;

    @ApiModelProperty(value = "单据类型")
    private String formType;

    @ApiModelProperty(value = "单据状态")
    private String formStatus;
}
