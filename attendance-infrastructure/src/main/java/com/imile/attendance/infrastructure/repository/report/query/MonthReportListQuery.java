package com.imile.attendance.infrastructure.repository.report.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.query.ResourceQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 月报分页查询类
 * <AUTHOR> chen
 * @Date 2025/6/23 
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MonthReportListQuery extends ResourceQuery {

    /**
     * 工作状态
     */
    private String workStatus;

    /**
     * 账号状态
     */
    private String accountStatus;

    /**
     * 用工类型(多选，默认全部)
     */
    private List<String> employeeTypeList;

    /**
     * 部门id(多选)
     */
    private List<Long> deptIds;

    /**
     * 岗位id(多选)
     */
    private List<Long> postIds;

    /**
     * 常驻国
     */
    private String locationCountry;
    /**
     * 常驻省
     */
    private String locationProvince;
    /**
     * 常驻市
     */
    private String locationCity;

    /**
     * 考勤年份  2025
     */
    @NotNull(message = "attendance year can not be null")
    private String attendanceYear;

    /**
     * 考勤月份  01,02,10,12
     */
    @NotNull(message = "attendance month can not be null")
    private String attendanceMonth;

    /**
     * 用户id(多选)
     */
    private List<Long> userIds;


    //==============内部字段======================

    //==============导出字段======================

    /**
     * 用户编码(多选)
     */
    private List<String> userCodes;

    /**
     * 是否导出
     */
    private Boolean areExport = false;

    /**
     * 权限常驻国
     */
    private List<String> authLocationCountryList;

    /**
     * 权限部门
     */
    private List<Long> authDeptIdList;

    /**
     * 是否有部门权限
     */
    private Boolean hasDeptPermission;

    /**
     * 是否有国家权限
     */
    private Boolean hasCountryPermission;

    /**
     * 是否有与国家、部门权限
     */
    private Boolean hasAndDeptAndCountryPermission;

    /**
     * 是否有或国家、部门权限
     */
    private Boolean hasOrDeptAndCountryPermission;

    /**
     * 前端是否选择了部门
     */
    private Boolean isChooseDept;

    /**
     * 仓内国家
     */
    private List<String> wareHouseCountry;

    /**
     * 考勤管理劳务派遣范围国家
     */
    private List<String> osCountry;

    /**
     * 当前日期(yyyy-MM-dd HH:mm:ss)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dateTime;
}
