package com.imile.attendance.infrastructure.repository.report.query;

import lombok.Data;

import java.util.List;

/**
 * 员工日报查询Query
 *
 * <AUTHOR>
 * @date 2025/6/9
 */
@Data
public class UserDayReportQuery {
    /**
     * 用工类型
     */
    private List<String> employeeTypeList;

    /**
     * 用户id(多选)
     */
    private List<Long> userIds;

    /**
     * 岗位id(多选)
     */
    private List<Long> postIds;

    /**
     * 部门id(多选)
     */
    private List<Long> deptIds;

    /**
     * 常驻国
     */
    private String locationCountry;
    /**
     * 常驻省
     */
    private String locationProvince;
    /**
     * 常驻市
     */
    private String locationCity;

    /**
     * 打卡类型
     */
    private String punchType;

    /**
     * 查询开始时间(yyyyMMdd)
     */
    private Long startDay;

    /**
     * 查询结束时间(yyyyMMdd)
     */
    private Long endDay;

    /**
     * 排班计划
     */
    private String dayShiftRule;

    /**
     * 初始考勤结果
     */
    private String initResult;

    /**
     * 最终考勤结果
     */
    private String finalResult;

    /**
     * 日期集合
     */
    private List<Long> dayIds;

    //==============内部字段======================

    /**
     * 权限常驻国
     */
    private List<String> authLocationCountryList;

    /**
     * 权限部门
     */
    private List<Long> authDeptIdList;
}
