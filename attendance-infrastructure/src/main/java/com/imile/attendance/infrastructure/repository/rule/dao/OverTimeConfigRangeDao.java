package com.imile.attendance.infrastructure.repository.rule.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7
 * @Description
 */
public interface OverTimeConfigRangeDao extends IService<OverTimeConfigRangeDO> {

    /**
     * 根据用户id列表查询加班范围配置
     *
     * @param userIds 用户id列表
     * @return 加班范围配置
     */
    List<OverTimeConfigRangeDO> listConfigRanges(List<Long> userIds);

    /**
     * 根据用户id列表查询启用的加班范围配置(不区分是否最新)
     *
     * @param userIds 用户id列表
     * @return 启用的加班范围配置
     */
    List<OverTimeConfigRangeDO> listActivedConfigRanges(List<Long> userIds);

    /**
     * 根据用户id列表查询未删除的加班范围配置（不区分是否启用）
     *
     * @param userIds 用户id列表
     * @return 未删除的加班范围配置
     */
    List<OverTimeConfigRangeDO> listNotDeletedConfigRanges(List<Long> userIds);

    /**
     * 查询员工所有的适用范围
     *
     * @param userId 用户ID
     * @return 员工所有的适用范围
     */
    List<OverTimeConfigRangeDO> listAllConfigRanges(Long userId);


    /**
     * 根据用户id列表获取当前配置适用范围(不区分是否最新和状态)
     */
    List<OverTimeConfigRangeDO> listAllRangeByUserIds(List<Long> userIdList);

    /**
     * 根据配置id查询加班范围配置
     *
     * @param configId 配置id
     * @return 加班范围配置
     */
    List<OverTimeConfigRangeDO> listByConfigId(Long configId);

    /**
     * 根据配置id查询启用的加班范围配置(不区分是否最新)
     *
     * @param configId 配置id
     * @return 启用的加班范围配置
     */
    List<OverTimeConfigRangeDO> listActivedConfigByConfigId(Long configId);

    /**
     * 根据配置id查询未启用的加班范围配置
     *
     * @param configId 配置id
     * @return 未启用的加班范围配置
     */
    List<OverTimeConfigRangeDO> listDisabledByConfigId(Long configId);

    /**
     * 根据配置id查询未删除的加班范围配置（不区分是否启用）
     *
     * @param configId 配置id
     * @return 未删除的加班范围配置
     */
    List<OverTimeConfigRangeDO> listNotDeletedByConfigId(Long configId);

    /**
     * 根据配置id列表查询加班范围配置
     *
     * @param configIdList 配置id列表
     * @return 加班范围配置
     */
    List<OverTimeConfigRangeDO> listByConfigIds(List<Long> configIdList);

    /**
     * 根据配置id列表查询未删除的加班范围配置（不区分是否启用）
     *
     * @param configIdList 配置id列表
     * @return 未删除的加班范围配置
     */
    List<OverTimeConfigRangeDO> listNotDeletedByConfigIds(List<Long> configIdList);

    /**
     * 统计国家下在职非司机且未配置规则的用户总数（国家级规则的人数）
     *
     * @param country 国家
     * @return 国家级规则的人数
     */
    Integer countOnJobNoDriverNotConfiguredUsers(String country);

    /**
     * 根据国家查询在职非司机且未配置规则的用户列表（国家级规则的用户列表）
     *
     * @param ruleRangeUserQuery 查询条件
     * @return 国家级规则的用户列表
     */
    List<UserInfoDO> listOnJobNoDriverUsersExcludeConfigured(RuleRangeUserQuery ruleRangeUserQuery);

    /**
     * 统计多个国家下在职非司机且未配置规则的用户列表
     *
     * @param ruleRangeUserQuery 查询条件
     * @return 用户列表
     */
    List<UserInfoDO> listOnJobNoDriverMultiCountryUsersExcludeConfigured(RuleRangeUserQuery ruleRangeUserQuery);
}
