package com.imile.attendance.infrastructure.repository.rule.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.dto.DateAndTimeZoneDate;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/4/8
 */
public interface PunchClassConfigRangeDao extends IService<PunchClassConfigRangeDO> {

    /**
     * 查询最新适用范围规则
     *
     * @param userIds   用户ID集合
     * @param rangeType 规则范围
     * @return 最新适用范围规则列表
     */
    List<PunchClassConfigRangeDO> selectLatestByBizIdsAndRangeType(Collection<Long> userIds, String rangeType);

    /**
     * 查询最新且启用的适用范围规则
     *
     * @param userIds 用户ID集合
     * @return 最新适用范围规则列表
     */
    List<PunchClassConfigRangeDO> selectLatestAndActiveByBizIds(Collection<Long> userIds);

    /**
     * 查询最新适用范围规则
     * 不过滤状态
     *
     * @param userIds 用户ID集合
     * @return 最新适用范围规则列表
     */
    List<PunchClassConfigRangeDO> selectLatestByBizIds(Collection<Long> userIds);

    /**
     * 查询员工所有的适用范围
     *
     * @param userIdList 用户ID
     * @return 员工所有的适用范围
     */
    List<PunchClassConfigRangeDO> selectAllByBizIds(List<Long> userIdList);

    /**
     * 查询适用范围规则
     *
     * @param userIds 用户ID集合
     * @param status  班次状态
     */
    List<PunchClassConfigRangeDO> selectByBizIdsAndStatus(Collection<Long> userIds, String status);

    /**
     * 查询适用范围规则
     *
     * @param ruleConfigIds 班次Id集合
     * @return 适用范围规则列表
     */
    List<PunchClassConfigRangeDO> selectByRuleConfigIds(Collection<Long> ruleConfigIds);

    /**
     * 查询最新且启用的适用范围规则
     *
     * @param ruleConfigIds 班次Id集合
     * @return 适用范围规则列表
     */
    List<PunchClassConfigRangeDO> selectLatestAndActiveByRuleConfigIds(Collection<Long> ruleConfigIds);

    /**
     * 更新为历史版本
     */
    void updateToOld(Long classId);

    /**
     * 停用状态
     */
    void disabledStatus(Long classId, DateAndTimeZoneDate currentDateAndTimeZoneDate);

    /**
     * 根据用户id和班次id更新适用范围
     */
    void updateRangeByBizIdAndClassId(Long userId, List<Long> classIds, DateAndTimeZoneDate currentDateAndTimeZoneDate);

    /**
     * 更新适用范围为非最新版本
     */
    void updateToOldByBizIds(Set<Long> userId, List<String> lowerPriorityRangeTypeList);

    /**
     * 查询适用范围员工
     */
    List<UserInfoDO> listClassRangeApplyUser(RuleRangeUserQuery ruleRangeUserQuery);
}
