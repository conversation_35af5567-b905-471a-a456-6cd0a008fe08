package com.imile.attendance.infrastructure.repository.rule.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
public interface PunchClassItemConfigDao extends IService<PunchClassItemConfigDO> {

    /**
     * 查询班次时段信息
     *
     * @param id 班次时段ID
     * @return 班次时段信息
     */
    PunchClassItemConfigDO selectById(Long id);

    /**
     * 批量查询班次时段信息
     *
     * @param classIdList 班次ID列表
     * @return 班次时段信息列表
     */
    List<PunchClassItemConfigDO> selectByClassIds(List<Long> classIdList);

    /**
     * 批量查询最新班次时段信息
     *
     * @param classIdList 班次ID列表
     * @return 班次时段信息列表
     */
    List<PunchClassItemConfigDO> selectLatestByClassIds(List<Long> classIdList);

    /**
     * 启用状态
     */
    void enableStatus(Long classId);

    /**
     * 停用状态
     */
    void disabledStatus(Long classId);

}
