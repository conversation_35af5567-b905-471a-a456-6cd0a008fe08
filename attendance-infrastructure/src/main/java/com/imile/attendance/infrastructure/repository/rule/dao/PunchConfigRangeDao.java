package com.imile.attendance.infrastructure.repository.rule.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.query.PunchConfigRangeByDateQuery;
import com.imile.attendance.infrastructure.repository.rule.query.PunchRangeConfigQuery;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7
 * @Description
 */
public interface PunchConfigRangeDao extends IService<PunchConfigRangeDO> {

    /**
     * 根据用户id列表获取当前最新的打卡规则配置适用范围
     */
    List<PunchConfigRangeDO> listConfigRanges(List<Long> userIds);

    /**
     * 根据用户id列表获取启用的打卡规则配置适用范围(不区分是否最新)
     */
    List<PunchConfigRangeDO> listActivedConfigRanges(List<Long> userIds);

    /**
     * 根据用户id列表获取当前最新的打卡规则配置适用范围(不区分是否停启用)
     */
    List<PunchConfigRangeDO> listNotDeletedConfigRanges(List<Long> userIds);

    /**
     * 查询员工所有的适用范围
     *
     * @param userId 用户ID
     * @return 员工所有的适用范围
     */
    List<PunchConfigRangeDO> listAllConfigRanges(Long userId);

    /**
     * 根据配置id列表获取当前最新的打卡规则配置适用范围
     */
    List<PunchConfigRangeDO> listByConfigIds(List<Long> configIdList);

    /**
     * 根据配置id列表获取当前最新的打卡规则配置适用范围(不区分是否停启用)
     */
    List<PunchConfigRangeDO> listNotDeletedByConfigIds(List<Long> configIdList);

    /**
     * 根据配置id获取当前最新的打卡规则配置适用范围
     */
    List<PunchConfigRangeDO> listByConfigId(Long configId);

    /**
     * 根据用户id列表获取启用的打卡规则配置适用范围(不区分是否最新)
     */
    List<PunchConfigRangeDO> listActivedConfigByConfigId(Long configId);

    /**
     * 根据配置id获取停用的打卡规则配置适用范围
     */
    List<PunchConfigRangeDO> listDisabledByConfigId(Long configId);

    /**
     * 根据配置id获取当前最新的打卡规则配置适用范围（不区分是否启用）
     */
    List<PunchConfigRangeDO> listNotDeletedByConfigId(Long configId);

    /**
     * 根据查询条件获取当前最新且启用的打卡规则配置适用范围
     */
    List<PunchConfigRangeDO> listPunchConfigRangeByQuery(PunchRangeConfigQuery userQuery);

    /**
     * 根据用户id列表获取当前配置适用范围(不区分是否最新和状态)
     */
    List<PunchConfigRangeDO> listAllRangeByUserIds(List<Long> userIdList);

    /**
     * 统计国家下在职非司机且未配置规则的用户总数
     *
     * @param country 国家代码
     * @return 用户总数
     */
    Integer countOnJobNoDriverNotConfiguredUsers(String country);

    /**
     * 根据查询条件获取当前最新的打卡规则配置适用范围
     */
    List<UserInfoDO> listOnJobNoDriverUsersExcludeConfigured(RuleRangeUserQuery ruleRangeUserQuery);

    /**
     * 统计多个国家下在职非司机且未配置规则的用户列表
     *
     * @param ruleRangeUserQuery 查询条件
     * @return 用户列表
     */
    List<UserInfoDO> listOnJobNoDriverMultiCountryUsersExcludeConfigured(RuleRangeUserQuery ruleRangeUserQuery);

    /**
     * 根据日期范围查询规则
     *
     * @param query
     * @return
     */
    List<PunchConfigRangeDO> selectConfigRangeByDate(PunchConfigRangeByDateQuery query);

}
