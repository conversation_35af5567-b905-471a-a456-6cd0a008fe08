package com.imile.attendance.infrastructure.repository.rule.dao;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.rule.query.ReissueCardConfigPageQuery;
import com.imile.attendance.infrastructure.repository.rule.query.ReissueCardConfigQuery;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7
 * @Description
 */
public interface ReissueCardConfigDao extends IService<ReissueCardConfigDO> {

    /**
     * 根据名称获取补卡配置
     * 
     * @param name 名称
     * @return 补卡配置
     */
    ReissueCardConfigDO getByName(String name);

    /**
     * 根据配置编码获取最新补卡配置
     * 
     * @param configNo 配置编码
     * @return 补卡配置
     */
    ReissueCardConfigDO getLatestByConfigNo(String configNo);

    /**
     * 根据国家获取补卡配置
     * 
     * @param country 国家
     * @return 补卡配置列表
     */
    List<ReissueCardConfigDO> getByCountry(String country);

    /**
     * 根据国家列表获取补卡配置
     *
     * @param countries 国家列表
     * @return 补卡配置列表
     */
    List<ReissueCardConfigDO> listByCountries(List<String> countries);

    /**
     * 查询配置（最新且启用的）
     * 
     * @param configIdList 配置ID列表
     * @return 补卡配置列表
     */
    List<ReissueCardConfigDO> listLatestByConfigIds(List<Long> configIdList);

    /**
     * 查询配置（非已删除的，不区分是否启用）
     *
     * @param configIdList 配置ID列表
     * @return 打卡配置列表
     */
    List<ReissueCardConfigDO> listByConfigIds(List<Long> configIdList);

    /**
     * 查询配置(不区分是否启用)
     * 
     * @param configNo 配置编码
     * @return 补卡配置列表
     */
    List<ReissueCardConfigDO> listByConfigNo(String configNo);

    /**
     * 补卡规则列表查询
     * 
     * @param query 查询条件
     * @return 补卡配置列表
     */
    List<ReissueCardConfigDO> listByQuery(ReissueCardConfigQuery query);

    /**
     * 分页查询补卡规则
     *
     * @param query 查询条件
     * @return 补卡配置列表
     */
    List<ReissueCardConfigDO> pageQuery(ReissueCardConfigPageQuery query);

}
