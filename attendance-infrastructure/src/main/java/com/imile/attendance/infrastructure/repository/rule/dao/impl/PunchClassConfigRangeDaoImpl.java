package com.imile.attendance.infrastructure.repository.rule.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.dto.DateAndTimeZoneDate;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.mapper.PunchClassConfigRangeMapper;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/4/8
 */
@Component
@RequiredArgsConstructor
public class PunchClassConfigRangeDaoImpl extends ServiceImpl<PunchClassConfigRangeMapper, PunchClassConfigRangeDO> implements PunchClassConfigRangeDao {


    @Override
    public List<PunchClassConfigRangeDO> selectLatestByBizIdsAndRangeType(Collection<Long> userIds, String rangeType) {
        if (CollectionUtils.isEmpty(userIds) || StringUtils.isBlank(rangeType)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchClassConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchClassConfigRangeDO::getBizId, userIds);
        queryWrapper.eq(PunchClassConfigRangeDO::getRangeType, rangeType);
        queryWrapper.eq(PunchClassConfigRangeDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchClassConfigRangeDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(PunchClassConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<PunchClassConfigRangeDO> selectLatestAndActiveByBizIds(Collection<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchClassConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchClassConfigRangeDO::getBizId, userIds);
        queryWrapper.eq(PunchClassConfigRangeDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchClassConfigRangeDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(PunchClassConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<PunchClassConfigRangeDO> selectLatestByBizIds(Collection<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchClassConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchClassConfigRangeDO::getBizId, userIds);
        queryWrapper.eq(PunchClassConfigRangeDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchClassConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<PunchClassConfigRangeDO> selectAllByBizIds(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchClassConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchClassConfigRangeDO::getBizId, userIdList);
        queryWrapper.eq(PunchClassConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(PunchClassConfigRangeDO::getCreateDate);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<PunchClassConfigRangeDO> selectByBizIdsAndStatus(Collection<Long> userIds, String status) {
        if (CollectionUtils.isEmpty(userIds) || StringUtils.isEmpty(status)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchClassConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchClassConfigRangeDO::getBizId, userIds);
        queryWrapper.eq(PunchClassConfigRangeDO::getStatus, status);
        queryWrapper.eq(PunchClassConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByAsc(PunchClassConfigRangeDO::getId);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<PunchClassConfigRangeDO> selectByRuleConfigIds(Collection<Long> ruleConfigIds) {
        if (CollectionUtils.isEmpty(ruleConfigIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchClassConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchClassConfigRangeDO::getRuleConfigId, ruleConfigIds);
        queryWrapper.eq(PunchClassConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<PunchClassConfigRangeDO> selectLatestAndActiveByRuleConfigIds(Collection<Long> ruleConfigIds) {
        if (CollectionUtils.isEmpty(ruleConfigIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchClassConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchClassConfigRangeDO::getRuleConfigId, ruleConfigIds);
        queryWrapper.eq(PunchClassConfigRangeDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchClassConfigRangeDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(PunchClassConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public void updateToOld(Long classId) {
        if (Objects.isNull(classId)) {
            return;
        }
        LambdaQueryWrapper<PunchClassConfigRangeDO> updateWrapper = Wrappers.lambdaQuery();
        updateWrapper.eq(PunchClassConfigRangeDO::getRuleConfigId, classId);
        PunchClassConfigRangeDO model = new PunchClassConfigRangeDO();
        model.setIsLatest(BusinessConstant.N);
        BaseDOUtil.fillDOUpdate(model);
        update(model, updateWrapper);
    }

    @Override
    public void disabledStatus(Long classId, DateAndTimeZoneDate currentDateAndTimeZoneDate) {
        LambdaQueryWrapper<PunchClassConfigRangeDO> updateWrapper = Wrappers.lambdaQuery();
        updateWrapper.eq(PunchClassConfigRangeDO::getRuleConfigId, classId);
        updateWrapper.eq(PunchClassConfigRangeDO::getStatus, StatusEnum.ACTIVE.getCode());
        PunchClassConfigRangeDO model = new PunchClassConfigRangeDO();
        model.setStatus(StatusEnum.DISABLED.getCode());
        model.setExpireTime(currentDateAndTimeZoneDate.getTimeZoneDate());
        model.setExpireTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
        BaseDOUtil.fillDOUpdate(model);
        update(model, updateWrapper);
    }

    @Override
    public void updateRangeByBizIdAndClassId(Long userId, List<Long> classIds, DateAndTimeZoneDate currentDateAndTimeZoneDate) {
        LambdaQueryWrapper<PunchClassConfigRangeDO> updateWrapper = Wrappers.lambdaQuery();
        updateWrapper.in(PunchClassConfigRangeDO::getRuleConfigId, classIds);
        updateWrapper.eq(PunchClassConfigRangeDO::getBizId, userId);
        PunchClassConfigRangeDO model = new PunchClassConfigRangeDO();
        model.setIsLatest(BusinessConstant.N);
        model.setExpireTime(currentDateAndTimeZoneDate.getTimeZoneDate());
        model.setExpireTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
        BaseDOUtil.fillDOUpdate(model);
        update(model, updateWrapper);
    }

    @Override
    public void updateToOldByBizIds(Set<Long> userIdList, List<String> lowerPriorityRangeTypeList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }
        LambdaQueryWrapper<PunchClassConfigRangeDO> updateWrapper = Wrappers.lambdaQuery();
        updateWrapper.in(PunchClassConfigRangeDO::getBizId, userIdList);
        updateWrapper.in(PunchClassConfigRangeDO::getStatus, StatusEnum.ACTIVE.getCode());
        updateWrapper.in(CollectionUtils.isNotEmpty(lowerPriorityRangeTypeList), PunchClassConfigRangeDO::getRangeType, lowerPriorityRangeTypeList);
        PunchClassConfigRangeDO model = new PunchClassConfigRangeDO();
        model.setExpireTime(new Date());
        model.setIsLatest(BusinessConstant.N);
        BaseDOUtil.fillDOUpdate(model);
        update(model, updateWrapper);
    }

    @Override
    public List<UserInfoDO> listClassRangeApplyUser(RuleRangeUserQuery ruleRangeUserQuery) {
        return this.baseMapper.listClassRangeApplyUser(ruleRangeUserQuery);
    }
}
