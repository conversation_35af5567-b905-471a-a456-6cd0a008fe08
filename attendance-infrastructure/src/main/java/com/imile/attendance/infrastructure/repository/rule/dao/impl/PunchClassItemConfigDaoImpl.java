package com.imile.attendance.infrastructure.repository.rule.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassItemConfigDao;
import com.imile.attendance.infrastructure.repository.rule.mapper.PunchClassItemConfigMapper;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Component
@RequiredArgsConstructor
public class PunchClassItemConfigDaoImpl extends ServiceImpl<PunchClassItemConfigMapper, PunchClassItemConfigDO> implements PunchClassItemConfigDao {

    @Override
    public PunchClassItemConfigDO selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        LambdaQueryWrapper<PunchClassItemConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PunchClassItemConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(PunchClassItemConfigDO::getId, id);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<PunchClassItemConfigDO> selectByClassIds(List<Long> classIdList) {
        if (CollectionUtils.isEmpty(classIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchClassItemConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PunchClassItemConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(PunchClassItemConfigDO::getPunchClassId, classIdList);
        return this.list(queryWrapper);
    }

    @Override
    public List<PunchClassItemConfigDO> selectLatestByClassIds(List<Long> classIdList) {
        if (CollectionUtils.isEmpty(classIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchClassItemConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PunchClassItemConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(PunchClassItemConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.in(PunchClassItemConfigDO::getPunchClassId, classIdList);
        return this.list(queryWrapper);
    }

    @Override
    public void enableStatus(Long classId) {
        LambdaQueryWrapper<PunchClassItemConfigDO> updateWrapper = Wrappers.lambdaQuery();
        updateWrapper.eq(PunchClassItemConfigDO::getPunchClassId, classId);
        updateWrapper.eq(PunchClassItemConfigDO::getStatus, StatusEnum.DISABLED.getCode());
        PunchClassItemConfigDO model = new PunchClassItemConfigDO();
        model.setStatus(StatusEnum.ACTIVE.getCode());
        BaseDOUtil.fillDOUpdate(model);
        update(model, updateWrapper);
    }

    @Override
    public void disabledStatus(Long classId) {
        LambdaQueryWrapper<PunchClassItemConfigDO> updateWrapper = Wrappers.lambdaQuery();
        updateWrapper.eq(PunchClassItemConfigDO::getPunchClassId, classId);
        updateWrapper.eq(PunchClassItemConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        PunchClassItemConfigDO model = new PunchClassItemConfigDO();
        model.setStatus(StatusEnum.DISABLED.getCode());
        BaseDOUtil.fillDOUpdate(model);
        update(model, updateWrapper);
    }
}
