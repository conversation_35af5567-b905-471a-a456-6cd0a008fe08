package com.imile.attendance.infrastructure.repository.rule.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.infrastructure.common.CommonUserService;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.mapper.ReissueCardConfigRangeMapper;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.query.ReissueCardConfigRangeByDateQuery;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7
 * @Description
 */
@Component
@RequiredArgsConstructor
public class ReissueCardConfigRangeDaoImpl extends ServiceImpl<ReissueCardConfigRangeMapper, ReissueCardConfigRangeDO>
        implements ReissueCardConfigRangeDao {

    @Override
    public List<ReissueCardConfigRangeDO> listConfigRanges(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(ReissueCardConfigRangeDO::getBizId, userIds)
                .eq(ReissueCardConfigRangeDO::getIsLatest, BusinessConstant.Y)
                .eq(ReissueCardConfigRangeDO::getStatus, StatusEnum.ACTIVE.getCode())
                .eq(ReissueCardConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigRangeDO> listActivedConfigRanges(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(ReissueCardConfigRangeDO::getBizId, userIds)
                .eq(ReissueCardConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(ReissueCardConfigRangeDO::getStatus, StatusEnum.ACTIVE.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigRangeDO> listNotDeletedConfigRanges(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(ReissueCardConfigRangeDO::getBizId, userIds)
                .eq(ReissueCardConfigRangeDO::getIsLatest, BusinessConstant.Y)
                .eq(ReissueCardConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigRangeDO> listAllConfigRanges(Long userId) {
        if (Objects.isNull(userId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ReissueCardConfigRangeDO::getBizId, userId)
                .eq(ReissueCardConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .orderByDesc(ReissueCardConfigRangeDO::getCreateDate);
        return this.list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigRangeDO> listAllRangeByUserIds(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigRangeDO> queryWrapper = Wrappers.lambdaQuery(ReissueCardConfigRangeDO.class);
        queryWrapper.eq(ReissueCardConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .in(ReissueCardConfigRangeDO::getBizId, userIdList)
                .orderByDesc(ReissueCardConfigRangeDO::getCreateDate);
        return list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigRangeDO> listByConfigId(Long configId) {
        if (configId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ReissueCardConfigRangeDO::getRuleConfigId, configId);
        queryWrapper.eq(ReissueCardConfigRangeDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(ReissueCardConfigRangeDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(ReissueCardConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigRangeDO> listActivedConfigByConfigId(Long configId) {
        if (configId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ReissueCardConfigRangeDO::getRuleConfigId, configId);
        queryWrapper.eq(ReissueCardConfigRangeDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(ReissueCardConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigRangeDO> listNotDeletedByConfigId(Long configId) {
        if (configId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ReissueCardConfigRangeDO::getRuleConfigId, configId);
        queryWrapper.eq(ReissueCardConfigRangeDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(ReissueCardConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigRangeDO> listDisabledByConfigId(Long configId) {
        if (configId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ReissueCardConfigRangeDO::getRuleConfigId, configId);
        queryWrapper.eq(ReissueCardConfigRangeDO::getStatus, StatusEnum.DISABLED.getCode());
        queryWrapper.eq(ReissueCardConfigRangeDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(ReissueCardConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigRangeDO> listByConfigIds(List<Long> configIdList) {
        if (CollectionUtils.isEmpty(configIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigRangeDO> queryWrapper = Wrappers.lambdaQuery(ReissueCardConfigRangeDO.class);
        queryWrapper.eq(ReissueCardConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(ReissueCardConfigRangeDO::getStatus, StatusEnum.ACTIVE.getCode())
                .eq(ReissueCardConfigRangeDO::getIsLatest, BusinessConstant.Y)
                .in(ReissueCardConfigRangeDO::getRuleConfigId, configIdList);
        return list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigRangeDO> listNotDeletedByConfigIds(List<Long> configIdList) {
        if (CollectionUtils.isEmpty(configIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigRangeDO> queryWrapper = Wrappers.lambdaQuery(ReissueCardConfigRangeDO.class);
        queryWrapper.eq(ReissueCardConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(ReissueCardConfigRangeDO::getIsLatest, BusinessConstant.Y)
                .in(ReissueCardConfigRangeDO::getRuleConfigId, configIdList);
        return list(queryWrapper);
    }

    @Override
    public Integer countOnJobNoDriverNotConfiguredUsers(String country) {
        if (StringUtils.isEmpty(country)) {
            return 0;
        }
        List<String> employeeTypes = CommonUserService.getCountryEmployeeTypes(country);
        return this.baseMapper.countOnJobNoDriverNotConfiguredUsers(country, employeeTypes);
    }

    @Override
    public List<UserInfoDO> listOnJobNoDriverUsersExcludeConfigured(RuleRangeUserQuery ruleRangeUserQuery) {
        //多个国家单独处理
        if (CollectionUtils.isNotEmpty(ruleRangeUserQuery.getCountries())) {
            Map<Boolean, List<String>> countryMap = ruleRangeUserQuery.getCountries().stream()
                    .collect(Collectors.groupingBy(CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT::contains));
            List<String> normalCountryList = countryMap.getOrDefault(false, Collections.emptyList());
            List<String> specialCountryList = countryMap.getOrDefault(true, Collections.emptyList());
            if (CollectionUtils.isNotEmpty(normalCountryList)) {
                ruleRangeUserQuery.setNormalCountryList(normalCountryList);
                ruleRangeUserQuery.setNormalEmployeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
            }
            if (CollectionUtils.isNotEmpty(specialCountryList)) {
                ruleRangeUserQuery.setIsNeedQuerySpecialCountry(true);
                ruleRangeUserQuery.setSpecialCountryList(specialCountryList);
                ruleRangeUserQuery.setSpecialEmployeeTypeList(EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
            }
            return this.baseMapper.listOnJobNoDriverMultiCountryUsersExcludeConfigured(ruleRangeUserQuery);
        }
        if (StringUtils.isNotBlank(ruleRangeUserQuery.getCountry())) {
            List<String> employeeTypes = CommonUserService.getCountryEmployeeTypes(ruleRangeUserQuery.getCountry());
            ruleRangeUserQuery.setEmployeeTypeList(employeeTypes);
        }
        return this.baseMapper.listOnJobNoDriverUsersExcludeConfigured(ruleRangeUserQuery);
    }

    @Override
    public List<UserInfoDO> listOnJobNoDriverMultiCountryUsersExcludeConfigured(RuleRangeUserQuery ruleRangeUserQuery) {
        return this.baseMapper.listOnJobNoDriverMultiCountryUsersExcludeConfigured(ruleRangeUserQuery);
    }

    @Override
    public List<ReissueCardConfigRangeDO> selectConfigRangeByDate(ReissueCardConfigRangeByDateQuery query) {
        return this.baseMapper.selectConfigRangeByDate(query);
    }
}
