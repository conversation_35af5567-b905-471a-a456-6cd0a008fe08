package com.imile.attendance.infrastructure.repository.rule.dao.migrate;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchClassConfigMigrateDO;
import com.imile.attendance.infrastructure.repository.rule.query.PunchClassConfigQuery;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigExportDTO;

import java.util.Collection;
import java.util.List;

/**
 * 考勤班次规则迁移表DAO接口
 *
 * <AUTHOR> chen
 * @since 2025/6/16
 */
public interface PunchClassConfigMigrateDao extends IService<PunchClassConfigMigrateDO> {
}
