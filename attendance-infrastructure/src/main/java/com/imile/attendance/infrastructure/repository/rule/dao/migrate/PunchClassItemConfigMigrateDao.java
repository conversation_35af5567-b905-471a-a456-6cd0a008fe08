package com.imile.attendance.infrastructure.repository.rule.dao.migrate;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchClassItemConfigMigrateDO;

import java.util.List;

/**
 * 考勤班次规则时间配置迁移表DAO接口
 *
 * <AUTHOR> chen
 * @since 2025/6/16
 */
public interface PunchClassItemConfigMigrateDao extends IService<PunchClassItemConfigMigrateDO> {

    /**
     * 查询班次时段信息
     *
     * @param id 班次时段ID
     * @return 班次时段信息
     */
    PunchClassItemConfigMigrateDO selectById(Long id);

    /**
     * 批量查询班次时段信息
     *
     * @param classIdList 班次ID列表
     * @return 班次时段信息列表
     */
    List<PunchClassItemConfigMigrateDO> selectByClassIds(List<Long> classIdList);

    /**
     * 批量查询最新班次时段信息
     *
     * @param classIdList 班次ID列表
     * @return 班次时段信息列表
     */
    List<PunchClassItemConfigMigrateDO> selectLatestByClassIds(List<Long> classIdList);

}
