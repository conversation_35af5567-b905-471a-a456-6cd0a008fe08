package com.imile.attendance.infrastructure.repository.rule.dao.migrate;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchConfigMigrateDO;
import com.imile.attendance.infrastructure.repository.rule.query.PunchConfigPageQuery;
import com.imile.attendance.infrastructure.repository.rule.query.PunchConfigQuery;

import java.util.List;

/**
 * 考勤打卡规则迁移表DAO接口
 *
 * <AUTHOR> chen
 * @since 2025/6/16
 */
public interface PunchConfigMigrateDao extends IService<PunchConfigMigrateDO> {
}
