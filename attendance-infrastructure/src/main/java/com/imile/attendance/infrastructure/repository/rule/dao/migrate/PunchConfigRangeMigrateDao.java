package com.imile.attendance.infrastructure.repository.rule.dao.migrate;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchConfigRangeMigrateDO;

import java.util.List;

/**
 * 打卡规则适用范围迁移表DAO接口
 *
 * <AUTHOR> chen
 * @since 2025/6/16
 */
public interface PunchConfigRangeMigrateDao extends IService<PunchConfigRangeMigrateDO> {

    /**
     * 根据用户ID列表查询配置范围
     * 
     * @param userIds 用户ID列表
     * @return 配置范围列表
     */
    List<PunchConfigRangeMigrateDO> listConfigRanges(List<Long> userIds);
}
