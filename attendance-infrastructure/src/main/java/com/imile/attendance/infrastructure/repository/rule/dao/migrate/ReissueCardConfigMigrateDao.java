package com.imile.attendance.infrastructure.repository.rule.dao.migrate;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.ReissueCardConfigMigrateDO;

import java.util.List;

/**
 * 考勤补卡规则迁移表DAO接口
 *
 * <AUTHOR> chen
 * @since 2025/6/16
 */
public interface ReissueCardConfigMigrateDao extends IService<ReissueCardConfigMigrateDO> {

    /**
     * 根据名称获取补卡配置
     * 
     * @param name 名称
     * @return 补卡配置
     */
    ReissueCardConfigMigrateDO getByName(String name);

    /**
     * 根据国家获取补卡配置
     * 
     * @param country 国家代码
     * @return 补卡配置列表
     */
    List<ReissueCardConfigMigrateDO> getByCountry(String country);

    /**
     * 查询配置（非已删除的，不区分是否启用）
     * 
     * @param configIdList 配置ID列表
     * @return 补卡配置列表
     */
    List<ReissueCardConfigMigrateDO> listByConfigIds(List<Long> configIdList);

    /**
     * 查询配置（最新且启用的）
     * 
     * @param configIdList 配置ID列表
     * @return 补卡配置列表
     */
    List<ReissueCardConfigMigrateDO> listLatestByConfigIds(List<Long> configIdList);

    /**
     * 根据configNo获取最新补卡规则
     * 
     * @param configNo 配置编码
     * @return 补卡配置
     */
    ReissueCardConfigMigrateDO getLatestByConfigNo(String configNo);
}
