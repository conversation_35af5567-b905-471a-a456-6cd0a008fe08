package com.imile.attendance.infrastructure.repository.rule.dao.migrate;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.ReissueCardConfigRangeMigrateDO;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;

import java.util.List;

/**
 * 补卡规则适用范围迁移表DAO接口
 *
 * <AUTHOR> chen
 * @since 2025/6/16
 */
public interface ReissueCardConfigRangeMigrateDao extends IService<ReissueCardConfigRangeMigrateDO> {

    /**
     * 根据用户ID列表查询配置范围
     * 
     * @param userIds 用户ID列表
     * @return 配置范围列表
     */
    List<ReissueCardConfigRangeMigrateDO> listConfigRanges(List<Long> userIds);

    /**
     * 根据规则配置ID查询范围
     * 
     * @param ruleConfigId 规则配置ID
     * @return 配置范围列表
     */
    List<ReissueCardConfigRangeMigrateDO> listByRuleConfigId(Long ruleConfigId);

    /**
     * 根据规则配置ID列表查询范围
     * 
     * @param ruleConfigIds 规则配置ID列表
     * @return 配置范围列表
     */
    List<ReissueCardConfigRangeMigrateDO> listByRuleConfigIds(List<Long> ruleConfigIds);

    /**
     * 根据规则编码查询范围
     * 
     * @param ruleConfigNo 规则编码
     * @return 配置范围列表
     */
    List<ReissueCardConfigRangeMigrateDO> listByRuleConfigNo(String ruleConfigNo);

    /**
     * 根据规则编码列表查询范围
     * 
     * @param ruleConfigNos 规则编码列表
     * @return 配置范围列表
     */
    List<ReissueCardConfigRangeMigrateDO> listByRuleConfigNos(List<String> ruleConfigNos);

    /**
     * 根据业务ID和范围类型查询
     * 
     * @param bizId 业务ID
     * @param rangeType 范围类型
     * @return 配置范围列表
     */
    List<ReissueCardConfigRangeMigrateDO> listByBizIdAndRangeType(Long bizId, String rangeType);

    /**
     * 根据业务ID列表和范围类型查询
     * 
     * @param bizIds 业务ID列表
     * @param rangeType 范围类型
     * @return 配置范围列表
     */
    List<ReissueCardConfigRangeMigrateDO> listByBizIdsAndRangeType(List<Long> bizIds, String rangeType);


    /**
     * 统计国家下在职非司机且未配置规则的用户列表
     *
     * @param ruleRangeUserQuery 查询条件
     * @return 用户列表
     */
    List<UserInfoDO> listOnJobNoDriverUsersExcludeConfigured(RuleRangeUserQuery ruleRangeUserQuery);
}
