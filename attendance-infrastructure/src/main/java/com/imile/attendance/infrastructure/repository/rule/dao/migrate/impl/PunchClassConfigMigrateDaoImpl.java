package com.imile.attendance.infrastructure.repository.rule.dao.migrate.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.rule.dao.migrate.PunchClassConfigMigrateDao;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigExportDTO;
import com.imile.attendance.infrastructure.repository.rule.mapper.migrate.PunchClassConfigMigrateMapper;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchClassConfigMigrateDO;
import com.imile.attendance.infrastructure.repository.rule.query.PunchClassConfigQuery;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 考勤班次规则迁移表DAO实现类
 *
 * <AUTHOR> chen
 * @since 2025/6/16
 */
@Component
@RequiredArgsConstructor
public class PunchClassConfigMigrateDaoImpl extends ServiceImpl<PunchClassConfigMigrateMapper, PunchClassConfigMigrateDO> implements PunchClassConfigMigrateDao {

}
