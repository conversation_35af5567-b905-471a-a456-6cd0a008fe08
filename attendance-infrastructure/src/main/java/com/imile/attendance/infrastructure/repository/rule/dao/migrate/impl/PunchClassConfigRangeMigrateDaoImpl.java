package com.imile.attendance.infrastructure.repository.rule.dao.migrate.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.rule.dao.migrate.PunchClassConfigRangeMigrateDao;
import com.imile.attendance.infrastructure.repository.rule.mapper.migrate.PunchClassConfigRangeMigrateMapper;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchClassConfigRangeMigrateDO;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 考勤班次规则适用范围迁移表DAO实现类
 *
 * <AUTHOR> chen
 * @since 2025/6/16
 */
@Component
@RequiredArgsConstructor
public class PunchClassConfigRangeMigrateDaoImpl extends ServiceImpl<PunchClassConfigRangeMigrateMapper, PunchClassConfigRangeMigrateDO>
        implements PunchClassConfigRangeMigrateDao {

}
