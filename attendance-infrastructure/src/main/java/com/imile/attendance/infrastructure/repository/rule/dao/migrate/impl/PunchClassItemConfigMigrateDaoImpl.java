package com.imile.attendance.infrastructure.repository.rule.dao.migrate.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.rule.dao.migrate.PunchClassItemConfigMigrateDao;
import com.imile.attendance.infrastructure.repository.rule.mapper.migrate.PunchClassItemConfigMigrateMapper;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchClassItemConfigMigrateDO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 考勤班次规则时间配置迁移表DAO实现类
 *
 * <AUTHOR> chen
 * @since 2025/6/16
 */
@Component
@RequiredArgsConstructor
public class PunchClassItemConfigMigrateDaoImpl extends ServiceImpl<PunchClassItemConfigMigrateMapper, PunchClassItemConfigMigrateDO> implements PunchClassItemConfigMigrateDao {

    @Override
    public PunchClassItemConfigMigrateDO selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        LambdaQueryWrapper<PunchClassItemConfigMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PunchClassItemConfigMigrateDO::getId, id);
        queryWrapper.eq(PunchClassItemConfigMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<PunchClassItemConfigMigrateDO> selectByClassIds(List<Long> classIdList) {
        if (CollectionUtils.isEmpty(classIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchClassItemConfigMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchClassItemConfigMigrateDO::getPunchClassId, classIdList);
        queryWrapper.eq(PunchClassItemConfigMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByAsc(PunchClassItemConfigMigrateDO::getSortNo);
        return list(queryWrapper);
    }

    @Override
    public List<PunchClassItemConfigMigrateDO> selectLatestByClassIds(List<Long> classIdList) {
        if (CollectionUtils.isEmpty(classIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchClassItemConfigMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchClassItemConfigMigrateDO::getPunchClassId, classIdList);
        queryWrapper.eq(PunchClassItemConfigMigrateDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchClassItemConfigMigrateDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(PunchClassItemConfigMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByAsc(PunchClassItemConfigMigrateDO::getSortNo);
        return list(queryWrapper);
    }
}
