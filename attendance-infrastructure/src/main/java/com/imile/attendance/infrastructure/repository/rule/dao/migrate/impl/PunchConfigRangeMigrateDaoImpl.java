package com.imile.attendance.infrastructure.repository.rule.dao.migrate.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.rule.dao.migrate.PunchConfigRangeMigrateDao;
import com.imile.attendance.infrastructure.repository.rule.mapper.migrate.PunchConfigRangeMigrateMapper;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchConfigRangeMigrateDO;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 打卡规则适用范围迁移表DAO实现类
 *
 * <AUTHOR> chen
 * @since 2025/6/16
 */
@Component
@RequiredArgsConstructor
public class PunchConfigRangeMigrateDaoImpl extends ServiceImpl<PunchConfigRangeMigrateMapper, PunchConfigRangeMigrateDO>
        implements PunchConfigRangeMigrateDao {

    @Override
    public List<PunchConfigRangeMigrateDO> listConfigRanges(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigRangeMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchConfigRangeMigrateDO::getBizId, userIds)
                .eq(PunchConfigRangeMigrateDO::getIsLatest, BusinessConstant.Y)
                .eq(PunchConfigRangeMigrateDO::getStatus, StatusEnum.ACTIVE.getCode())
                .eq(PunchConfigRangeMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }
}
