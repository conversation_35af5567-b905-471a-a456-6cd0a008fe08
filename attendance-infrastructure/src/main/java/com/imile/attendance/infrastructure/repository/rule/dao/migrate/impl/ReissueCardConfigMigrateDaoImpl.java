package com.imile.attendance.infrastructure.repository.rule.dao.migrate.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.rule.dao.migrate.ReissueCardConfigMigrateDao;
import com.imile.attendance.infrastructure.repository.rule.mapper.migrate.ReissueCardConfigMigrateMapper;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.ReissueCardConfigMigrateDO;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 考勤补卡规则迁移表DAO实现类
 *
 * <AUTHOR> chen
 * @since 2025/6/16
 */
@Component
@RequiredArgsConstructor
public class ReissueCardConfigMigrateDaoImpl extends ServiceImpl<ReissueCardConfigMigrateMapper, ReissueCardConfigMigrateDO> implements ReissueCardConfigMigrateDao {

    @Override
    public ReissueCardConfigMigrateDO getByName(String name) {
        LambdaQueryWrapper<ReissueCardConfigMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ReissueCardConfigMigrateDO::getConfigName, name)
                .eq(ReissueCardConfigMigrateDO::getIsLatest, BusinessConstant.Y)
                .eq(ReissueCardConfigMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(ReissueCardConfigMigrateDO::getStatus, StatusEnum.ACTIVE.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigMigrateDO> getByCountry(String country) {
        if (StringUtils.isBlank(country)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ReissueCardConfigMigrateDO::getCountry, country)
                .eq(ReissueCardConfigMigrateDO::getIsLatest, BusinessConstant.Y)
                .eq(ReissueCardConfigMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(ReissueCardConfigMigrateDO::getStatus, StatusEnum.ACTIVE.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigMigrateDO> listByConfigIds(List<Long> configIdList) {
        if (CollectionUtils.isEmpty(configIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(ReissueCardConfigMigrateDO::getId, configIdList)
                .eq(ReissueCardConfigMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigMigrateDO> listLatestByConfigIds(List<Long> configIdList) {
        if (CollectionUtils.isEmpty(configIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(ReissueCardConfigMigrateDO::getId, configIdList)
                .eq(ReissueCardConfigMigrateDO::getIsLatest, BusinessConstant.Y)
                .eq(ReissueCardConfigMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(ReissueCardConfigMigrateDO::getStatus, StatusEnum.ACTIVE.getCode());
        return list(queryWrapper);
    }

    @Override
    public ReissueCardConfigMigrateDO getLatestByConfigNo(String configNo) {
        if (StringUtils.isBlank(configNo)) {
            return null;
        }
        LambdaQueryWrapper<ReissueCardConfigMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ReissueCardConfigMigrateDO::getConfigNo, configNo)
                .eq(ReissueCardConfigMigrateDO::getIsLatest, BusinessConstant.Y)
                .eq(ReissueCardConfigMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(ReissueCardConfigMigrateDO::getStatus, StatusEnum.ACTIVE.getCode());
        return getOne(queryWrapper);
    }
}
