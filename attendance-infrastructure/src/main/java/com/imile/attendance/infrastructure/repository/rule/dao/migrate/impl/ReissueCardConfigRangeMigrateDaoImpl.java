package com.imile.attendance.infrastructure.repository.rule.dao.migrate.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.infrastructure.common.CommonUserService;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dao.migrate.ReissueCardConfigRangeMigrateDao;
import com.imile.attendance.infrastructure.repository.rule.mapper.migrate.ReissueCardConfigRangeMigrateMapper;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.ReissueCardConfigRangeMigrateDO;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 补卡规则适用范围迁移表DAO实现类
 *
 * <AUTHOR> chen
 * @since 2025/6/16
 */
@Component
@RequiredArgsConstructor
public class ReissueCardConfigRangeMigrateDaoImpl extends ServiceImpl<ReissueCardConfigRangeMigrateMapper, ReissueCardConfigRangeMigrateDO>
        implements ReissueCardConfigRangeMigrateDao {

    @Override
    public List<ReissueCardConfigRangeMigrateDO> listConfigRanges(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigRangeMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(ReissueCardConfigRangeMigrateDO::getBizId, userIds)
                .eq(ReissueCardConfigRangeMigrateDO::getIsLatest, BusinessConstant.Y)
                .eq(ReissueCardConfigRangeMigrateDO::getStatus, StatusEnum.ACTIVE.getCode())
                .eq(ReissueCardConfigRangeMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigRangeMigrateDO> listByRuleConfigId(Long ruleConfigId) {
        if (ruleConfigId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigRangeMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ReissueCardConfigRangeMigrateDO::getRuleConfigId, ruleConfigId)
                .eq(ReissueCardConfigRangeMigrateDO::getIsLatest, BusinessConstant.Y)
                .eq(ReissueCardConfigRangeMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigRangeMigrateDO> listByRuleConfigIds(List<Long> ruleConfigIds) {
        if (CollectionUtils.isEmpty(ruleConfigIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigRangeMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(ReissueCardConfigRangeMigrateDO::getRuleConfigId, ruleConfigIds)
                .eq(ReissueCardConfigRangeMigrateDO::getIsLatest, BusinessConstant.Y)
                .eq(ReissueCardConfigRangeMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigRangeMigrateDO> listByRuleConfigNo(String ruleConfigNo) {
        if (StringUtils.isBlank(ruleConfigNo)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigRangeMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ReissueCardConfigRangeMigrateDO::getRuleConfigNo, ruleConfigNo)
                .eq(ReissueCardConfigRangeMigrateDO::getIsLatest, BusinessConstant.Y)
                .eq(ReissueCardConfigRangeMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigRangeMigrateDO> listByRuleConfigNos(List<String> ruleConfigNos) {
        if (CollectionUtils.isEmpty(ruleConfigNos)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigRangeMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(ReissueCardConfigRangeMigrateDO::getRuleConfigNo, ruleConfigNos)
                .eq(ReissueCardConfigRangeMigrateDO::getIsLatest, BusinessConstant.Y)
                .eq(ReissueCardConfigRangeMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigRangeMigrateDO> listByBizIdAndRangeType(Long bizId, String rangeType) {
        if (bizId == null || StringUtils.isBlank(rangeType)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigRangeMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ReissueCardConfigRangeMigrateDO::getBizId, bizId)
                .eq(ReissueCardConfigRangeMigrateDO::getRangeType, rangeType)
                .eq(ReissueCardConfigRangeMigrateDO::getIsLatest, BusinessConstant.Y)
                .eq(ReissueCardConfigRangeMigrateDO::getStatus, StatusEnum.ACTIVE.getCode())
                .eq(ReissueCardConfigRangeMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigRangeMigrateDO> listByBizIdsAndRangeType(List<Long> bizIds, String rangeType) {
        if (CollectionUtils.isEmpty(bizIds) || StringUtils.isBlank(rangeType)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigRangeMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(ReissueCardConfigRangeMigrateDO::getBizId, bizIds)
                .eq(ReissueCardConfigRangeMigrateDO::getRangeType, rangeType)
                .eq(ReissueCardConfigRangeMigrateDO::getIsLatest, BusinessConstant.Y)
                .eq(ReissueCardConfigRangeMigrateDO::getStatus, StatusEnum.ACTIVE.getCode())
                .eq(ReissueCardConfigRangeMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<UserInfoDO> listOnJobNoDriverUsersExcludeConfigured(RuleRangeUserQuery ruleRangeUserQuery) {
        //多个国家单独处理
//        if (CollectionUtils.isNotEmpty(ruleRangeUserQuery.getCountries())) {
//            Map<Boolean, List<String>> countryMap = ruleRangeUserQuery.getCountries().stream()
//                    .collect(Collectors.groupingBy(CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT::contains));
//            List<String> normalCountryList = countryMap.getOrDefault(false, Collections.emptyList());
//            List<String> specialCountryList = countryMap.getOrDefault(true, Collections.emptyList());
//            if (CollectionUtils.isNotEmpty(normalCountryList)) {
//                ruleRangeUserQuery.setNormalCountryList(normalCountryList);
//                ruleRangeUserQuery.setNormalEmployeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
//            }
//            if (CollectionUtils.isNotEmpty(specialCountryList)) {
//                ruleRangeUserQuery.setIsNeedQuerySpecialCountry(true);
//                ruleRangeUserQuery.setSpecialCountryList(specialCountryList);
//                ruleRangeUserQuery.setSpecialEmployeeTypeList(EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
//            }
//            return this.baseMapper.listOnJobNoDriverMultiCountryUsersExcludeConfigured(ruleRangeUserQuery);
//        }
        if (StringUtils.isNotBlank(ruleRangeUserQuery.getCountry())) {
            List<String> employeeTypes = CommonUserService.getCountryEmployeeTypes(ruleRangeUserQuery.getCountry());
            ruleRangeUserQuery.setEmployeeTypeList(employeeTypes);
        }
        return this.baseMapper.listOnJobNoDriverUsersExcludeConfigured(ruleRangeUserQuery);
    }
}
