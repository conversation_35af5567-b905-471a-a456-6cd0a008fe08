package com.imile.attendance.infrastructure.repository.rule.dto;

import com.imile.attendance.constants.BusinessConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Data
public class PunchClassConfigDTO implements Serializable {

    private Long id;

    /**
     * 班次性质
     */
    private String classNature;

    /**
     * 班次规则编码
     */
    private String configNo;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 班次类型: 0表示未选择，1:早班,2:晚班
     */
    private Integer classType;

    /**
     * 法定工作时长（不包含休息时间）
     */
    private BigDecimal legalWorkingHours;

    /**
     * 出勤时长（包含休息时间）
     */
    private BigDecimal attendanceHours;

    /**
     * 时段数
     */
    private Integer itemNum;

    /**
     * 状态 ACTIVE、DISABLED
     */
    private String status;

    /**
     * 适用国家
     */
    private String country;

    /**
     * 适用部门
     */
    private String deptIds;

    /**
     * 是否最新
     */
    private Integer isLatest;

    /**
     * 生效时间
     */
    private Date effectTime;

    /**
     * 失效时间
     */
    private Date expireTime;

    /**
     * 是否为国家级别规则
     */
    private Integer isCountryLevel;

    /**
     * 班次时段信息
     */
    private List<PunchClassItemConfigDTO> classItemConfigList;

    /**
     * 适用范围
     */
    private List<PunchClassConfigRangeDTO> classConfigRangeList;


    /**
     * String 转 List<Long>
     */
    public List<Long> convertDeptList() {
        if (StringUtils.isBlank(deptIds)){
            return Collections.emptyList();
        }
        return Arrays.stream(deptIds.split(BusinessConstant.DEFAULT_DELIMITER))
                .map(String::trim)
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

    public boolean countryLevel() {
        return Objects.equals(BusinessConstant.Y,isCountryLevel);
    }
}
