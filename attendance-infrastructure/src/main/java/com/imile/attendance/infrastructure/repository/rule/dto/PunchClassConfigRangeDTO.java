package com.imile.attendance.infrastructure.repository.rule.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Data
public class PunchClassConfigRangeDTO implements Serializable {

    private Long id;

    /**
     * 业务ID 部门id、用户ID
     */
    private Long bizId;

    /**
     * 关联规则ID
     */
    private Long ruleConfigId;

    /**
     * 关联规则编码
     */
    private String ruleConfigNo;

    /**
     * 范围类型 DEPT,USER
     */
    private String rangeType;

    /**
     * 生效时间
     */
    private Date effectTime;

    /**
     * 失效时间
     */
    private Date expireTime;

    /**
     * 是否为最新
     */
    private Integer isLatest;

    /**
     * 状态
     */
    private String status;

    /**
     * 备注说明
     */
    private String remark;

    private Date createDate;

    private String createUserCode;

    private String createUserName;

    private Date lastUpdDate;

    private String lastUpdUserCode;

    private String lastUpdUserName;

    private Integer isDelete;

    private Long recordVersion;
}
