package com.imile.attendance.infrastructure.repository.rule.dto;

import lombok.Data;

import java.util.Date;


/**
 * <AUTHOR>
 * @since 2025/5/12
 */
@Data
public class RuleConfigModifyDTO {

    /**
     * 配置ID
     */
    private Long id;

    /**
     * 国家
     */
    private String country;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 创建人
     */
    private String createUserName;
}
