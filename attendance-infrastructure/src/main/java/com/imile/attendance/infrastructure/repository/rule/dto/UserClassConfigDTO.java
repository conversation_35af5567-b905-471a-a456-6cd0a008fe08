package com.imile.attendance.infrastructure.repository.rule.dto;

import com.imile.attendance.enums.ClassNatureEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/18
 */
@Data
public class UserClassConfigDTO implements Serializable {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 班次性质
     */
    private String classNature;

    /**
     * 用户班次列表
     */
    private List<PunchClassConfigSelectDTO> classConfigSelectList;



    /**
     * 校验用户关联的班次是否包含在指定的班次列表中
     *
     * @param classIdList 班次ID列表
     * @return 是否包含
     */
    public Boolean checkClassIdListIsContains(List<Long> classIdList) {
        if (CollectionUtils.isEmpty(classIdList) || CollectionUtils.isEmpty(classConfigSelectList)) {
            return false;
        }
        List<Long> userClassIdList = classConfigSelectList.stream()
                .map(PunchClassConfigSelectDTO::getId)
                .collect(Collectors.toList());
        return CollectionUtils.containsAll(userClassIdList, classIdList);
    }

    /**
     * 判断用户是否为固定班次
     *
     * @return 是否为固定班次
     */
    public Boolean checkUserIsFixedClass() {
        return StringUtils.equals(classNature, ClassNatureEnum.FIXED_CLASS.name());
    }
}
