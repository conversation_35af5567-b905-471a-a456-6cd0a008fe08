package com.imile.attendance.infrastructure.repository.rule.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.common.AttendanceBaseMapper;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Mapper
@Repository
public interface PunchClassConfigRangeMapper extends AttendanceBaseMapper<PunchClassConfigRangeDO> {

    List<UserInfoDO> listClassRangeApplyUser(RuleRangeUserQuery ruleRangeUserQuery);
}
