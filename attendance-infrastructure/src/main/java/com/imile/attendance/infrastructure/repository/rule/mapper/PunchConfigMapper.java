package com.imile.attendance.infrastructure.repository.rule.mapper;

import java.util.List;

import com.imile.attendance.infrastructure.common.AttendanceBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.query.PunchConfigPageQuery;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7
 * @Description
 */
@Mapper
@Repository
public interface PunchConfigMapper extends AttendanceBaseMapper<PunchConfigDO> {

    /**
     * 根据国家列表查询国家级别的打卡配置
     * 
     * @param countryList 国家列表
     * @return 国家级别的打卡配置列表
     */
    List<PunchConfigDO> listCountryLevelConfigsByCountries(@Param("countryList") List<String> countryList);

    /**
     * 分页查询打卡配置
     *
     * @param query 查询条件
     * @return 打卡配置列表
     */
    List<PunchConfigDO> pageQuery(PunchConfigPageQuery query);

}
