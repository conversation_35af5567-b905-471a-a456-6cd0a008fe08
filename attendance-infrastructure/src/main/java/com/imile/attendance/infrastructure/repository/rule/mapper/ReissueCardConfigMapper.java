package com.imile.attendance.infrastructure.repository.rule.mapper;

import java.util.List;

import com.imile.attendance.infrastructure.common.AttendanceBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.rule.query.ReissueCardConfigPageQuery;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7
 * @Description
 */
@Mapper
@Repository
public interface ReissueCardConfigMapper extends AttendanceBaseMapper<ReissueCardConfigDO> {

    List<ReissueCardConfigDO> pageQuery(ReissueCardConfigPageQuery query);

}
