package com.imile.attendance.infrastructure.repository.rule.mapper.migrate;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.common.AttendanceBaseMapper;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchClassConfigMigrateDO;
import com.imile.attendance.infrastructure.repository.rule.query.PunchClassConfigQuery;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigExportDTO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 考勤班次规则迁移表Mapper
 *
 * <AUTHOR> chen
 * @since 2025/6/16
 */
@Mapper
@Repository
public interface PunchClassConfigMigrateMapper extends AttendanceBaseMapper<PunchClassConfigMigrateDO> {

    /**
     * 分页查询班次配置
     *
     * @param query 查询条件
     * @return 班次配置列表
     */
    List<PunchClassConfigMigrateDO> pageQuery(PunchClassConfigQuery query);

    /**
     * 班次导出查询
     *
     * @param query 查询条件
     * @return 班次导出数据列表
     */
    List<PunchClassConfigExportDTO> export(PunchClassConfigQuery query);
}
