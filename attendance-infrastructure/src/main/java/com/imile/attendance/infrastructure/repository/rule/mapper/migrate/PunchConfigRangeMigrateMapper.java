package com.imile.attendance.infrastructure.repository.rule.mapper.migrate;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.common.AttendanceBaseMapper;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchConfigRangeMigrateDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * 打卡规则适用范围迁移表Mapper
 *
 * <AUTHOR> chen
 * @since 2025/6/16
 */
@Mapper
@Repository
public interface PunchConfigRangeMigrateMapper extends AttendanceBaseMapper<PunchConfigRangeMigrateDO> {

}
