package com.imile.attendance.infrastructure.repository.rule.mapper.migrate;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.common.AttendanceBaseMapper;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.ReissueCardConfigRangeMigrateDO;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 补卡规则适用范围迁移表Mapper
 *
 * <AUTHOR> chen
 * @since 2025/6/16
 */
@Mapper
@Repository
public interface ReissueCardConfigRangeMigrateMapper extends AttendanceBaseMapper<ReissueCardConfigRangeMigrateDO> {

    /**
     * 统计国家下在职非司机且未配置规则的用户列表
     *
     * @param ruleRangeUserQuery 查询条件
     * @return 用户列表
     */
    List<UserInfoDO> listOnJobNoDriverUsersExcludeConfigured(RuleRangeUserQuery ruleRangeUserQuery);

}
