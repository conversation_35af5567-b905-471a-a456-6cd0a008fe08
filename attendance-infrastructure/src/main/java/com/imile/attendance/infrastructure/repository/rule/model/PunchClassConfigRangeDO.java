package com.imile.attendance.infrastructure.repository.rule.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import com.imile.common.annotation.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 班次规则适用范围表
 *
 * <AUTHOR>
 * @since 2025/4/7
 */
@ApiModel(description = "考勤班次规则适用范围表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("punch_class_config_range")
public class PunchClassConfigRangeDO extends BaseDO {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 业务ID 部门id、用户ID
     */
    @ApiModelProperty(value = "业务ID")
    private Long bizId;

    /**
     * 关联规则ID
     */
    @ApiModelProperty(value = "关联规则ID")
    private Long ruleConfigId;

    /**
     * 关联规则编码
     */
    @ApiModelProperty(value = "关联规则编码")
    private String ruleConfigNo;

    /**
     * 范围类型 DEPT,USER
     */
    @ApiModelProperty(value = "范围类型")
    private String rangeType;

    /**
     * 生效时间
     */
    @ApiModelProperty(value = "生效时间")
    private Date effectTime;

    /**
     * 失效时间
     */
    @ApiModelProperty(value = "失效时间")
    private Date expireTime;

    /**
     * 生效时间戳
     */
    @ApiModelProperty(value = "生效时间戳")
    private Long effectTimestamp;

    /**
     * 失效时间戳
     */
    @ApiModelProperty(value = "失效时间戳")
    private Long expireTimestamp;

    /**
     * 是否为最新
     */
    @ApiModelProperty(value = "是否为最新")
    private Integer isLatest;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 备注说明
     */
    @ApiModelProperty(value = "备注说明")
    private String remark;

    /**
     * 是否来自hr的历史考勤组
     */
    @ApiModelProperty(value = "是否来自hr的历史考勤组")
    private Integer isFromHrHistoryConfig;

}
