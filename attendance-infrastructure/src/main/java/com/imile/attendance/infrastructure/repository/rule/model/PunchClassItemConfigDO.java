package com.imile.attendance.infrastructure.repository.rule.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.util.PunchTimeCalculator;
import com.imile.common.annotation.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 考勤班次规则时间配置表
 *
 * <AUTHOR>
 * @since 2025/4/7
 */
@ApiModel(description = "考勤班次规则时间配置表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("punch_class_item_config")
@FieldNameConstants
public class PunchClassItemConfigDO extends BaseDO {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 班次规则ID
     */
    @ApiModelProperty(value = "班次规则ID")
    private Long punchClassId;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private Integer sortNo;

    /**
     * 上班时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    @ApiModelProperty(value = "上班时间")
    private Date punchInTime;

    /**
     * 下班时间 仅有时分秒信息，形如：1970-01-01 18:00:00
     */
    @ApiModelProperty(value = "下班时间")
    private Date punchOutTime;

    /**
     * 最早上班打卡时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    @ApiModelProperty(value = "最早上班打卡时间")
    private Date earliestPunchInTime;

    /**
     * 最晚上班打卡时间 仅有时分秒信息
     */
    @ApiModelProperty(value = "最晚上班打卡时间")
    private Date latestPunchInTime;

    /**
     * 最晚下班打卡时间 仅有时分秒信息，形如：1970-01-01 19:00:00
     */
    @ApiModelProperty(value = "最晚下班打卡时间")
    private Date latestPunchOutTime;

    /**
     * 是否跨天 跨天是指打卡时间是第二天：0：不跨天，1：跨天
     */
    @ApiModelProperty(value = "是否跨天")
    private Integer isAcross;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 是否最新
     */
    @ApiModelProperty(value = "是否最新")
    private Integer isLatest;

    /**
     * 弹性时间
     */
    @ApiModelProperty(value = "弹性时间")
    private BigDecimal elasticTime;

    /**
     * 法定工作时长（不包含休息时间）
     */
    @ApiModelProperty(value = "法定工作时长")
    private BigDecimal legalWorkingHours;

    /**
     * 出勤时长（包含休息时间）
     */
    @ApiModelProperty(value = "出勤时长")
    private BigDecimal attendanceHours;

    /**
     * 休息开始时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    @ApiModelProperty(value = "休息开始时间")
    private Date restStartTime;

    /**
     * 休息结束时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    @ApiModelProperty(value = "休息结束时间")
    private Date restEndTime;


    /**
     * 班次时段信息无效
     */
    public boolean notHasValidTimes() {
        return punchInTime == null
                || punchOutTime == null
                || earliestPunchInTime == null
                || latestPunchOutTime == null;
    }

    /**
     * 班次规则ID无效
     */
    public boolean notHasValidPunchClassId() {
        return punchClassId == null;
    }

    /**
     * 判断是否跨天
     */
    public boolean isAcrossDay() {
        return isAcross != null && isAcross.equals(BusinessConstant.Y);
    }

    /**
     * 判断上班是否跨天（最早打卡时间大于等于标准上班时间表示跨天）
     */
    public boolean isPunchInAcrossDay() {
        return PunchTimeCalculator.isTimeAcrossDay(earliestPunchInTime, punchInTime);
    }

    /**
     * 计算指定日期的最早打卡时间
     *
     * @param dayDate 基准日期
     * @param isPunchInAcrossDay 上班是否跨天
     * @return 完整的最早打卡时间
     */
    public Date calculateEarliestPunchInDateTime(Date dayDate, boolean isPunchInAcrossDay) {
        // 如果上班跨天，则最早打卡时间应该是前一天
        int dayOffset = isPunchInAcrossDay ? -1 : 0;
        return PunchTimeCalculator.calculatePunchTime(dayDate, earliestPunchInTime, dayOffset);
    }

    /**
     * 计算指定日期的最晚打卡时间
     *
     * @param dayDate 基准日期
     * @param isPunchInAcrossDay 上班是否跨天
     * @return 完整的最晚打卡时间
     */
    public Date calculateLatestPunchOutDateTime(Date dayDate, boolean isPunchInAcrossDay) {
        // 如果上班不跨天，则下班通常跨到第二天
        // 与原代码一致：最早时间不跨天时，最晚下班时间使用第二天的日期
        int dayOffset = isPunchInAcrossDay ? 0 : 1;
        return PunchTimeCalculator.calculatePunchTime(dayDate, latestPunchOutTime, dayOffset);
    }

    /**
     * 判断当前班次配置的打卡时间是否跨天
     * @return 是否跨天
     */
    public boolean isTimeCrossingDay() {
        return punchInTime.compareTo(punchOutTime) > -1 ||
                earliestPunchInTime.compareTo(punchInTime) > -1 ||
                latestPunchOutTime.compareTo(punchOutTime) < 1;
    }

    /**
     * 判断当前班次项目是否与指定ID匹配
     */
    public boolean matchesId(Long classItemId) {
        return this.getId() != null && this.getId().equals(classItemId);
    }
}
