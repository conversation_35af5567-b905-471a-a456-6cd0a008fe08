package com.imile.attendance.infrastructure.repository.rule.model.migrate;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.rule.RuleRangeTypeEnum;
import com.imile.common.enums.StatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Objects;

/**
 * 班次规则适用范围迁移表
 *
 * <AUTHOR> chen
 * @since 2025/6/16
 */
@ApiModel(description = "考勤班次规则适用范围迁移表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("punch_class_config_range_migrate")
public class PunchClassConfigRangeMigrateDO extends BaseDO {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 业务ID 部门id、用户ID
     */
    @ApiModelProperty(value = "业务ID")
    private Long bizId;

    /**
     * 关联规则ID
     */
    @ApiModelProperty(value = "关联规则ID")
    private Long ruleConfigId;

    /**
     * 关联规则编码
     */
    @ApiModelProperty(value = "关联规则编码")
    private String ruleConfigNo;

    /**
     * 范围类型 DEPT,USER
     */
    @ApiModelProperty(value = "范围类型")
    private String rangeType;

    /**
     * 生效时间
     */
    @ApiModelProperty(value = "生效时间")
    private Date effectTime;

    /**
     * 失效时间
     */
    @ApiModelProperty(value = "失效时间")
    private Date expireTime;

    /**
     * 生效时间戳
     */
    @ApiModelProperty(value = "生效时间戳")
    private Long effectTimestamp;

    /**
     * 失效时间戳
     */
    @ApiModelProperty(value = "失效时间戳")
    private Long expireTimestamp;

    /**
     * 是否为最新
     */
    @ApiModelProperty(value = "是否为最新")
    private Integer isLatest;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 备注说明
     */
    @ApiModelProperty(value = "备注说明")
    private String remark;

    /**
     * 是否来自hr的历史考勤组
     */
    @ApiModelProperty(value = "是否来自hr的历史考勤组")
    private Integer isFromHrHistoryConfig;

    /**
     * 判断是否为国家范围
     */
    public Boolean areCountryRange(){
        return StringUtils.equals(this.rangeType, RuleRangeTypeEnum.COUNTRY.getCode());
    }

    /**
     * 判断是否非国家范围
     */
    public Boolean areNotCountryRange(){
        return !areCountryRange();
    }

    /**
     * 判断是否为用户范围
     */
    public Boolean areUserRange(){
        return StringUtils.equals(this.rangeType, RuleRangeTypeEnum.USER.getCode());
    }

    /**
     * 判断是否为部门范围
     */
    public Boolean areDeptRange(){
        return StringUtils.equals(this.rangeType, RuleRangeTypeEnum.DEPT.getCode());
    }

    /**
     * 判断是否为启用状态
     */
    public Boolean areActive() {
        return StringUtils.equals(this.status, StatusEnum.ACTIVE.getCode());
    }

    /**
     * 判断是否为禁用状态
     */
    public Boolean areDisabled() {
        return StringUtils.equals(this.status, StatusEnum.DISABLED.getCode());
    }

    /**
     * 判断是否为最新版本
     */
    public Boolean areLatest() {
        return Objects.equals(this.isLatest, BusinessConstant.Y);
    }

    /**
     * 判断是否为启用且最新版本
     */
    public Boolean areActiveAndLatest() {
        return areActive() && areLatest();
    }
}
