package com.imile.attendance.infrastructure.repository.rule.model.migrate;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.rule.RuleRangeTypeEnum;
import com.imile.common.enums.StatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Objects;

/**
 * 打卡规则适用范围迁移表
 *
 * <AUTHOR> chen
 * @since 2025/6/16
 */
@ApiModel(description = "打卡规则适用范围迁移表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("punch_config_range_migrate")
public class PunchConfigRangeMigrateDO extends BaseDO {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "关联规则表ID")
    private Long ruleConfigId;

    @ApiModelProperty(value = "关联规则编码")
    private String ruleConfigNo;

    @ApiModelProperty(value = "业务ID 用户ID")
    private Long bizId;

    @ApiModelProperty(value = "范围类型 DEPT,USER")
    private String rangeType;

    @ApiModelProperty(value = "生效时间")
    private Date effectTime;

    @ApiModelProperty(value = "失效时间")
    private Date expireTime;

    @ApiModelProperty(value = "生效时间戳")
    private Long effectTimestamp;

    @ApiModelProperty(value = "失效时间戳")
    private Long expireTimestamp;

    @ApiModelProperty(value = "状态 ACTIVE、DISABLED")
    private String status;

    @ApiModelProperty(value = "是否为最新")
    private Integer isLatest;

    @ApiModelProperty(value = "备注说明")
    private String remark;

    /**
     * 是否来自hr的历史考勤组
     */
    @ApiModelProperty(value = "是否来自hr的历史考勤组")
    private Integer isFromHrHistoryConfig;

    /**
     * 判断是否为国家范围
     */
    public Boolean areCountryRange(){
        return StringUtils.equals(this.rangeType, RuleRangeTypeEnum.COUNTRY.getCode());
    }

    /**
     * 判断是否非国家范围
     */
    public Boolean areNotCountryRange(){
        return !areCountryRange();
    }

    /**
     * 判断是否为用户范围
     */
    public Boolean areUserRange(){
        return StringUtils.equals(this.rangeType, RuleRangeTypeEnum.USER.getCode());
    }

    /**
     * 判断是否为部门范围
     */
    public Boolean areDeptRange(){
        return StringUtils.equals(this.rangeType, RuleRangeTypeEnum.DEPT.getCode());
    }

    /**
     * 判断是否为启用状态
     */
    public Boolean areActive() {
        return StringUtils.equals(this.status, StatusEnum.ACTIVE.getCode());
    }

    /**
     * 判断是否为禁用状态
     */
    public Boolean areDisabled() {
        return StringUtils.equals(this.status, StatusEnum.DISABLED.getCode());
    }

    /**
     * 判断是否为最新版本
     */
    public Boolean areLatest() {
        return Objects.equals(this.isLatest, BusinessConstant.Y);
    }

    /**
     * 判断是否为启用且最新版本
     */
    public Boolean areActiveAndLatest() {
        return areActive() && areLatest();
    }
}
