package com.imile.attendance.infrastructure.repository.rule.model.migrate;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.common.enums.StatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 考勤补卡规则迁移表
 *
 * <AUTHOR> chen
 * @since 2025/6/16
 */
@ApiModel(description = "考勤补卡规则迁移表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("reissue_card_config_migrate")
@FieldNameConstants
public class ReissueCardConfigMigrateDO extends BaseDO {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "补卡规则编码")
    private String configNo;

    @ApiModelProperty(value = "补卡规则名称")
    private String configName;

    @ApiModelProperty(value = "每月最大补卡次数")
    private Integer maxRepunchNumber;

    @ApiModelProperty(value = "是否为国家级别规则")
    private Integer isCountryLevel;

    @ApiModelProperty(value = "状态 ACTIVE、DISABLED")
    private String status;

    @ApiModelProperty(value = "适用部门")
    private String deptIds;

    @ApiModelProperty(value = "是否最新")
    private Integer isLatest;

    @ApiModelProperty(value = "生效时间")
    private Date effectTime;

    @ApiModelProperty(value = "失效时间")
    private Date expireTime;

    @ApiModelProperty(value = "生效时间戳")
    private Long effectTimestamp;

    @ApiModelProperty(value = "失效时间戳")
    private Long expireTimestamp;

    /**
     * 判断是否为国家级别规则
     */
    public Boolean areCountryLevel() {
        return Objects.equals(this.isCountryLevel, BusinessConstant.Y);
    }

    /**
     * 获取适用部门ID列表
     */
    public List<Long> listDeptIds() {
        if (StringUtils.isNotBlank(this.deptIds)) {
            return Arrays.asList((Long[]) ConvertUtils.convert(this.deptIds.split(","), Long.class));
        }
        return new ArrayList<>();
    }

    /**
     * 判断是否为最新版本
     */
    public Boolean areLatest() {
        return Objects.equals(this.isLatest, BusinessConstant.Y);
    }

    /**
     * 判断是否为启用状态
     */
    public Boolean areActive() {
        return StringUtils.equals(this.status, StatusEnum.ACTIVE.getCode());
    }

    /**
     * 判断是否为禁用状态
     */
    public Boolean areDisabled() {
        return StringUtils.equals(this.status, StatusEnum.DISABLED.getCode());
    }

    /**
     * 判断是否为启用且最新版本
     */
    public Boolean areActiveAndLatest() {
        return areActive() && areLatest();
    }
}
