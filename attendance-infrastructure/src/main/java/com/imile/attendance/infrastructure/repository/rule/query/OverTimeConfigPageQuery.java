package com.imile.attendance.infrastructure.repository.rule.query;

import com.imile.attendance.base.PermissionBaseQuery;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/18 
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OverTimeConfigPageQuery extends PermissionBaseQuery {

    /**
     * 加班规则id列表
     */
    private List<Long> configIds;

    /**
     * 国家
     */
    private String country;

    /**
     * 加班规则名称
     */
    private String configName;

    /**
     * 部门列表
     */
    private List<Long> deptIds;

    /**
     * 状态
     */
    private String status;

    /**
     * 用户id列表
     */
    private List<Long> userIdList;

    /**
     * 权限常驻国
     */
    private List<String> countryList;
}
