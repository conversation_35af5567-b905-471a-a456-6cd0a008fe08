package com.imile.attendance.infrastructure.repository.rule.query;

import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Data
public class PunchClassConfigQuery {

    /**
     * 班次ID集合
     */
    private Set<Long> ids;

    /**
     * 状态
     */
    private String status;

    /**
     * 国家
     */
    private String country;

    /**
     * 部门
     */
    private List<Long> deptIds;

    /**
     * 账号ID
     */
    private List<Long> userIdList;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 班次性质
     */
    private String classNature;

    /**
     * 权限常驻国
     */
    private List<String> countryList;
}
