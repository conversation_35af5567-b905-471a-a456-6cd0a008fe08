package com.imile.attendance.infrastructure.repository.rule.query;

import com.imile.attendance.base.PermissionBaseQuery;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/14 
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PunchConfigPageQuery extends PermissionBaseQuery {

    /**
     * 国家
     */
    private String country;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;

    /**
     * 部门列表
     */
    private List<Long> deptIds;

    /**
     * 状态
     */
    private String status;

    /**
     * 用户id列表
     */
    private List<Long> userIdList;

    /**
     * 是否分页导出
     */
    private Boolean arePageExport = false;


    //=======内部使用=============

    /**
     * 打卡规则id列表
     */
    private List<Long> punchConfigIds;

    /**
     * 权限常驻国
     */
    private List<String> countryList;
}
