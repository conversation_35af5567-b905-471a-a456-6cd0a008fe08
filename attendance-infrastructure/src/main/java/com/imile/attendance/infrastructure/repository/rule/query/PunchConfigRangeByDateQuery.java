package com.imile.attendance.infrastructure.repository.rule.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PunchConfigRangeByDateQuery implements Serializable {
    private static final long serialVersionUID = -6099272622205258396L;

    private List<Long> userIds;

    private List<Long> ruleConfigIds;

    private Long startDate;

    private Long endDate;
}
