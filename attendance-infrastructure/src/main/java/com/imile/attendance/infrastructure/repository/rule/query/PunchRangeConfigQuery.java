package com.imile.attendance.infrastructure.repository.rule.query;

import com.imile.attendance.enums.rule.RuleRangeTypeEnum;
import com.imile.common.query.BaseQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/10 
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PunchRangeConfigQuery extends BaseQuery {

    /**
     * 业务ID
     */
    private Long bizId;
    /**
     * 业务ID集合
     */
    private Collection<Long> bizIds;

    /**
     * 范围类型 DEPT,USER
     */
    private String rangeType;
    /**
     * 打卡配置ID
     */
    private Long punchConfigId;
    /**
     * 打卡配置状态
     */
    private String punchConfigStatus;

    /**
     * 排除的打卡配置No
     */
    private Long notEqPunchConfigId;

    /**
     * 查询起始时间
     */
    private Date startTime;
    /**
     * 查询结束时间
     */
    private Date endTime;

    /**
     * punchNos的集合
     */
    private List<String> punchNos;


    public static PunchRangeConfigQuery buildByUserIdsAndPunchTime(List<Long> userIds, Date punchTime) {
        return PunchRangeConfigQuery.builder()
                .bizIds(userIds)
                .rangeType(RuleRangeTypeEnum.USER.getCode())
                .startTime(punchTime)
                .endTime(punchTime)
                .build();
    }
}
