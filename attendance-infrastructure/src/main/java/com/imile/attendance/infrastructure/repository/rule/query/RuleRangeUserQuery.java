package com.imile.attendance.infrastructure.repository.rule.query;

import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.infrastructure.common.CommonUserService;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/15
 * @Description 查询在职非司机的用户列表查询条件
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RuleRangeUserQuery {

    /**
     * 国家
     */
    private String country;

    /**
     * 国家列表
     */
    private List<String> countries;

    /**
     * 用户id
     */
    private List<Long> userIds;

    /**
     * 部门id
     */
    private List<Long> deptIds;

    /**
     * 账号或姓名(模糊搜索)
     */
    private String codeOrNameLike;

    /**
     * 班次性质
     */
    private String classNature;

    /**
     * 班次ID
     */
    private Long classId;

    /**
     * 规则类型
     */
    private List<String> rangeTypeList;

    /**
     * 用工类型，国家不为空使用
     */
    private List<String> employeeTypeList;


    //===============国家列表不为空使用====================

    /**
     * 是否需要查询特殊的国家，影响用工类型
     */
    private Boolean isNeedQuerySpecialCountry = false;

    /**
     * 特殊国家集合
     */
    private List<String> specialCountryList;

    /**
     * 常规国家集合
     */
    private List<String> normalCountryList;

    /**
     * 特殊的用工类型集合
     */
    private List<String> specialEmployeeTypeList;

    /**
     * 常规用工类型集合
     */
    private List<String> normalEmployeeTypeList;
}
