package com.imile.attendance.infrastructure.repository.shift.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.shift.model.UserCycleShiftConfigDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/17
 * @Description
 */
public interface UserCycleShiftConfigDao extends IService<UserCycleShiftConfigDO> {

    /**
     * 查询用户的循环排班规则
     */
    List<UserCycleShiftConfigDO> selectByUserIdList(List<Long> userIdList);

    /**
     * 查询所有最新的循环排班规则
     */
    List<UserCycleShiftConfigDO> selectAllLatest();

    /**
     * 批量更新员工的循环排班
     */
    void batchUpdate(List<UserCycleShiftConfigDO> cycleShiftConfigDOList);

    /**
     * 删除用户所有的循环排班
     *
     * @param userId 用户id
     */
    void deleteByUserId(Long userId);
}
