package com.imile.attendance.infrastructure.repository.shift.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.shift.dto.DayShiftConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.dto.UserShiftConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.infrastructure.repository.shift.dto.ShiftConfigUpdateToOldDTO;
import com.imile.attendance.infrastructure.repository.shift.query.UserShiftConfigQuery;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/17
 * @Description
 */
public interface UserShiftConfigDao extends IService<UserShiftConfigDO> {

    /**
     * 逻辑删除用户指定时间后的所有排班记录
     */
    void updateToOld(Long userId, Date date);

    /**
     * 更新用户指定时间后的所有排班记录为历史版本
     */
    void updateToHistory(Long userId, Long startDayId, List<Long> classIds);

    /**
     * 逻辑删除用户指定时间后的所有排班记录
     */
    void updateToOld(ShiftConfigUpdateToOldDTO updateToOldDTO);

    /**
     * 查找用户排班信息
     */
    List<UserShiftConfigDO> selectUserShift(Long userId, Long startDayId, Long endDayId);

    /**
     * 获取用户在指定天数的排班数据
     */
    List<UserShiftConfigDO> selectUserShiftByDayIds(Long userId, List<Long> dayIds);

    /**
     * 逻辑删除排班信息
     */
    void updateToDelete(List<DayShiftConfigDTO> dayShiftConfigDTOList);

    /**
     * 物理删除排班信息
     */
    int delete(List<Long> ids);

    /**
     * 获取用户规定时间段内的排班信息
     */
    List<UserShiftConfigDO> selectRecordByUserIdList(List<Long> userIdList, Long startDayId, Long endDayId);

    List<UserShiftConfigDO> selectRecordByUserIdListAndClassId(List<Long> userIdList, Long classId, Long startDayId, Long endDayId);

    /**
     * 获取用户规定时间段内的排班规则(特殊使用，不通用)
     */
    List<UserShiftConfigDO> selectRecordByDateRange(Long userId, Long startDayId, Long endDayId);

    /**
     * 获取用户在指定天数的排班数据
     */
    List<UserShiftConfigDO> selectRecordByDayList(Long userId, List<Long> dayIdList);

    /**
     * 批量获取用户的排班记录
     */
    List<UserShiftConfigDO> selectBatchUserRecord(List<Long> userIdList, List<Long> dayIdList);

    /**
     * 根据起始时间查询排班记录
     * 不过滤非最新版本记录
     */
    List<UserShiftConfigDO> selectBatchByDate(Long startDayId, Long endDayId);

    /**
     * 分页查询
     */
    List<UserShiftConfigDTO> page(UserShiftConfigQuery userShiftConfigQuery);


    /**
     * 统计需要回滚的迁移数据数量
     * 根据班次配置ID列表、日期范围和任务标识统计符合回滚条件的记录数量
     *
     * @param punchClassConfigIds 班次配置ID列表
     * @param startDayId 开始日期ID（可选）
     * @param endDayId 结束日期ID（可选）
     * @param taskFlagPrefix 任务标识前缀，用于识别迁移数据
     * @return 符合条件的记录数量
     */
    Long countMigratedDataForRollback(List<Long> punchClassConfigIds, Long startDayId, Long endDayId, String taskFlagPrefix);


    /**
     * 分页查询需要回滚的迁移数据ID列表
     * 根据班次配置ID列表、日期范围和任务标识查询符合回滚条件的记录ID，支持分页处理
     *
     * @param punchClassConfigIds 班次配置ID列表
     * @param startDayId 开始日期ID（可选）
     * @param endDayId 结束日期ID（可选）
     * @param taskFlagPrefix 任务标识前缀，用于识别迁移数据
     * @param offset 分页偏移量
     * @param limit 分页大小
     * @return 符合条件的记录ID列表
     */
    List<Long> selectMigratedDataIdsForRollback(List<Long> punchClassConfigIds, Long startDayId, Long endDayId,
                                                String taskFlagPrefix, Integer offset, Integer limit);
}
