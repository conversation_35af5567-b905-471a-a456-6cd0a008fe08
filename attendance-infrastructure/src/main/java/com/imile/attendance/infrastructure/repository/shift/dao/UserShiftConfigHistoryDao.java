package com.imile.attendance.infrastructure.repository.shift.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigHistoryDO;

import java.util.List;

/**
 * 员工排班配置历史表 DAO 接口
 *
 * <AUTHOR> chen
 * @Date 2025/6/18
 * @Description 员工排班配置历史表数据访问对象
 */
public interface UserShiftConfigHistoryDao extends IService<UserShiftConfigHistoryDO> {

    /**
     * 根据用户ID查询历史排班记录
     *
     * @param userId 用户ID
     * @return 历史排班记录列表
     */
    List<UserShiftConfigHistoryDO> selectByUserId(Long userId);

    /**
     * 根据用户ID和时间范围查询历史排班记录
     *
     * @param userId     用户ID
     * @param startDayId 开始dayId
     * @param endDayId   结束dayId
     * @return 历史排班记录列表
     */
    List<UserShiftConfigHistoryDO> selectByUserIdAndDateRange(Long userId, Long startDayId, Long endDayId);

    /**
     * 根据用户ID列表和时间范围查询历史排班记录
     *
     * @param userIdList 用户ID列表
     * @param startDayId 开始dayId
     * @param endDayId   结束dayId
     * @return 历史排班记录列表
     */
    List<UserShiftConfigHistoryDO> selectByUserIdListAndDateRange(List<Long> userIdList, Long startDayId, Long endDayId);

    /**
     * 根据dayId范围查询历史排班记录
     *
     * @param startDayId 开始dayId
     * @param endDayId   结束dayId
     * @return 历史排班记录列表
     */
    List<UserShiftConfigHistoryDO> selectByDayIdRange(Long startDayId, Long endDayId);

    /**
     * 根据用户ID和dayId列表查询历史排班记录
     *
     * @param userId    用户ID
     * @param dayIdList dayId列表
     * @return 历史排班记录列表
     */
    List<UserShiftConfigHistoryDO> selectByUserIdAndDayIds(Long userId, List<Long> dayIdList);

    /**
     * 批量插入历史排班记录
     *
     * @param historyList 历史排班记录列表
     * @return 插入成功的记录数
     */
    int batchInsert(List<UserShiftConfigHistoryDO> historyList);

    /**
     * 根据班次规则ID查询历史排班记录
     *
     * @param punchClassConfigId 班次规则ID
     * @return 历史排班记录列表
     */
    List<UserShiftConfigHistoryDO> selectByPunchClassConfigId(Long punchClassConfigId);

    /**
     * 根据排班类型查询历史排班记录
     *
     * @param shiftType 排班类型
     * @return 历史排班记录列表
     */
    List<UserShiftConfigHistoryDO> selectByShiftType(String shiftType);

    /**
     * 根据数据来源查询历史排班记录
     *
     * @param dataSource 数据来源
     * @return 历史排班记录列表
     */
    List<UserShiftConfigHistoryDO> selectByDataSource(String dataSource);


    /**
     * 批量获取用户的排班记录
     */
    List<UserShiftConfigHistoryDO> selectBatchUserRecord(List<Long> userIdList, List<Long> dayIdList);

}
