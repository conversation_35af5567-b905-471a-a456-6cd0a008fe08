package com.imile.attendance.infrastructure.repository.shift.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.shift.dao.ShiftTaskRelateUserDao;
import com.imile.attendance.infrastructure.repository.shift.mapper.ShiftTaskRelateUserMapper;
import com.imile.attendance.infrastructure.repository.shift.model.ShiftTaskRelateUserDO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> chen
 * @Date 2025/4/19 
 * @Description
 */
@Component
@RequiredArgsConstructor
public class ShiftTaskRelateUserDaoImpl extends ServiceImpl<ShiftTaskRelateUserMapper, ShiftTaskRelateUserDO> implements ShiftTaskRelateUserDao {
}
