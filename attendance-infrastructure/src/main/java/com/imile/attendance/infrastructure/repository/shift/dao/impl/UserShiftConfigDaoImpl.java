package com.imile.attendance.infrastructure.repository.shift.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.shift.dao.UserShiftConfigDao;
import com.imile.attendance.infrastructure.repository.shift.dto.DayShiftConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.dto.ShiftConfigUpdateToOldDTO;
import com.imile.attendance.infrastructure.repository.shift.dto.UserShiftConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.mapper.UserShiftConfigMapper;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.infrastructure.repository.shift.query.UserShiftConfigQuery;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/17
 * @Description
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserShiftConfigDaoImpl extends ServiceImpl<UserShiftConfigMapper, UserShiftConfigDO> implements UserShiftConfigDao {

    @Override
    public void updateToOld(Long userId, Date date) {
        LambdaQueryWrapper<UserShiftConfigDO> updateWrapper = Wrappers.lambdaQuery();
        updateWrapper.eq(UserShiftConfigDO::getUserId, userId);
        updateWrapper.gt(UserShiftConfigDO::getDayId, DateHelper.getDayId(date));
        updateWrapper.eq(UserShiftConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        UserShiftConfigDO model = new UserShiftConfigDO();
        BaseDOUtil.fillDOUpdate(model);
        model.setIsLatest(BusinessConstant.N);
        model.setIsDelete(IsDeleteEnum.YES.getCode());
        update(model, updateWrapper);
    }

    @Override
    public void updateToOld(ShiftConfigUpdateToOldDTO updateToOldDTO) {
        LambdaQueryWrapper<UserShiftConfigDO> updateWrapper = Wrappers.lambdaQuery();
        updateWrapper.eq(UserShiftConfigDO::getUserId, updateToOldDTO.getUserId());
        updateWrapper.ge(UserShiftConfigDO::getDayId, updateToOldDTO.getStartDayId());
        updateWrapper.eq(Objects.nonNull(updateToOldDTO.getAttendanceConfigId()),UserShiftConfigDO::getAttendanceConfigId, updateToOldDTO.getAttendanceConfigId());
        updateWrapper.in(CollectionUtils.isNotEmpty(updateToOldDTO.getConditionShiftTypeList()),UserShiftConfigDO::getShiftType, updateToOldDTO.getConditionShiftTypeList());
        updateWrapper.eq(UserShiftConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        UserShiftConfigDO model = new UserShiftConfigDO();
        BaseDOUtil.fillDOUpdateByUserOrSystem(model);
        model.setIsLatest(BusinessConstant.N);
        model.setIsDelete(IsDeleteEnum.YES.getCode());
        update(model, updateWrapper);
    }

    @Override
    public void updateToHistory(Long userId, Long startDayId, List<Long> classIds) {
        if (Objects.isNull(userId) || Objects.isNull(startDayId)) {
            return;
        }
        LambdaQueryWrapper<UserShiftConfigDO> updateWrapper = Wrappers.lambdaQuery();
        updateWrapper.eq(UserShiftConfigDO::getUserId, userId);
        updateWrapper.ge(UserShiftConfigDO::getDayId, startDayId);
        updateWrapper.in(CollectionUtils.isNotEmpty(classIds), UserShiftConfigDO::getPunchClassConfigId, classIds);
        updateWrapper.eq(UserShiftConfigDO::getIsLatest, BusinessConstant.Y);
        updateWrapper.eq(UserShiftConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        UserShiftConfigDO model = new UserShiftConfigDO();
        BaseDOUtil.fillDOUpdateByUserOrSystem(model);
        model.setIsLatest(BusinessConstant.N);
        update(model, updateWrapper);
    }

    @Override
    public List<UserShiftConfigDO> selectUserShift(Long userId, Long startDayId, Long endDayId) {
        if (Objects.isNull(userId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserShiftConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.gt(Objects.nonNull(startDayId), UserShiftConfigDO::getDayId, startDayId);
        queryWrapper.le(Objects.nonNull(endDayId), UserShiftConfigDO::getDayId, endDayId);
        queryWrapper.eq(UserShiftConfigDO::getUserId, userId);
        queryWrapper.eq(UserShiftConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserShiftConfigDO::getIsLatest, BusinessConstant.Y);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<UserShiftConfigDO> selectUserShiftByDayIds(Long userId, List<Long> dayIds) {
        if (Objects.isNull(userId) || CollectionUtils.isEmpty(dayIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserShiftConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserShiftConfigDO::getUserId,userId);
        queryWrapper.in(UserShiftConfigDO::getDayId, dayIds);
        queryWrapper.eq(UserShiftConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserShiftConfigDO::getIsLatest, BusinessConstant.Y);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional
    public void updateToDelete(List<DayShiftConfigDTO> dayShiftConfigDTOList) {
        UserShiftConfigDO model = new UserShiftConfigDO();
        BaseDOUtil.fillDOUpdate(model);
        model.setIsDelete(IsDeleteEnum.YES.getCode());
        List<Long> ids = dayShiftConfigDTOList.stream()
                .map(DayShiftConfigDTO::getId)
                .collect(Collectors.toList());
        LambdaQueryWrapper<UserShiftConfigDO> updateWrapper = Wrappers.lambdaQuery();
        updateWrapper.in(UserShiftConfigDO::getId, ids);
        this.baseMapper.update(model, updateWrapper);
    }

    @Override
    public int delete(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        return this.baseMapper.deleteBatchIds(ids);
    }

    @Override
    public List<UserShiftConfigDO> selectRecordByUserIdList(List<Long> userIdList, Long startDayId, Long endDayId) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<UserShiftConfigDO> queryWrapper = Wrappers.lambdaQuery();
        //这里是大于等于
        queryWrapper.ge(Objects.nonNull(startDayId), UserShiftConfigDO::getDayId, startDayId);
        //这里是小于等于
        queryWrapper.le(Objects.nonNull(endDayId), UserShiftConfigDO::getDayId, endDayId);
        queryWrapper.in(UserShiftConfigDO::getUserId, userIdList);
        queryWrapper.eq(UserShiftConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserShiftConfigDO::getIsLatest, BusinessConstant.Y);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<UserShiftConfigDO> selectRecordByUserIdListAndClassId(List<Long> userIdList, Long classId, Long startDayId, Long endDayId) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<UserShiftConfigDO> queryWrapper = Wrappers.lambdaQuery();
        //这里是大于等于
        queryWrapper.ge(Objects.nonNull(startDayId), UserShiftConfigDO::getDayId, startDayId);
        //这里是小于等于
        queryWrapper.le(Objects.nonNull(endDayId), UserShiftConfigDO::getDayId, endDayId);
        queryWrapper.in(UserShiftConfigDO::getUserId, userIdList);
        queryWrapper.eq(Objects.nonNull(classId), UserShiftConfigDO::getPunchClassConfigId, classId);
        queryWrapper.eq(UserShiftConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserShiftConfigDO::getIsLatest, BusinessConstant.Y);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<UserShiftConfigDO> selectRecordByDateRange(Long userId, Long startDayId, Long endDayId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserShiftConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.ge(Objects.nonNull(startDayId), UserShiftConfigDO::getDayId, startDayId);
        queryWrapper.lt(Objects.nonNull(endDayId), UserShiftConfigDO::getDayId, endDayId);
        queryWrapper.eq(UserShiftConfigDO::getUserId, userId);
        queryWrapper.eq(UserShiftConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserShiftConfigDO::getIsLatest, BusinessConstant.Y);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<UserShiftConfigDO> selectRecordByDayList(Long userId, List<Long> dayIdList) {
        if (userId == null || CollectionUtils.isEmpty(dayIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserShiftConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserShiftConfigDO::getUserId, userId);
        queryWrapper.in(UserShiftConfigDO::getDayId, dayIdList);
        queryWrapper.eq(UserShiftConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserShiftConfigDO::getIsLatest, BusinessConstant.Y);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<UserShiftConfigDO> selectBatchUserRecord(List<Long> userIdList, List<Long> dayIdList) {
        if (CollectionUtils.isEmpty(userIdList) || CollectionUtils.isEmpty(dayIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<UserShiftConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(UserShiftConfigDO::getUserId, userIdList);
        queryWrapper.in(UserShiftConfigDO::getDayId, dayIdList);
        queryWrapper.eq(UserShiftConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserShiftConfigDO::getIsLatest, BusinessConstant.Y);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<UserShiftConfigDO> selectBatchByDate(Long startDayId, Long endDayId) {
        if (endDayId == null) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<UserShiftConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.ge(Objects.nonNull(startDayId), UserShiftConfigDO::getDayId, startDayId);
        queryWrapper.le(UserShiftConfigDO::getDayId, endDayId);
        queryWrapper.eq(UserShiftConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<UserShiftConfigDTO> page(UserShiftConfigQuery userShiftConfigQuery) {
        return this.baseMapper.page(userShiftConfigQuery);
    }

    @Override
    public Long countMigratedDataForRollback(List<Long> punchClassConfigIds, Long startDayId, Long endDayId, String taskFlagPrefix) {
        if (CollectionUtils.isEmpty(punchClassConfigIds) || StringUtils.isBlank(taskFlagPrefix)) {
            log.debug("统计回滚数据失败，考勤组ID列表和任务标识前缀不能为空");
            return 0L;
        }

        LambdaQueryWrapper<UserShiftConfigDO> queryWrapper = Wrappers.lambdaQuery();

        // 直接通过班次配置ID列表查询
        queryWrapper.in(UserShiftConfigDO::getPunchClassConfigId, punchClassConfigIds);

        // 日期范围条件
        queryWrapper.ge(Objects.nonNull(startDayId), UserShiftConfigDO::getDayId, startDayId);
        queryWrapper.le(Objects.nonNull(endDayId), UserShiftConfigDO::getDayId, endDayId);

        Integer count = this.baseMapper.selectCount(queryWrapper);
        log.info("统计回滚数据完成, punchConfigIds数量: {}, dayId范围: {} - {}, taskFlagPrefix: {}, 结果数量: {}",
                punchClassConfigIds.size(), startDayId, endDayId, taskFlagPrefix, count);
        return Long.valueOf(count);
    }

    @Override
    public List<Long> selectMigratedDataIdsForRollback(List<Long> punchClassConfigIds, Long startDayId, Long endDayId, String taskFlagPrefix, Integer offset, Integer limit) {
        if (CollectionUtils.isEmpty(punchClassConfigIds) || StringUtils.isBlank(taskFlagPrefix)) {
            log.info("查询回滚数据ID失败，考勤组ID列表和任务标识前缀不能为空");
            return Collections.emptyList();
        }

        LambdaQueryWrapper<UserShiftConfigDO> queryWrapper = Wrappers.lambdaQuery();

        // 只查询ID字段，提高查询效率
        queryWrapper.select(UserShiftConfigDO::getId);

        // 直接通过班次配置ID列表查询
        queryWrapper.in(UserShiftConfigDO::getPunchClassConfigId, punchClassConfigIds);

        // 日期范围条件
        queryWrapper.ge(Objects.nonNull(startDayId), UserShiftConfigDO::getDayId, startDayId);
        queryWrapper.le(Objects.nonNull(endDayId), UserShiftConfigDO::getDayId, endDayId);

        // 按ID排序，确保删除顺序的一致性
        queryWrapper.orderByAsc(UserShiftConfigDO::getId);

        // 分页查询
        if (Objects.nonNull(offset) && Objects.nonNull(limit)) {
            queryWrapper.last("LIMIT " + offset + ", " + limit);
        }

        List<UserShiftConfigDO> records = this.baseMapper.selectList(queryWrapper);
        List<Long> ids = records.stream()
                .map(UserShiftConfigDO::getId)
                .collect(Collectors.toList());

        log.info("查询回滚数据ID完成, punchConfigIds数量: {}, dayId范围: {} - {}, offset: {}, limit: {}, 结果数量: {}",
                punchClassConfigIds.size(), startDayId, endDayId, offset, limit, ids.size());
        return ids;
    }
}
