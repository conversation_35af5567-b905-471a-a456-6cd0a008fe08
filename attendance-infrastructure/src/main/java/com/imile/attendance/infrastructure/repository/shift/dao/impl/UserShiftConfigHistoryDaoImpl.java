package com.imile.attendance.infrastructure.repository.shift.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.shift.dao.UserShiftConfigHistoryDao;
import com.imile.attendance.infrastructure.repository.shift.mapper.UserShiftConfigHistoryMapper;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigHistoryDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 员工排班配置历史表 DAO 实现类
 *
 * <AUTHOR> chen
 * @Date 2025/6/18
 * @Description 员工排班配置历史表数据访问对象实现
 */
@Service
@RequiredArgsConstructor
public class UserShiftConfigHistoryDaoImpl extends ServiceImpl<UserShiftConfigHistoryMapper, UserShiftConfigHistoryDO> implements UserShiftConfigHistoryDao {

    @Override
    public List<UserShiftConfigHistoryDO> selectByUserId(Long userId) {
        if (Objects.isNull(userId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserShiftConfigHistoryDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserShiftConfigHistoryDO::getUserId, userId);
        queryWrapper.eq(UserShiftConfigHistoryDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(UserShiftConfigHistoryDO::getCreateDate);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<UserShiftConfigHistoryDO> selectByUserIdAndDateRange(Long userId, Long startDayId, Long endDayId) {
        if (Objects.isNull(userId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserShiftConfigHistoryDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserShiftConfigHistoryDO::getUserId, userId);
        queryWrapper.ge(Objects.nonNull(startDayId), UserShiftConfigHistoryDO::getDayId, startDayId);
        queryWrapper.le(Objects.nonNull(endDayId), UserShiftConfigHistoryDO::getDayId, endDayId);
        queryWrapper.eq(UserShiftConfigHistoryDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByAsc(UserShiftConfigHistoryDO::getDayId);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<UserShiftConfigHistoryDO> selectByUserIdListAndDateRange(List<Long> userIdList, Long startDayId, Long endDayId) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<UserShiftConfigHistoryDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(UserShiftConfigHistoryDO::getUserId, userIdList);
        queryWrapper.ge(Objects.nonNull(startDayId), UserShiftConfigHistoryDO::getDayId, startDayId);
        queryWrapper.le(Objects.nonNull(endDayId), UserShiftConfigHistoryDO::getDayId, endDayId);
        queryWrapper.eq(UserShiftConfigHistoryDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByAsc(UserShiftConfigHistoryDO::getUserId, UserShiftConfigHistoryDO::getDayId);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<UserShiftConfigHistoryDO> selectByDayIdRange(Long startDayId, Long endDayId) {
        if (Objects.isNull(endDayId)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<UserShiftConfigHistoryDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.ge(Objects.nonNull(startDayId), UserShiftConfigHistoryDO::getDayId, startDayId);
        queryWrapper.le(UserShiftConfigHistoryDO::getDayId, endDayId);
        queryWrapper.eq(UserShiftConfigHistoryDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByAsc(UserShiftConfigHistoryDO::getDayId);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<UserShiftConfigHistoryDO> selectByUserIdAndDayIds(Long userId, List<Long> dayIdList) {
        if (Objects.isNull(userId) || CollectionUtils.isEmpty(dayIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserShiftConfigHistoryDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserShiftConfigHistoryDO::getUserId, userId);
        queryWrapper.in(UserShiftConfigHistoryDO::getDayId, dayIdList);
        queryWrapper.eq(UserShiftConfigHistoryDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByAsc(UserShiftConfigHistoryDO::getDayId);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public int batchInsert(List<UserShiftConfigHistoryDO> historyList) {
        if (CollectionUtils.isEmpty(historyList)) {
            return 0;
        }
        // 使用 MyBatis-Plus 的批量插入方法，默认批次大小为 1000
        return this.saveBatch(historyList, 1000) ? historyList.size() : 0;
    }

    @Override
    public List<UserShiftConfigHistoryDO> selectByPunchClassConfigId(Long punchClassConfigId) {
        if (Objects.isNull(punchClassConfigId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserShiftConfigHistoryDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserShiftConfigHistoryDO::getPunchClassConfigId, punchClassConfigId);
        queryWrapper.eq(UserShiftConfigHistoryDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(UserShiftConfigHistoryDO::getCreateDate);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<UserShiftConfigHistoryDO> selectByShiftType(String shiftType) {
        if (Objects.isNull(shiftType)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserShiftConfigHistoryDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserShiftConfigHistoryDO::getShiftType, shiftType);
        queryWrapper.eq(UserShiftConfigHistoryDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(UserShiftConfigHistoryDO::getCreateDate);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<UserShiftConfigHistoryDO> selectByDataSource(String dataSource) {
        if (Objects.isNull(dataSource)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserShiftConfigHistoryDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserShiftConfigHistoryDO::getDataSource, dataSource);
        queryWrapper.eq(UserShiftConfigHistoryDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(UserShiftConfigHistoryDO::getCreateDate);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<UserShiftConfigHistoryDO> selectBatchUserRecord(List<Long> userIdList, List<Long> dayIdList) {
        if (CollectionUtils.isEmpty(userIdList) || CollectionUtils.isEmpty(dayIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<UserShiftConfigHistoryDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(UserShiftConfigHistoryDO::getUserId, userIdList);
        queryWrapper.in(UserShiftConfigHistoryDO::getDayId, dayIdList);
        queryWrapper.eq(UserShiftConfigHistoryDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserShiftConfigHistoryDO::getIsLatest, BusinessConstant.Y);
        return this.baseMapper.selectList(queryWrapper);
    }
}
