package com.imile.attendance.infrastructure.repository.shift.dao.migrate;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.shift.dto.DayShiftConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.dto.ShiftConfigUpdateToOldDTO;
import com.imile.attendance.infrastructure.repository.shift.dto.UserShiftConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.model.migrate.UserShiftConfigMigrateDO;
import com.imile.attendance.infrastructure.repository.shift.query.UserShiftConfigQuery;

import java.util.Date;
import java.util.List;

/**
 * 员工排班配置迁移表DAO接口
 * 提供对user_shift_config_migrate表的业务数据访问操作
 *
 * <AUTHOR> chen
 * @since 2025/6/18
 */
public interface UserShiftConfigMigrateDao extends IService<UserShiftConfigMigrateDO> {

    /**
     * 查找用户排班信息
     * 获取指定用户在指定时间范围内的有效排班记录
     *
     * @param userId 用户ID
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 排班记录列表
     */
    List<UserShiftConfigMigrateDO> selectUserShift(Long userId, Long startDayId, Long endDayId);

    /**
     * 获取用户在指定天数的排班数据
     * 根据具体的日期ID列表查询排班信息
     *
     * @param userId 用户ID
     * @param dayIds 日期ID列表
     * @return 排班记录列表
     */
    List<UserShiftConfigMigrateDO> selectUserShiftByDayIds(Long userId, List<Long> dayIds);

    /**
     * 逻辑删除排班信息
     * 将指定的排班记录标记为已删除状态
     *
     * @param dayShiftConfigDTOList 待删除的排班配置列表
     */
    void updateToDelete(List<DayShiftConfigDTO> dayShiftConfigDTOList);

    /**
     * 物理删除排班信息
     * 从数据库中彻底删除指定的排班记录
     *
     * @param ids 待删除的记录ID列表
     * @return 删除的记录数量
     */
    int delete(List<Long> ids);

    /**
     * 获取用户规定时间段内的排班信息
     * 支持多用户批量查询，用于报表和统计功能
     *
     * @param userIdList 用户ID列表
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 排班记录列表
     */
    List<UserShiftConfigMigrateDO> selectRecordByUserIdList(List<Long> userIdList, Long startDayId, Long endDayId);

    /**
     * 根据用户列表和班次ID查询排班记录
     * 用于查询特定班次的员工排班情况
     *
     * @param userIdList 用户ID列表
     * @param classId 班次ID
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 排班记录列表
     */
    List<UserShiftConfigMigrateDO> selectRecordByUserIdListAndClassId(List<Long> userIdList, Long classId, Long startDayId, Long endDayId);

    /**
     * 获取用户规定时间段内的排班规则(特殊使用，不通用)
     * 专用于特定业务场景的排班规则查询
     *
     * @param userId 用户ID
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 排班记录列表
     */
    List<UserShiftConfigMigrateDO> selectRecordByDateRange(Long userId, Long startDayId, Long endDayId);

    /**
     * 获取用户在指定天数的排班数据
     * 根据具体的日期列表查询排班信息
     *
     * @param userId 用户ID
     * @param dayIdList 日期ID列表
     * @return 排班记录列表
     */
    List<UserShiftConfigMigrateDO> selectRecordByDayList(Long userId, List<Long> dayIdList);

    /**
     * 批量获取用户的排班记录
     * 支持多用户、多日期的批量查询
     *
     * @param userIdList 用户ID列表
     * @param dayIdList 日期ID列表
     * @return 排班记录列表
     */
    List<UserShiftConfigMigrateDO> selectBatchUserRecord(List<Long> userIdList, List<Long> dayIdList);

    /**
     * 根据起始时间查询排班记录
     * 不过滤非最新版本记录，用于数据迁移和分析
     *
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 排班记录列表
     */
    List<UserShiftConfigMigrateDO> selectBatchByDate(Long startDayId, Long endDayId);

    /**
     * 分页查询排班配置
     * 支持复杂条件的分页查询，用于管理界面
     *
     * @param userShiftConfigQuery 查询条件
     * @return 排班配置DTO列表
     */
    List<UserShiftConfigDTO> page(UserShiftConfigQuery userShiftConfigQuery);
}
