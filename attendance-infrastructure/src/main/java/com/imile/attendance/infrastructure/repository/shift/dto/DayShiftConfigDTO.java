package com.imile.attendance.infrastructure.repository.shift.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/4/17 
 * @Description
 */
@Data
public class DayShiftConfigDTO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户的id
     */
    private Long userId;

    /**
     * 日期，yyyy-MM-dd格式
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date classTime;

    /**
     * dayId
     */
    private Long dayId;

    /**
     * 班次信息id
     */
    private Long classId;

    /**
     * 班次名称
     */
    private String className;
}
