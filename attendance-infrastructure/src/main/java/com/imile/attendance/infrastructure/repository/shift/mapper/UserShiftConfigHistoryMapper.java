package com.imile.attendance.infrastructure.repository.shift.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigHistoryDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * 员工排班配置历史表 Mapper 接口
 *
 * <AUTHOR> chen
 * @Date 2025/6/18
 * @Description 员工排班配置历史表数据访问层
 */
@Mapper
@Repository
public interface UserShiftConfigHistoryMapper extends BaseMapper<UserShiftConfigHistoryDO> {

}
