package com.imile.attendance.infrastructure.repository.shift.mapper;

import com.imile.attendance.infrastructure.common.AttendanceBaseMapper;
import com.imile.attendance.infrastructure.repository.shift.dto.UserShiftConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.infrastructure.repository.shift.query.UserShiftConfigQuery;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/17 
 * @Description
 */
@Mapper
@Repository
public interface UserShiftConfigMapper extends AttendanceBaseMapper<UserShiftConfigDO> {

    /**
     * 分页查询
     */
    List<UserShiftConfigDTO> page(UserShiftConfigQuery userShiftConfigQuery);
}
