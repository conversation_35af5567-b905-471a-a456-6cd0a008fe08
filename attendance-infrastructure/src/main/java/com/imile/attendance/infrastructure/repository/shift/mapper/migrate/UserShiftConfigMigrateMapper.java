package com.imile.attendance.infrastructure.repository.shift.mapper.migrate;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.common.AttendanceBaseMapper;
import com.imile.attendance.infrastructure.repository.shift.dto.UserShiftConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.model.migrate.UserShiftConfigMigrateDO;
import com.imile.attendance.infrastructure.repository.shift.query.UserShiftConfigQuery;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 员工排班配置迁移表Mapper
 * 提供对user_shift_config_migrate表的数据访问操作
 *
 * <AUTHOR> chen
 * @since 2025/6/18
 */
@Mapper
@Repository
public interface UserShiftConfigMigrateMapper extends AttendanceBaseMapper<UserShiftConfigMigrateDO> {

    /**
     * 分页查询排班配置
     * 支持多条件组合查询，用于管理界面的数据展示
     *
     * @param userShiftConfigQuery 查询条件
     * @return 排班配置列表
     */
    List<UserShiftConfigDTO> page(UserShiftConfigQuery userShiftConfigQuery);

    /**
     * 根据用户ID和日期范围查询排班记录
     * 用于获取指定用户在特定时间段内的排班安排
     *
     * @param userId 用户ID
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 排班记录列表
     */
    List<UserShiftConfigMigrateDO> selectByUserIdAndDateRange(Long userId, Long startDayId, Long endDayId);

    /**
     * 根据用户ID列表和日期范围查询排班记录
     * 用于批量获取多个用户的排班信息
     *
     * @param userIdList 用户ID列表
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 排班记录列表
     */
    List<UserShiftConfigMigrateDO> selectByUserIdListAndDateRange(List<Long> userIdList, Long startDayId, Long endDayId);

    /**
     * 根据用户ID列表和班次ID查询排班记录
     * 用于查询特定班次的员工排班情况
     *
     * @param userIdList 用户ID列表
     * @param classId 班次ID
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 排班记录列表
     */
    List<UserShiftConfigMigrateDO> selectByUserIdListAndClassId(List<Long> userIdList, Long classId, Long startDayId, Long endDayId);

    /**
     * 根据日期范围查询所有排班记录
     * 不过滤非最新版本记录，用于数据迁移和统计分析
     *
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 排班记录列表
     */
    List<UserShiftConfigMigrateDO> selectBatchByDate(Long startDayId, Long endDayId);
}
