package com.imile.attendance.infrastructure.repository.shift.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/4/19 
 * @Description
 */
@ApiModel(description = "排班任务记录表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("shift_task_record")
public class ShiftTaskRecordDO extends BaseDO {

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "任务标识")
    private String taskFlag;

    @ApiModelProperty(value = "排班类型,自动排班、循环排班、自定义排班")
    private String shiftType;

    @ApiModelProperty(value = "来源：页面操作/批量排班/系统排班/循环排班/排班导入")
    private String shiftSource;

    @ApiModelProperty(value = "排班开始时间")
    private Date startDate;

    @ApiModelProperty(value = "排班结束时间")
    private Date endDate;

    @ApiModelProperty(value = "任务状态：1-执行中, 2-执行完成, 3-执行失败  ShiftTaskEnum")
    private Integer status;

    @ApiModelProperty(value = "任务执行耗时(秒)")
    private Integer costSecond;

    @ApiModelProperty(value = "错误信息")
    private String errorMessage;

    @ApiModelProperty(value = "任务优先级，数字越大优先级越高")
    private Integer priority;

    @ApiModelProperty(value = "已执行人数")
    private Integer executedCount;

    @ApiModelProperty(value = "总人数")
    private Integer totalCount;
}
