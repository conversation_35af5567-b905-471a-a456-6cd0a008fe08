package com.imile.attendance.infrastructure.repository.third.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.third.model.ZktecoAreaSnRelationDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/5 
 * @Description
 */
public interface ZktecoAreaSnRelationDao extends IService<ZktecoAreaSnRelationDO> {


    List<ZktecoAreaSnRelationDO> listByUserId(Long userId);

    List<ZktecoAreaSnRelationDO> listByPage(int currentPage, int pageSize);


}
