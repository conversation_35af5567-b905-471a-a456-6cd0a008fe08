package com.imile.attendance.infrastructure.repository.third.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.third.dao.ZktecoAreaSnRelationDao;
import com.imile.attendance.infrastructure.repository.third.mapper.ZktecoAreaSnRelationMapper;
import com.imile.attendance.infrastructure.repository.third.model.ZktecoAreaSnRelationDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/5 
 * @Description
 */
@Component
@RequiredArgsConstructor
public class ZktecoAreaSnRelationDaoImpl extends ServiceImpl<ZktecoAreaSnRelationMapper, ZktecoAreaSnRelationDO> implements ZktecoAreaSnRelationDao {

    @Override
    public List<ZktecoAreaSnRelationDO> listByUserId(Long userId) {
        LambdaQueryWrapper<ZktecoAreaSnRelationDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ZktecoAreaSnRelationDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(ZktecoAreaSnRelationDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.like(ZktecoAreaSnRelationDO::getUserIds, userId);
        List<ZktecoAreaSnRelationDO> snRelationDOList = this.list(queryWrapper);
        return CollectionUtils.isEmpty(snRelationDOList) ? Collections.emptyList() : snRelationDOList;
    }

    @Override
    public List<ZktecoAreaSnRelationDO> listByPage(int currentPage, int pageSize) {
        PageInfo<ZktecoAreaSnRelationDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }
}
