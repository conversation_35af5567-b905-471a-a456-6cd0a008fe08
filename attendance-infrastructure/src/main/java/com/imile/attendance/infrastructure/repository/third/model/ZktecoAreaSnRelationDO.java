package com.imile.attendance.infrastructure.repository.third.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/5 
 * @Description
 */
@ApiModel(description = "zkteco区域和考勤机编号关联表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zkteco_area_sn_relation")
public class ZktecoAreaSnRelationDO extends BaseDO {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    private String country;

    /**
     * 公司ID
     */
    @ApiModelProperty(value = "公司ID")
    private Long companyId;

    /**
     * 中控区域ID
     */
    @ApiModelProperty(value = "中控区域ID")
    private Integer zktecoAreaId;

    /**
     * 中控区域名称
     */
    @ApiModelProperty(value = "中控区域名称")
    private String zktecoAreaName;

    /**
     * 中控区域编码
     */
    @ApiModelProperty(value = "中控区域编码")
    private String zktecoAreaCode;

    /**
     * 考勤机编号，多个间用逗号分割
     */
    @ApiModelProperty(value = "考勤机编号，多个间用逗号分割")
    private String terminalSn;

    /**
     * 部门，存储多个
     */
    @ApiModelProperty(value = "部门，存储多个")
    private String deptIds;

    /**
     * 员工ID，存储多个
     */
    @ApiModelProperty(value = "员工ID，存储多个")
    private String userIds;

    /**
     * 是否为最新
     */
    @ApiModelProperty(value = "是否为最新")
    private Integer isLatest;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


    /**
     * 获取部门ID列表
     * @return 部门ID的Long类型列表
     */
    public List<Long> listDeptIdList() {
        if (StringUtils.isBlank(deptIds)) {
            return Collections.emptyList();
        }
        return Arrays.stream(deptIds.split(","))
                .filter(StringUtils::isNotBlank)
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

    public List<String> listTerminalSnList(){
        if (StringUtils.isBlank(terminalSn)){
            return Collections.emptyList();
        }
        return Arrays.stream(terminalSn.split(","))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }


    public List<Long> listUserIdList(){
        if (StringUtils.isBlank(userIds)){
            return Collections.emptyList();
        }
        return Arrays.stream(userIds.split(","))
                .filter(StringUtils::isNotBlank)
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }



}
