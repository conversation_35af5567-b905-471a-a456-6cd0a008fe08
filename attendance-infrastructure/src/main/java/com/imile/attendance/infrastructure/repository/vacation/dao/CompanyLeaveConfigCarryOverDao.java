package com.imile.attendance.infrastructure.repository.vacation.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverDO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/21
 * @Description
 */
public interface CompanyLeaveConfigCarryOverDao extends IService<CompanyLeaveConfigCarryOverDO> {
    /**
     * 根据假期方案id查询假期结转配置
     *
     * @param allCompanyLeaveConfigIdList 假期方案id
     * @return 假期结转配置
     */
    List<CompanyLeaveConfigCarryOverDO> selectByLeaveId(List<Long> allCompanyLeaveConfigIdList);

    /**
     * 根据假期方案id查询假期结转配置
     *
     * @param leaveId 假期方案id
     * @return 假期结转配置
     */
    List<CompanyLeaveConfigCarryOverDO> getCarryOverByConfigId(Long leaveId);
}
