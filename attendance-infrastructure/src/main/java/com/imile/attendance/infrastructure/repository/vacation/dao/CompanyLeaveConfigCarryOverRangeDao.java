package com.imile.attendance.infrastructure.repository.vacation.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverRangeDO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/21
 * @Description
 */
public interface CompanyLeaveConfigCarryOverRangeDao extends IService<CompanyLeaveConfigCarryOverRangeDO> {
    /**
     * 获取假期结转失效规则范围
     *
     * @param carryOverIdList 假期结转失效规则id
     * @return 假期结转失效规则范围
     */
    List<CompanyLeaveConfigCarryOverRangeDO> selectByCarryOverId(List<Long> carryOverIdList);

    /**
     * 获取假期结转失效规则范围
     *
     * @param carryOverId 假期结转失效规则id
     * @return 假期结转失效规则范围
     */
    List<CompanyLeaveConfigCarryOverRangeDO> getLeaveConfigCarryOverRangeList(Long carryOverId);

}
