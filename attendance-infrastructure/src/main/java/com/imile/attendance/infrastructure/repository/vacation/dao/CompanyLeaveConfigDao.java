package com.imile.attendance.infrastructure.repository.vacation.dao;


import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.query.CompanyLeaveQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/21
 * @Description
 */
public interface CompanyLeaveConfigDao extends IService<CompanyLeaveConfigDO> {

    /**
     * 根据公司查询假期类型
     *
     * @param countryList
     * @return
     */
    List<CompanyLeaveConfigDO> selectLeaveConfigByCountry(List<String> countryList);


    /**
     * 根据条件查询公司假期配置数据
     *
     * @param companyLeaveQuery
     * @return
     */
    List<CompanyLeaveConfigDO> selectLeaveConfig(CompanyLeaveQuery companyLeaveQuery);
}
