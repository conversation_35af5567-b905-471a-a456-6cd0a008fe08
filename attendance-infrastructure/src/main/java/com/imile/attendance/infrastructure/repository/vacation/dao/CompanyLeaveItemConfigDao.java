package com.imile.attendance.infrastructure.repository.vacation.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveItemConfigDO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
public interface CompanyLeaveItemConfigDao extends IService<CompanyLeaveItemConfigDO> {

    /**
     * 查询公司假期阶段信息
     *
     * @param leaveConfigIds
     * @return
     */
    List<CompanyLeaveItemConfigDO> selectItemByConfigId(List<Long> leaveConfigIds);

}
