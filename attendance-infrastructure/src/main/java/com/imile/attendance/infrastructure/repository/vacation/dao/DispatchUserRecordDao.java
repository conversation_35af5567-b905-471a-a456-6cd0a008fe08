package com.imile.attendance.infrastructure.repository.vacation.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.vacation.model.DispatchUserRecordDO;
import com.imile.attendance.infrastructure.repository.vacation.query.DispatchUserRecordQuery;

import java.util.List;


/**
 * <p>
 * 派遣人员记录 数据库操作
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
public interface DispatchUserRecordDao extends IService<DispatchUserRecordDO> {

    List<DispatchUserRecordDO> selectDispatchInfoByUserCode(List<String> userCodeList);

    List<DispatchUserRecordDO> selectDispatchInfo(DispatchUserRecordQuery query);
}
