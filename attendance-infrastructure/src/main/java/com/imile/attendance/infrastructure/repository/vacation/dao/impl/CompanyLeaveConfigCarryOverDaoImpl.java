package com.imile.attendance.infrastructure.repository.vacation.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveConfigCarryOverDao;
import com.imile.attendance.infrastructure.repository.vacation.mapper.CompanyLeaveConfigCarryOverMapper;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/23
 * @Description 假期结转规则 数据库操作实现类
 */
@Component
@RequiredArgsConstructor
public class CompanyLeaveConfigCarryOverDaoImpl extends ServiceImpl<CompanyLeaveConfigCarryOverMapper, CompanyLeaveConfigCarryOverDO> implements CompanyLeaveConfigCarryOverDao {

    /**
     * 根据假期方案id查询假期结转配置
     *
     * @param allCompanyLeaveConfigIdList 假期方案id
     * @return 假期结转配置
     */
    @Override
    public List<CompanyLeaveConfigCarryOverDO> selectByLeaveId(List<Long> allCompanyLeaveConfigIdList) {
        if (CollUtil.isEmpty(allCompanyLeaveConfigIdList)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CompanyLeaveConfigCarryOverDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CompanyLeaveConfigCarryOverDO::getLeaveId, allCompanyLeaveConfigIdList);
        queryWrapper.eq(CompanyLeaveConfigCarryOverDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<CompanyLeaveConfigCarryOverDO> getCarryOverByConfigId(Long leaveId) {
        if (ObjectUtil.isNull(leaveId)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CompanyLeaveConfigCarryOverDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CompanyLeaveConfigCarryOverDO::getLeaveId, leaveId);
        queryWrapper.eq(CompanyLeaveConfigCarryOverDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }
}
