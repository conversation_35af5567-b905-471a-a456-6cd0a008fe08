package com.imile.attendance.infrastructure.repository.vacation.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveConfigIssueRuleRangeDao;
import com.imile.attendance.infrastructure.repository.vacation.mapper.CompanyLeaveConfigIssueRuleRangeMapper;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleRangeDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/23
 * @Description 假期发放规则范围 数据库操作实现类
 */
@Component
@RequiredArgsConstructor
public class CompanyLeaveConfigIssueRuleRangeDaoImpl extends ServiceImpl<CompanyLeaveConfigIssueRuleRangeMapper, CompanyLeaveConfigIssueRuleRangeDO> implements CompanyLeaveConfigIssueRuleRangeDao {

    @Override
    public List<CompanyLeaveConfigIssueRuleRangeDO> selectByIssueRuleId(List<Long> issueRuleIdList) {
        if (CollUtil.isEmpty(issueRuleIdList)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CompanyLeaveConfigIssueRuleRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CompanyLeaveConfigIssueRuleRangeDO::getIssueRuleId, issueRuleIdList);
        queryWrapper.eq(CompanyLeaveConfigIssueRuleRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<CompanyLeaveConfigIssueRuleRangeDO> getLeaveConfigIssueRuleRangeList(Long issueRuleId) {
        if (ObjectUtil.isNull(issueRuleId)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CompanyLeaveConfigIssueRuleRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CompanyLeaveConfigIssueRuleRangeDO::getIssueRuleId, issueRuleId);
        queryWrapper.eq(CompanyLeaveConfigIssueRuleRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }
}
