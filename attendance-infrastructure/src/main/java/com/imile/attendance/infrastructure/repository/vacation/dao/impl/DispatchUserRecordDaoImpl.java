package com.imile.attendance.infrastructure.repository.vacation.dao.impl;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.vacation.dao.DispatchUserRecordDao;
import com.imile.attendance.infrastructure.repository.vacation.mapper.DispatchUserRecordMapper;
import com.imile.attendance.infrastructure.repository.vacation.model.DispatchUserRecordDO;
import com.imile.attendance.infrastructure.repository.vacation.query.DispatchUserRecordQuery;
import com.imile.common.enums.IsDeleteEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 派遣人员记录 数据库操作
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
@Service
public class DispatchUserRecordDaoImpl extends ServiceImpl<DispatchUserRecordMapper, DispatchUserRecordDO> implements DispatchUserRecordDao {


    @Override
    public List<DispatchUserRecordDO> selectDispatchInfoByUserCode(List<String> userCodeList) {
        if (CollUtil.isEmpty(userCodeList)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<DispatchUserRecordDO> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.in(CollUtil.isNotEmpty(userCodeList), DispatchUserRecordDO::getUserCode, userCodeList);
        lambdaQuery.eq(DispatchUserRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(lambdaQuery);
    }

    @Override
    public List<DispatchUserRecordDO> selectDispatchInfo(DispatchUserRecordQuery query) {
        LambdaQueryWrapper<DispatchUserRecordDO> lambdaQuery = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(query.getUserCode())) {
            lambdaQuery.eq(DispatchUserRecordDO::getUserCode, query.getUserCode());
        }
        if (CollectionUtils.isNotEmpty(query.getUserCodeList())) {
            lambdaQuery.in(DispatchUserRecordDO::getUserCode, query.getUserCodeList());
        }
        if (Objects.nonNull(query.getTransformType())) {
            lambdaQuery.eq(DispatchUserRecordDO::getTransformType, query.getTransformType());
        }
        lambdaQuery.eq(DispatchUserRecordDO::getEndFlag, Objects.isNull(query.getEndFlag())
                ? BusinessConstant.N : query.getEndFlag());
        lambdaQuery.eq(DispatchUserRecordDO::getIsDelete, IsDeleteEnum.NO);
        return this.list(lambdaQuery);
    }
}
