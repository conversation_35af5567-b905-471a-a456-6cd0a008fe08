package com.imile.attendance.infrastructure.repository.vacation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.vacation.model.UserLeaveConfigHistoryDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 人员常驻国切换历史假期范围表 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Mapper
public interface UserLeaveConfigHistoryMapper extends BaseMapper<UserLeaveConfigHistoryDO> {


}
