package com.imile.attendance.infrastructure.repository.vacation.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 国家福利假结转规则表
 *
 * <AUTHOR>
 * @Date 2025/4/23
 * @Description
 */
@ApiModel(description = "国家福利假结转规则表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("company_leave_config_carry_over")
public class CompanyLeaveConfigCarryOverDO extends BaseDO {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 国家假期配置id
     */
    @ApiModelProperty(value = "国家假期配置id")
    private Long leaveId;

    /**
     * 是否可结转：1：可结转 2：不可结转
     */
    @ApiModelProperty(value = "是否可结转：1：可结转 2：不可结转")
    private Integer isCarryOver;

    /**
     * 最大结转天数
     */
    @ApiModelProperty(value = "最大结转天数")
    private Integer maxCarryOverDay;

    /**
     * 是否永久有效：1：是 2：否
     */
    @ApiModelProperty(value = "是否永久有效：1：是 2：否 ")
    private Integer isInvalid;

    /**
     * 结转失效类型：0：固定日期 1：按年失效 2：按入职日期设置
     */
    @ApiModelProperty(value = "结转失效类型：0：固定日期 1：按年失效 2：按入职日期设置")
    private Integer invalidType;

    /**
     * 结转失效年：0：次年 1：第三年 2：第四年
     */
    @ApiModelProperty(value = "结转失效年：0：次年 1：第三年 2：第四年")
    private Integer invalidYear;

    /**
     * 失效日期：invalid_date 示例：0124
     */
    @ApiModelProperty(value = "失效日期：invalid_date 示例：0124")
    private Long invalidDate;
}