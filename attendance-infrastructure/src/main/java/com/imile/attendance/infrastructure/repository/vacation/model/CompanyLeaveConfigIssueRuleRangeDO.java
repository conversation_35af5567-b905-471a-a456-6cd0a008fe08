package com.imile.attendance.infrastructure.repository.vacation.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 国家福利假发放规则范围表
 *
 * <AUTHOR>
 * @Date 2025/4/23
 * @Description
 */
@ApiModel(description = "国家福利假发放规则范围表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("company_leave_config_issue_rule_range")
public class CompanyLeaveConfigIssueRuleRangeDO extends BaseDO {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 国家发放规则表主键id
     */
    @ApiModelProperty(value = "国家发放规则表主键id")
    private Long issueRuleId;

    /**
     * 左边符号：1：表示大于 2：表示大于等于
     */
    @ApiModelProperty(value = "左边符号：1：表示大于 2：表示大于等于")
    private Integer symbolLeft;

    /**
     * 左边年份
     */
    @ApiModelProperty(value = "左边年份")
    private Integer yearLeft;

    /**
     * 右边符号：1：表示小于 2：表示小于等于
     */
    @ApiModelProperty(value = "右边符号：1：表示小于 2：表示小于等于 ")
    private Integer symbolRight;

    /**
     * 左边年份
     */
    @ApiModelProperty(value = "左边年份")
    private Integer yearRight;

    /**
     * 常驻省
     */
    @ApiModelProperty(value = "常驻省")
    private String locationProvince;

    /**
     * 常驻市
     */
    @ApiModelProperty(value = "常驻市")
    private String locationCity;

    /**
     * 不同条件的发放额度
     */
    @ApiModelProperty(value = "不同条件的发放额度:没用，主要记录额度，对于issue_type=1使用假期详情表发放")
    private BigDecimal issueQuota;
}