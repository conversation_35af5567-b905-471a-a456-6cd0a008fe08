package com.imile.attendance.infrastructure.repository.vacation.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 公司假期阶段比例配置表
 *
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@ApiModel(description = "公司假期阶段比例配置表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("company_leave_item_config")
public class CompanyLeaveItemConfigDO extends BaseDO {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 公司假期配置id
     */
    @ApiModelProperty(value = "公司假期配置id")
    private Long leaveId;

    /**
     * 阶段
     */
    @ApiModelProperty(value = "阶段")
    private Integer stage;

    /**
     * 假期范围开始天数
     */
    @ApiModelProperty(value = "假期范围开始天数")
    private BigDecimal startDay;

    /**
     * 假期范围结束天数
     */
    @ApiModelProperty(value = "假期范围结束天数")
    private BigDecimal endDay;

    /**
     * 百分比日薪
     */
    @ApiModelProperty(value = "百分比日薪")
    private BigDecimal percentSalary;
}
