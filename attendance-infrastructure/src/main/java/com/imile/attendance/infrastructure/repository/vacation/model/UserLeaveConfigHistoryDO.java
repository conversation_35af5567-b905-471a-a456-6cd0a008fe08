package com.imile.attendance.infrastructure.repository.vacation.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 人员常驻国切换历史假期范围表（人员切换常驻国之前的假期范围记录）
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_leave_config_history")
public class UserLeaveConfigHistoryDO extends BaseDO {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户主键
     */
    private Long userId;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 假期主表主键
     */
    private Long leaveId;

    /**
     * 假期类型
     */
    private String leaveName;

    /**
     * 假期类型
     */
    private String leaveType;

    /**
     * 假期对应国家
     */
    private String leaveCountry;
}
