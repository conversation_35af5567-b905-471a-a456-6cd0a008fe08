package com.imile.attendance.infrastructure.repository.vacation.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/23
 * @Description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CompanyLeaveQuery {
    /**
     * 主键
     */
    List<Long> idList;
    /**
     * 所属国
     */
    private String country;
    /**
     * 假期名称
     */
    private String leaveName;
    /**
     * 假期名称集合
     */
    private List<String> leaveNameList;
    /**
     * 假期类型
     */
    private String leaveType;
    /**
     * 假期类型集合
     */
    private List<String> leaveTypeList;
    /**
     * 是否派遣假
     */
    private Integer isDispatch;
    /**
     * 状态
     */
    private String status;

    List<String> countryList;
}
