package com.imile.attendance.infrastructure.repository.vacation.query;

import com.imile.common.query.BaseQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date 2025/4/23
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CompanyLeaveTypeQuery extends BaseQuery {

    /**
     * 国家
     */
    private String country;

    /**
     * 假期类型
     */
    private String leaveType;

}
