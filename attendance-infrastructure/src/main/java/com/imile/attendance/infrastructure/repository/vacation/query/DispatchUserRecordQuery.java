package com.imile.attendance.infrastructure.repository.vacation.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/23
 * @Description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DispatchUserRecordQuery implements Serializable {

    private static final long serialVersionUID = 8280784484460295390L;

    /**
     * 派遣开始/结束日期
     */
    private Date dispatchDate;

    /**
     * 派遣地国家
     */
    private String dispatchCountry;

    /**
     * 派遣人员编码
     */
    private String userCode;

    /**
     * 派遣人员编码
     */
    private List<String> userCodeList;

    /**
     * 国籍编码
     */
    private String countryCode;

    /**
     * 派遣是否结束标志
     */
    private Integer endFlag;

    /**
     * 调动类型
     */
    private Integer transformType;
}
