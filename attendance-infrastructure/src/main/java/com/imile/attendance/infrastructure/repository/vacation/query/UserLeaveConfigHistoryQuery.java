package com.imile.attendance.infrastructure.repository.vacation.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserLeaveConfigHistoryQuery {

    /**
     * 用户主键
     */
    private Long userId;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 假期主表主键
     */
    private Long leaveId;

    /**
     * 假期名称
     */
    private String leaveName;

    /**
     * 假期类型
     */
    private String leaveType;

    /**
     * 假期对应国家
     */
    private String leaveCountry;
}
