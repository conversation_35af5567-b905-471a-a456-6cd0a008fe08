package com.imile.attendance.infrastructure.repository.zkteco.dao.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.zkteco.dao.AttendanceCompletionRateDao;
import com.imile.attendance.infrastructure.repository.zkteco.model.AttendanceCompletionRateDO;
import com.imile.attendance.infrastructure.repository.zkteco.mapper.AttendanceCompletionRateMapper;
import com.imile.common.enums.IsDeleteEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 打卡信息统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
@Service
public class AttendanceCompletionRateDaoImpl extends ServiceImpl<AttendanceCompletionRateMapper, AttendanceCompletionRateDO> implements AttendanceCompletionRateDao {

    @Override
    public List<AttendanceCompletionRateDO> selectListByDayIds(List<Long> dayIds) {

        LambdaQueryWrapper<AttendanceCompletionRateDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AttendanceCompletionRateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(AttendanceCompletionRateDO::getDayId, dayIds);
        return this.list(queryWrapper);
    }
}