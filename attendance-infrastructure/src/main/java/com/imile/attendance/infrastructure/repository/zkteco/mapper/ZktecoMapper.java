package com.imile.attendance.infrastructure.repository.zkteco.mapper;

import com.imile.attendance.infrastructure.repository.zkteco.DO.ZktecoAreasDO;
import com.imile.attendance.infrastructure.repository.zkteco.DO.ZktecoEmployeeAreaDO;
import com.imile.attendance.infrastructure.repository.zkteco.DO.ZktecoEmployeeDO;
import com.imile.attendance.infrastructure.repository.zkteco.DO.ZktecoEmployeeUpdateDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface ZktecoMapper {
    List<ZktecoAreasDO> selectSnByArea();

    List<ZktecoEmployeeDO> selectEmployee(@Param("empCodes") List<String> empCodes);

    List<ZktecoEmployeeAreaDO> selectAreaByEmployee(@Param("employeeId") Integer employeeId);

    boolean updateEmployee(ZktecoEmployeeUpdateDO updateDO);

    //boolean addEmployeeArea(@Param("employeeId") Integer employeeId, @Param("areaId") Integer areaId);

    boolean updateEmployeeArea(@Param("areaId") Integer areaId, @Param("id") Integer id);

    boolean deleteEmployeeArea(@Param("id") Integer id);

    List<ZktecoAreasDO> selectAreasByName(@Param("areaName") String areaName);

    List<ZktecoAreasDO> selectAreasByAreaName(@Param("areaName") String areaName);

    List<ZktecoEmployeeAreaDO> selectEmployeeAreas(@Param("areaIdList") List<Integer> areaIdList);

    List<ZktecoEmployeeDO> selectEmployeeByIds(@Param("idList") List<Integer> idList);

    List<Integer> selectEmployeeIdByIds(@Param("employeeIdList") List<Integer> employeeIdList);
}
