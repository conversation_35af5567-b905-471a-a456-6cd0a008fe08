<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.calendar.mapper.BaseDayInfoMapper">
    <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.calendar.model.BaseDayInfoDO">
        <!--@mbg.generated-->
        <!--@Table base_day_info-->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="year" jdbcType="INTEGER" property="year" />
        <result column="quarter" jdbcType="INTEGER" property="quarter" />
        <result column="month" jdbcType="INTEGER" property="month" />
        <result column="week" jdbcType="INTEGER" property="week" />
        <result column="day" jdbcType="INTEGER" property="day" />
        <result column="day_of_week" jdbcType="VARCHAR" property="dayOfWeek" />
        <result column="date" jdbcType="TIMESTAMP" property="date" />
        <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
        <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
        <result column="orderby" jdbcType="DECIMAL" property="orderby" />
    </resultMap>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, `year`, `quarter`, `month`, `week`, `day`, day_of_week, `date`, is_delete,
        record_version, create_date, create_user_code, create_user_name, last_upd_date,
        last_upd_user_code, last_upd_user_name, orderby
    </sql>

    <select id="listCycle"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from base_day_info bdi
        <where>
            bdi.is_delete = 0
            and (bdi.year  = #{year} or (bdi.year = #{year}-1 and bdi.month = 12))
        </where>
        order by bdi.id asc
    </select>


</mapper>
