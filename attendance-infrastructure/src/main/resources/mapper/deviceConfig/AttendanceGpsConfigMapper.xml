<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.deviceConfig.mapper.AttendanceGpsConfigMapper">

    <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceGpsConfigDO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="country" jdbcType="VARCHAR" property="country" />
        <result column="location_city" jdbcType="VARCHAR" property="locationCity" />
        <result column="address_name" jdbcType="VARCHAR" property="addressName" />
        <result column="address_detail" jdbcType="VARCHAR" property="addressDetail" />
        <result column="longitude" jdbcType="DECIMAL" property="longitude" />
        <result column="latitude" jdbcType="DECIMAL" property="latitude" />
        <result column="effective_range" jdbcType="INTEGER" property="effectiveRange" />
        <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
        <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
    </resultMap>

    <sql id="Base_Column_List">
        country,
        location_city,
        address_name,
        address_detail,
        longitude,
        latitude,
        effective_range,
        id,
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name
    </sql>

    <select id="queryGpsCountry" resultType="java.lang.String">
        select distinct country
        from attendance_gps_config
        where is_delete = 0
    </select>

    <select id="queryGpsCity" resultType="java.lang.String">
        select distinct location_city
        from attendance_gps_config
        where is_delete = 0
        and country = #{country}
    </select>
</mapper>
