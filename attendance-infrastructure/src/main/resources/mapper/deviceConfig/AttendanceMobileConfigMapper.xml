<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.deviceConfig.mapper.AttendanceMobileConfigMapper">

    <resultMap id="BaseResultMap"
               type="com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceMobileConfigDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="record_version" jdbcType="BIGINT" property="recordVersion"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate"/>
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode"/>
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName"/>
        <result column="user_code" jdbcType="VARCHAR" property="userCode"/>
        <result column="mobile_unicode" jdbcType="VARCHAR" property="mobileUnicode"/>
        <result column="mobile_model" jdbcType="VARCHAR" property="mobileModel"/>
        <result column="mobile_branch" jdbcType="VARCHAR" property="mobileBranch"/>
        <result column="mobile_version" jdbcType="VARCHAR" property="mobileVersion"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        user_code,
        mobile_unicode,
        mobile_model,
        mobile_branch,
        mobile_version
    </sql>

    <select id="queryAttendanceMobileConfig"
            parameterType="com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceMobileConfigListQuery"
            resultType="com.imile.attendance.infrastructure.repository.deviceConfig.dto.AttendanceMobileConfigListDTO">
        select hui.id as user_id,
        hui.user_code,
        hui.user_name,
        hui.location_country,
        hui.user_name_en,
        hui.dept_id,
        hamc.id      as mobile_config_id,
        hamc.mobile_unicode,
        hamc.mobile_model,
        hamc.mobile_branch,
        hamc.mobile_version,
        hamc.create_date,
        hamc.create_user_code,
        hamc.create_user_name,
        hamc.last_upd_date,
        hamc.last_upd_user_code,
        hamc.last_upd_user_name
        from attendance_mobile_config hamc
        left join user_info hui
        on hui.user_code is not null and hamc.user_code = hui.user_code
        where hui.is_delete = 0 and hamc.is_delete = 0
        <if test="country != null and country != ''">
            and hui.location_country = #{country}
        </if>
        <if test="countryList!=null and countryList.size()>0">
            <foreach collection="countryList" item="country" open="and hui.location_country in (" close=")"
                     separator=",">
                #{country}
            </foreach>
        </if>
        <if test="userCodeOrName != null and userCodeOrName != ''">
            and (hui.user_code like concat('%', #{userCodeOrName}, '%') or hui.user_name like
            concat('%', #{userCodeOrName}, '%') or
            hui.user_name_en like concat('%', #{userCodeOrName}, '%'))
        </if>
    </select>
</mapper>
