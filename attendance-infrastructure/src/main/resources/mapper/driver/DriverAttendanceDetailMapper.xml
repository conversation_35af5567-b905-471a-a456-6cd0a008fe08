<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.driver.mapper.DriverAttendanceDetailMapper">
    <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.driver.model.DriverAttendanceDetailDO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
        <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
        <result column="user_code" jdbcType="VARCHAR" property="userCode" />
        <result column="year" jdbcType="INTEGER" property="year" />
        <result column="month" jdbcType="INTEGER" property="month" />
        <result column="day" jdbcType="INTEGER" property="day" />
        <result column="date" jdbcType="TIMESTAMP" property="date" />
        <result column="day_id" jdbcType="BIGINT" property="dayId" />
        <result column="attendance_type" jdbcType="TINYINT" property="attendanceType" />
        <result column="dld_number" jdbcType="BIGINT" property="dldNumber" />
        <result column="locus_number" jdbcType="BIGINT" property="locusNumber" />
        <result column="last_operating_time" jdbcType="TIMESTAMP" property="lastOperatingTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, is_delete, record_version, create_date, create_user_code, create_user_name,
    last_upd_date, last_upd_user_code, last_upd_user_name, user_code, year, month, day,
    date, day_id, attendance_type, dld_number, locus_number, last_operating_time
    </sql>


    <select id="queryDriverAttendance" parameterType="com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceDetailQuery"
            resultType="com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceDetailDTO">
        <!--@mbg.generated-->
        select
        dad.id,
        ui.user_code,
        ui.id as user_id,
        ui.user_name,
        ui.user_name_en,
        ui.work_status,
        ui.status,
        dad.attendance_type,
        dad.locus_number,
        dad.dld_number,
        ui.location_country,
        ui.vendor_code,
        ui.vendor_name,
        ui.dept_id,
        ui.oc_code,
        ui.post_id,
        ui.employee_type,
        uer.entry_date,
        udr.actual_dimission_date,
        dad.last_operating_time,
        dad.day_id,
        dad.year,
        dad.month,
        dad.day,
        dad.date
        from user_info ui
        left join driver_attendance_detail dad on ui.user_code = dad.user_code  and dad.is_delete = 0
        <if test="dayId != null and dayId !=''">
            and dad.day_id = #{dayId}
        </if>
        left join user_entry_record uer on ui.id = uer.user_id and uer.is_delete = 0
        <!--    只需要状态是DIMISSION才拿出来离职时间，其他不需要离职时间-->
        left join user_dimission_record udr on ui.id = udr.user_id and udr.is_delete = 0 and udr.dimission_status = 'DIMISSION'
        where
        ui.is_delete = 0
        and ui.user_code is not null
        <!--    过滤work_status不为空的数据：work_status 为空表示没有入职，不需要查询-->
        and ui.work_status != ''
        <if test="country != null and country !=''">
            and ui.location_country = #{country}
        </if>
        <if test="attendanceTypeList!=null and attendanceTypeList.size()>0">
            <foreach collection="attendanceTypeList" separator="," open="and dad.attendance_type in (" close=")" item="type">
                #{type}
            </foreach>
        </if>
        <if test="employeeTypeList!=null and employeeTypeList.size()>0">
            <foreach collection="employeeTypeList" separator="," open="and ui.employee_type in (" close=")" item="employeeType">
                #{employeeType}
            </foreach>
        </if>
        <if test="status != null and status !=''">
            and ui.status = #{status}
        </if>
        <if test="workStatus != null and workStatus !=''">
            and ui.work_status = #{workStatus}
        </if>
        <if test="workStatusList!=null and workStatusList.size()>0">
            <foreach collection="workStatusList" separator="," open="and ui.work_status in (" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="vendorCode != null and vendorCode !=''">
            and ui.vendor_code = #{vendorCode}
        </if>
        <if test="userCodeOrName!=null and userCodeOrName!=''">
            and (ui.user_code like concat('%',#{userCodeOrName},'%') or
            ui.user_name like concat('%',#{userCodeOrName},'%') or
            ui.user_name_en like concat('%',#{userCodeOrName},'%'))
        </if>
        <if test="deptId != null and deptId !=''">
            and ui.dept_id = #{deptId}
        </if>
        <if test="deptList!=null and deptList.size()>0">
            <foreach collection="deptList" separator="," open="and ui.dept_id in (" close=")" item="deptId">
                #{deptId}
            </foreach>
        </if>
        <if test="isDriver!=null and isDriver!=''">
            and ui.is_driver = #{isDriver}
        </if>
        <if test="dayDateString != null and dayDateString != ''">
            <!--      该条件表示：没有离职记录的数据或者有正式离职的数据但是离职日期大于等于查询日期-->
            and (udr.id is null or udr.actual_dimission_date &gt;= #{dayDateString})
        </if>
        <if test="countryList!=null and countryList.size()>0">
            <foreach collection="countryList" separator="," open="and ui.location_country in (" close=")" item="country">
                #{country}
            </foreach>
        </if>
        <include refid="resourceSql"/>
    </select>

    <select id="queryDriverMonthAttendance" parameterType="com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceDetailMonthQuery"
            resultType="com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceDetailMonthDTO">
        <!--@mbg.generated-->
        select
        ui.id as user_id,
        ui.user_code,
        ui.user_name,
        ui.user_name_en,
        ui.work_status,
        ui.status,
        ui.location_country,
        ui.vendor_code,
        ui.vendor_name,
        ui.dept_id,
        ui.oc_code,
        ui.post_id,
        ui.employee_type,
        uer.entry_date,
        udr.actual_dimission_date
        from user_info ui
        left join user_entry_record uer on ui.id = uer.user_id and uer.is_delete = 0
        <!--    只需要状态是DIMISSION才拿出来离职时间，其他不需要离职时间-->
        left join user_dimission_record udr on ui.id = udr.user_id and udr.is_delete = 0 and udr.dimission_status = 'DIMISSION'
        where
        ui.is_delete = 0
        and ui.user_code is not null
        <!--    过滤work_status不为空的数据：work_status 为空表示没有入职，不需要查询-->
        and ui.work_status != ''
        <if test="country != null and country !=''">
            and ui.location_country = #{country}
        </if>
        <if test="employeeTypeList!=null and employeeTypeList.size()>0">
            <foreach collection="employeeTypeList" separator="," open="and ui.employee_type in (" close=")" item="employeeType">
                #{employeeType}
            </foreach>
        </if>
        <if test="status != null and status !=''">
            and ui.status = #{status}
        </if>
        <if test="workStatus != null and workStatus !=''">
            and ui.work_status = #{workStatus}
        </if>
        <if test="workStatusList!=null and workStatusList.size()>0">
            <foreach collection="workStatusList" separator="," open="and ui.work_status in (" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="vendorCode != null and vendorCode !=''">
            and ui.vendor_code = #{vendorCode}
        </if>
        <if test="userCodeOrName!=null and userCodeOrName!=''">
            and (ui.user_code like concat('%',#{userCodeOrName},'%') or
            ui.user_name like concat('%',#{userCodeOrName},'%') or
            ui.user_name_en like concat('%',#{userCodeOrName},'%'))
        </if>
        <if test="deptId != null and deptId !=''">
            and ui.dept_id = #{deptId}
        </if>
        <if test="deptList!=null and deptList.size()>0">
            <foreach collection="deptList" separator="," open="and ui.dept_id in (" close=")" item="deptId">
                #{deptId}
            </foreach>
        </if>
        <if test="userCodeList!=null and userCodeList.size()>0">
            <foreach collection="userCodeList" separator="," open="and ui.user_code in (" close=")" item="userCode">
                #{userCode}
            </foreach>
        </if>
        <if test="isDriver!=null and isDriver!=''">
            and ui.is_driver = #{isDriver}
        </if>
        <if test="(monthStartDateString != null and monthStartDateString != '') and ( monthEndDateString != null and monthEndDateString != '')">
            <!--      该条件表示：没有离职记录的数据或者有正式离职的数据但是离职日期大于等于查询日期开始日期并且小于等于结束日期-->
            and (udr.id is null or (udr.actual_dimission_date &gt;= #{monthStartDateString} and udr.actual_dimission_date &lt;= #{monthEndDateString}))

        </if>

        <if test="countryList!=null and countryList.size()>0">
            <foreach collection="countryList" separator="," open="and ui.location_country in (" close=")" item="country">
                #{country}
            </foreach>
        </if>
        <include refid="resourceSql"/>
    </select>


    <sql id="resourceSql">
        <if test="resourceType!=null">
            <!--无数据权限时-->
            <if test="resourceType=='NONE'">
                AND ui.id = -1
            </if>
            <!--部门数据权限-->
            <if test="resourceType=='DEPT_ID'">
                <!--0，标识全部数据权限-->
                <if test="!organizationIds.contains(0L)">
                    <foreach collection="organizationIds" item = 'id' open="and ui.dept_id in (" close=")" separator=",">
                        #{id}
                    </foreach>
                </if>
            </if>
            <!--全部数据权限，不做任何操作-->
            <if test="resourceType=='ALL'">

            </if>
        </if>
    </sql>






</mapper>
