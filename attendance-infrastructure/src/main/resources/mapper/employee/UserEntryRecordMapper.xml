<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.employee.mapper.UserEntryRecordMapper">

    <!-- 根据国家和入职确认时间查找用户 -->
    <select id="findUsersByCountryAndConfirmDate"
            resultType="com.imile.attendance.infrastructure.repository.employee.dto.UserEntryInfoDTO">
        SELECT
            ui.id as userId,
            ui.user_code as userCode,
            ui.user_name as userName,
            uer.confirm_date as confirmDate,
            ui.location_country as locationCountry
        FROM user_entry_record uer
        LEFT JOIN user_info ui ON uer.user_id = ui.id
        WHERE ui.location_country = #{locationCountry}
          AND uer.confirm_date >= #{confirmDate}
          AND ui.is_delete = 0 AND uer.is_delete = 0
          AND uer.entry_status = 'ENTRY'
    </select>

</mapper>