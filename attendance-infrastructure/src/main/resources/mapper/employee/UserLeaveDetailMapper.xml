<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.employee.mapper.UserLeaveDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO">
        <id column="id" property="id"/>
        <result column="is_delete" property="isDelete"/>
        <result column="create_date" property="createDate"/>
        <result column="create_user_code" property="createUserCode"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="last_upd_date" property="lastUpdDate"/>
        <result column="last_upd_user_code" property="lastUpdUserCode"/>
        <result column="last_upd_user_name" property="lastUpdUserName"/>
        <result column="config_id" property="configId"/>
        <result column="user_id" property="userId"/>
        <result column="user_code" property="userCode"/>
        <result column="leave_name" property="leaveName"/>
        <result column="leave_type" property="leaveType"/>
        <result column="status" property="status"/>
        <result column="record_version" property="recordVersion"/>

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, config_id, user_id, user_code, leave_name, leave_type, status ,record_version
    </sql>

    <select id="selectBatchUserResidual"
            resultType="com.imile.attendance.infrastructure.repository.employee.dto.UserLeaveBalanceDTO">
        SELECT
        t2.id AS userId,
        t2.user_name AS userName,
        t2.user_code AS userCode,
        t4.is_invalid AS isInvalid,
        t4.issue_date AS issueDate,
        t4.invalid_date AS invalidDate,
        t3.is_dispatch AS isDispatch,
        t3.country AS country,
        t3.leave_type AS leaveType,
        t3.leave_name AS leaveName,
        t4.leave_mark AS leaveMark,
        t3.leave_usage_restrictions AS useCondition,
        t4.leave_used_minutes + t4.leave_residue_minutes AS leaveTotalMinutes,
        t4.leave_used_minutes AS leaveUsedMinutes,
        t4.leave_residue_minutes AS leaveResidueMinutes,
        t4.percent_salary AS percentSalary
        FROM user_leave_detail AS t1
        LEFT JOIN user_info AS t2 ON t2.id = t1.user_id AND t2.is_delete = 0
        LEFT JOIN company_leave_config AS t3 ON t3.id = t1.config_id AND t3.is_delete = 0
        LEFT JOIN user_leave_stage_detail AS t4 ON t4.leave_id = t1.id AND t4.is_delete = 0
        WHERE t1.is_delete = 0
        <if test="userIds != null and userIds.size() > 0">
            AND t1.user_id IN
            <foreach collection="userIds" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
