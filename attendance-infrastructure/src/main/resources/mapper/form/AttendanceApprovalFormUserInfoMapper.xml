<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.form.mapper.AttendanceApprovalFormUserInfoMapper">
  <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormUserInfoDO">
    <!--@mbg.generated-->
    <!--@Table hrms.hrms_approval_form_user_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
    <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
    <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="day_id" jdbcType="BIGINT" property="dayId" />
    <result column="user_code" jdbcType="VARCHAR" property="userCode" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="dept_id" jdbcType="BIGINT" property="deptId" />
    <result column="post_id" jdbcType="BIGINT" property="postId" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="start_date" jdbcType="TIMESTAMP" property="startDate" />
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
    <result column="estimate_duration" jdbcType="DECIMAL" property="estimateDuration" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
      <!--@mbg.generated-->
      id, is_delete, record_version, create_date, create_user_code, create_user_name, last_upd_date,
      last_upd_user_code, last_upd_user_name, form_id, day_id, user_code, user_name, dept_id,
      post_id, country, start_date, end_date, estimate_duration, remark
  </sql>

  <select id="selectListByCondition"
          parameterType="com.imile.attendance.infrastructure.repository.form.query.OverTimeListQuery"
          resultType="com.imile.attendance.infrastructure.repository.form.dto.OverTimeApprovalListDTO">
      select
      t1.user_code, t1.user_name, t1.dept_id, t1.post_id, t1.start_date, t1.country,
      t2.application_code, t2.id, t2.form_type, t2.form_status, t2.approval_id,
      t1.create_date, t1.create_user_code, t1.create_user_name, t1.day_id, t1.remark,
      t1.start_date, t1.end_date, t1.estimate_duration, t1.last_upd_date, t1.last_upd_user_code, t1.last_upd_user_name
      from attendance_approval_form_user_info t1
      left join attendance_approval_form t2 on t1.form_id = t2.id
      <where>
          t1.is_delete = 0 and t2.is_delete = 0
          <if test="query.userCode!=null and query.userCode!=''">
              and t1.user_code = #{query.userCode}
          </if>
          <if test="query.dayId!=null">
              and t1.day_id = #{query.dayId}
          </if>
          <if test="query.userCodeList!=null and query.userCodeList.size()>0">
              <foreach collection="query.userCodeList" item="userCode" open="and t1.user_code in (" close=")"
                       separator=",">
                  #{userCode}
              </foreach>
          </if>
          <if test="query.userCodeOrName!=null and query.userCodeOrName!=''">
              and (t1.user_name like concat('%',#{query.userCodeOrName},'%') or t1.user_code like
              concat('%',#{query.userCodeOrName},'%'))
          </if>
          <if test="query.country!=null and query.country!=''">
              and t1.country = #{query.country}
          </if>
          <if test="query.deptIdList!=null and query.deptIdList.size()>0">
              <foreach collection="query.deptIdList" item="deptId" open="and t1.dept_id in (" close=")"
                       separator=",">
                  #{deptId}
              </foreach>
          </if>
          <if test="query.postIdList!=null and query.postIdList.size()>0">
              <foreach collection="query.postIdList" item="postId" open="and t1.post_id in (" close=")"
                       separator=",">
                  #{postId}
              </foreach>
          </if>
          <if test="query.formStatus!=null and query.formStatus!=''">
              and t2.form_status = #{query.formStatus}
          </if>
          <if test="query.formStatusList!=null and query.formStatusList.size()>0">
              <foreach collection="query.formStatusList" item="formStatus" open="and t2.form_status in (" close=")"
                       separator=",">
                  #{formStatus}
              </foreach>
          </if>
          <if test="query.formTypeList!=null and query.formTypeList.size()>0">
              <foreach collection="query.formTypeList" item="formType" open="and t2.form_type in (" close=")"
                       separator=",">
                  #{formType}
              </foreach>
          </if>
          <if test="query.applicationCode!=null and query.applicationCode!=''">
              and t2.application_code = #{query.applicationCode}
          </if>
          <if test="query.startDate != null">
              and t2.create_date &gt;= #{query.startDate}
          </if>
          <if test="query.endDate != null">
              and t2.create_date &lt;= #{query.endDate}
          </if>
      </where>
      order by t1.last_upd_date desc
  </select>
</mapper>