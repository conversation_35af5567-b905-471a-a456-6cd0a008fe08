<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.form.mapper.AttendanceFormMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO">
        <id column="id" property="id"/>
        <result column="create_date" property="createDate"/>
        <result column="create_user_code" property="createUserCode"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="last_upd_date" property="lastUpdDate"/>
        <result column="last_upd_user_code" property="lastUpdUserCode"/>
        <result column="last_upd_user_name" property="lastUpdUserName"/>
        <result column="is_delete" property="isDelete"/>
        <result column="record_version" property="recordVersion"/>
        <result column="apply_user_id" property="applyUserId"/>
        <result column="user_id" property="userId"/>
        <result column="user_code" property="userCode"/>
        <result column="user_name" property="userName"/>
        <result column="dept_id" property="deptId"/>
        <result column="post_id" property="postId"/>
        <result column="country" property="country"/>
        <result column="origin_country" property="originCountry" />
        <result column="is_warehouse_staff" property="isWarehouseStaff" />
        <result column="application_code" property="applicationCode"/>
        <result column="form_type" property="formType"/>
        <result column="form_status" property="formStatus"/>
        <result column="approval_id" property="approvalId"/>
        <result column="approval_process_info" property="approvalProcessInfo"/>
        <result column="data_source" property="dataSource"/>

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        record_version,
        id, apply_user_id, user_id, user_code, user_name, dept_id, post_id, country, origin_country, is_warehouse_staff, application_code, form_type, form_status, approval_id, approval_process_info, data_source
    </sql>

    <select id="selectAttendanceApprovalInfo" resultMap="BaseResultMap">
        select
        t1.*
        from attendance_form t1
        <if test="(query.leaveType != null and query.leaveType != '') or (query.reissueCardType != null and query.reissueCardType != '')">
            left join attendance_form_attr t2 on t1.id = t2.form_id
        </if>
        <where>
            t1.is_delete = 0
            <if test="(query.leaveType != null and query.leaveType != '') or (query.reissueCardType != null and query.reissueCardType != '')">
                and t2.is_delete = 0
            </if>
            <if test="query.applyUserId != null">
                and t1.apply_user_id = #{query.applyUserId}
            </if>
            <if test="query.userIdList!=null and query.userIdList.size()>0">
                <foreach collection="query.userIdList" item="userId" open="and t1.user_id in (" close=")"
                         separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="query.formStatusList!=null and query.formStatusList.size()>0">
                <foreach collection="query.formStatusList" item="formStatus" open="and t1.form_status in (" close=")"
                         separator=",">
                    #{formStatus}
                </foreach>
            </if>
            <if test="query.userName!=null and query.userName!=''">
                and t1.user_name = #{query.userName}
            </if>
            <if test="query.userCodeOrName!=null and query.userCodeOrName!=''">
                and (t1.user_name like concat('%',#{query.userCodeOrName},'%') or t1.user_code like concat('%',#{query.userCodeOrName},'%'))
            </if>
            <if test="query.deptId != null">
                and t1.dept_id = #{query.deptId}
            </if>
            <!-- 同时具有部门和国家权限 -->
            <if test="query.authDeptIdList != null and query.authDeptIdList.size() > 0 and
            query.authLocationCountryList != null and query.authLocationCountryList.size() > 0">
                and (t1.dept_id in
                <foreach collection="query.authDeptIdList" item="deptId" separator="," open="(" close=")">
                    #{deptId}
                </foreach>
                or t1.country in
                <foreach collection="query.authLocationCountryList" item="country" separator="," open="(" close=")">
                    #{country}
                </foreach>
                )
            </if>
            <!-- 只有部门权限 -->
            <if test="query.authDeptIdList != null and query.authDeptIdList.size() > 0 and
            (query.authLocationCountryList == null or query.authLocationCountryList.size() == 0)">
                and (t1.dept_id in
                <foreach collection="query.authDeptIdList" item="deptId" separator="," open="(" close=")">
                    #{deptId}
                </foreach>
                )
            </if>
            <!-- 只有国家权限 -->
            <if test="query.authLocationCountryList != null and query.authLocationCountryList.size() > 0 and
            (query.authDeptIdList == null or query.authDeptIdList.size() == 0)">
                and (t1.country in
                <foreach collection="query.authLocationCountryList" item="country" separator="," open="(" close=")">
                    #{country}
                </foreach>
                )
            </if>
            <if test="query.deptIds!=null and query.deptIds.size()>0">
                <foreach collection="query.deptIds" item="deptId" open="and t1.dept_id in (" close=")"
                         separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="query.postId != null">
                and t1.post_id = #{query.postId}
            </if>
            <if test="query.formTypeList!=null and query.formTypeList.size()>0">
                <foreach collection="query.formTypeList" item="formType" open="and t1.form_type in (" close=")"
                         separator=",">
                    #{formType}
                </foreach>
            </if>
            <if test="query.startDate != null">
                and t1.create_date &gt;= #{query.startDate}
            </if>
            <if test="query.endDate != null">
                and t1.create_date &lt;= #{query.endDate}
            </if>
            <if test="query.applicationFormCode != null and query.applicationFormCode!=''">
                and t1.application_code = #{query.applicationFormCode}
            </if>
            <if test="query.excludeApplicationDataSource != null and query.excludeApplicationDataSource != ''">
                and t1.data_source is null
            </if>
            <if test="query.country != null and query.country!=''">
                and t1.country = #{query.country}
            </if>
            <if test="(query.leaveType != null and query.leaveType != '') or (query.reissueCardType != null and query.reissueCardType != '')">
                and t2.attr_key in ('leavetype', 'reissuecardtype')
            </if>
            <if test="query.leaveType != null and query.leaveType != ''">
                and t2.attr_key = 'leavetype' and t2.attr_value = #{query.leaveType}
            </if>
            <if test="query.reissueCardType != null and query.reissueCardType != ''">
                and t2.attr_key = 'reissuecardtype' and t2.attr_value = #{query.reissueCardType}
            </if>
        </where>
        order by t1.last_upd_date desc
    </select>
</mapper>
