<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsAttendanceClassEmployeeConfigMapper">

    <!-- 通用结果映射 -->
    <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendanceClassEmployeeConfigDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="attendance_config_id" property="attendanceConfigId" />
        <result column="punch_config_id" property="punchConfigId" />
        <result column="class_time" property="classTime" />
        <result column="day_id" property="dayId" />
        <result column="class_id" property="classId" />
        <result column="day_punch_type" property="dayPunchType" />
        <result column="data_source" property="dataSource" />
        <result column="is_latest" property="isLatest" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="orderby" property="orderby" />
        <result column="company_id" property="companyId" />
    </resultMap>

    <!-- 通用列定义 -->
    <sql id="Base_Column_List">
        id, user_id, attendance_config_id, punch_config_id, class_time, day_id, class_id, 
        day_punch_type, data_source, is_latest, is_delete, record_version, create_date, 
        create_user_code, create_user_name, last_upd_date, last_upd_user_code, 
        last_upd_user_name, orderby, company_id
    </sql>

    <!-- 按国家和日期范围分页查询排班记录（用于数据迁移） -->
    <select id="pageByCountryAndDateRange" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM hrms_attendance_class_employee_config h
        INNER JOIN user_info u ON h.user_id = u.id
        WHERE h.is_delete = 0 and h.is_latest= 1
        <if test="country != null and country != ''">
            AND u.location_country = #{country}
        </if>
        <if test="startDayId != null">
            AND h.day_id &gt;= #{startDayId}
        </if>
        <if test="endDayId != null">
            AND h.day_id &lt;= #{endDayId}
        </if>
        ORDER BY h.create_date ASC, h.id ASC
    </select>

    <!-- 统计按国家和日期范围的排班记录数量 -->
    <select id="countByCountryAndDateRange" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM hrms_attendance_class_employee_config h
        INNER JOIN user_info u ON h.user_id = u.id
        WHERE h.is_delete = 0 and h.is_latest= 1
        <if test="country != null and country != ''">
            AND u.location_country = #{country}
        </if>
        <if test="startDayId != null">
            AND h.day_id &gt;= #{startDayId}
        </if>
        <if test="endDayId != null">
            AND h.day_id &lt;= #{endDayId}
        </if>
    </select>

    <!-- 按考勤组ID列表和日期范围分页查询排班记录 -->
    <select id="pageByPunchConfigIdsAndDateRange" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM hrms_attendance_class_employee_config h
        WHERE h.is_delete = 0 and h.is_latest= 1
        <if test="punchConfigIds != null and punchConfigIds.size() > 0">
            AND h.punch_config_id IN
            <foreach collection="punchConfigIds" item="punchConfigId" open="(" separator="," close=")">
                #{punchConfigId}
            </foreach>
        </if>
        <if test="startDayId != null">
            AND h.day_id &gt;= #{startDayId}
        </if>
        <if test="endDayId != null">
            AND h.day_id &lt;= #{endDayId}
        </if>
        ORDER BY h.create_date , h.id
    </select>

    <!-- 统计按考勤组ID列表和日期范围的排班记录数量-->
    <select id="countByPunchConfigIdsAndDateRange" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM hrms_attendance_class_employee_config h
        WHERE h.is_delete = 0 and h.is_latest= 1
        <if test="punchConfigIds != null and punchConfigIds.size() > 0">
            AND h.punch_config_id IN
            <foreach collection="punchConfigIds" item="punchConfigId" open="(" separator="," close=")">
                #{punchConfigId}
            </foreach>
        </if>
        <if test="startDayId != null">
            AND h.day_id &gt;= #{startDayId}
        </if>
        <if test="endDayId != null">
            AND h.day_id &lt;= #{endDayId}
        </if>
    </select>

    <!-- 按日期范围分页查询历史数据（用于历史数据迁移，不按国家分组） -->
    <select id="pageByDateRangeForHistory" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM hrms_attendance_class_employee_config h
        WHERE h.is_delete = 0 and h.is_latest= 1
        <if test="startDayId != null">
            AND h.day_id &gt;= #{startDayId}
        </if>
        <if test="endDayId != null">
            AND h.day_id &lt;= #{endDayId}
        </if>
        ORDER BY h.create_date ASC, h.id ASC
    </select>

    <!-- 统计按日期范围的历史数据数量（用于历史数据迁移，不按国家分组） -->
    <select id="countByDateRangeForHistory" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM hrms_attendance_class_employee_config h
        WHERE h.is_delete = 0 and h.is_latest= 1
        <if test="startDayId != null">
            AND h.day_id &gt;= #{startDayId}
        </if>
        <if test="endDayId != null">
            AND h.day_id &lt;= #{endDayId}
        </if>
    </select>

    <!-- 按用户ID列表和日期范围分页查询排班记录 -->
    <select id="pageByUserIdListAndDateRange" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM hrms_attendance_class_employee_config h
        WHERE h.is_delete = 0 and h.is_latest= 1
        <if test="userIdList != null and userIdList.size() > 0">
            AND h.user_id IN
            <foreach collection="userIdList" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="startDayId != null">
            AND h.day_id &gt;= #{startDayId}
        </if>
        <if test="endDayId != null">
            AND h.day_id &lt;= #{endDayId}
        </if>
        ORDER BY h.create_date ASC, h.id ASC
    </select>

    <!-- 统计按用户ID列表和日期范围的排班记录数量 -->
    <select id="countByUserIdListAndDateRange" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM hrms_attendance_class_employee_config h
        WHERE h.is_delete = 0 and h.is_latest= 1
        <if test="userIdList != null and userIdList.size() > 0">
            AND h.user_id IN
            <foreach collection="userIdList" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="startDayId != null">
            AND h.day_id &gt;= #{startDayId}
        </if>
        <if test="endDayId != null">
            AND h.day_id &lt;= #{endDayId}
        </if>
    </select>

</mapper>
