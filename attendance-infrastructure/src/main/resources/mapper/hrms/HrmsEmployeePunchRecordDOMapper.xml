<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsEmployeePunchRecordMapper">


    <select id="listReissueCard" resultType="com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeePunchRecordDO"
            parameterType="com.imile.attendance.infrastructure.repository.hrms.query.AbnormalMigrationQuery">
        select pr.*
        from employee_punch_record pr
        left join hrms_user_info hui on hui.user_code = pr.user_code
        <where>
            pr.source_type = 'REISSUE_CARD' and pr.from_new_system = 0
            <if test="countryList!=null and countryList.size()>0">
                <foreach collection="countryList" item="locationCountry" open="and hui.location_country in (" close=")"
                         separator=",">
                    #{locationCountry}
                </foreach>
            </if>
            <if test="deptIdList!=null and deptIdList.size()>0">
                <foreach collection="deptIdList" item="deptId" open="and hui.dept_id in (" close=")"
                         separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="startDayId!=null">
                and pr.day_id >= #{startDayId}
            </if>
            <if test="endDayId!=null">
                and pr.day_id &lt;= #{endDayId}
            </if>
            <if test="userCodeList!=null and userCodeList.size()>0">
                <foreach collection="userCodeList" item="userCode" open="and pr.user_code in (" close=")"
                         separator=",">
                    #{userCode}
                </foreach>
            </if>
        </where>
    </select>

</mapper>
