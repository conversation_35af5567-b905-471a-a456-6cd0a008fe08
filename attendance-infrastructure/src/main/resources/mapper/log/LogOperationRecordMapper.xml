<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.log.mapper.LogOperationRecordMapper">
    <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.log.model.LogOperationRecordDO">
        <!--@mbg.generated-->
        <!--@Table log_operation_record-->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="user_id" jdbcType="BIGINT" property="userId" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="dept_id" jdbcType="BIGINT" property="deptId" />
        <result column="foreign_key" jdbcType="VARCHAR" property="foreignKey" />
        <result column="foreign_table" jdbcType="VARCHAR" property="foreignTable" />
        <result column="operation_module" jdbcType="VARCHAR" property="operationModule" />
        <result column="operation_type" jdbcType="VARCHAR" property="operationType" />
        <result column="operation_code" jdbcType="VARCHAR" property="operationCode" />
        <result column="operation_user_code" jdbcType="VARCHAR" property="operationUserCode" />
        <result column="operation_user_name" jdbcType="VARCHAR" property="operationUserName" />
        <result column="operation_time" jdbcType="TIMESTAMP" property="operationTime" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
        <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
        <result column="field_diff" jdbcType="LONGVARCHAR" property="fieldDiff" />
    </resultMap>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, user_id, user_name, dept_id, foreign_key, foreign_table, operation_module,
        operation_type, operation_code, operation_user_code, operation_user_name, operation_time,
        remark, is_delete, record_version, create_date, create_user_code, create_user_name,
        last_upd_date, last_upd_user_code, last_upd_user_name, field_diff
    </sql>

    <select id="listPage" resultType="com.imile.attendance.infrastructure.repository.log.model.LogOperationRecordDO"
            parameterType="com.imile.attendance.infrastructure.repository.log.query.LogRecordPageQuery">
        select
        <include refid="Base_Column_List"/>
        from log_operation_record
        where is_delete = 0
        <if test="operationModule != null and operationModule != ''">
            and operation_module = #{operationModule}
        </if>
        <if test="operator != null and operator != ''">
            and (
            operation_user_code like concat('%',#{operator},'%') or
            operation_user_name like concat('%',#{operator},'%')
            )
        </if>
        <if test="operationTimeStart != null">
            and operation_time &gt;= #{operationTimeStart}
        </if>
        <if test="operationTimeEnd != null">
            and operation_time &lt;= #{operationTimeEnd}
        </if>
        <if test="operationContent != null and operationContent != ''">
            and operation_code like concat('%',#{operationContent},'%')
        </if>
    </select>
</mapper>
