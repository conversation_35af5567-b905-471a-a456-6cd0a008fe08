<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.rule.mapper.migrate.ReissueCardConfigRangeMigrateMapper">

    <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.rule.model.migrate.ReissueCardConfigRangeMigrateDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="rule_config_id" property="ruleConfigId" jdbcType="BIGINT"/>
        <result column="rule_config_no" property="ruleConfigNo" jdbcType="VARCHAR"/>
        <result column="biz_id" property="bizId" jdbcType="BIGINT"/>
        <result column="range_type" property="rangeType" jdbcType="VARCHAR"/>
        <result column="effect_time" property="effectTime" jdbcType="TIMESTAMP"/>
        <result column="expire_time" property="expireTime" jdbcType="TIMESTAMP"/>
        <result column="effect_timestamp" property="effectTimestamp" jdbcType="BIGINT"/>
        <result column="expire_timestamp" property="expireTimestamp" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="is_latest" property="isLatest" jdbcType="TINYINT"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="record_version" property="recordVersion" jdbcType="BIGINT"/>
        <result column="create_date" property="createDate" jdbcType="TIMESTAMP"/>
        <result column="create_user_code" property="createUserCode" jdbcType="VARCHAR"/>
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
        <result column="last_upd_date" property="lastUpdDate" jdbcType="TIMESTAMP"/>
        <result column="last_upd_user_code" property="lastUpdUserCode" jdbcType="VARCHAR"/>
        <result column="last_upd_user_name" property="lastUpdUserName" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="listOnJobNoDriverUsersExcludeConfigured"
            parameterType="com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery"
            resultType="com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO">
        SELECT u.*
        FROM user_info u
        LEFT JOIN (SELECT DISTINCT biz_id
        FROM reissue_card_config_range_migrate
        WHERE is_latest = 1
        AND status = 'ACTIVE'
        AND is_delete = 0 ) r ON u.id = r.biz_id
        WHERE u.is_driver = 0
        AND u.work_status = 'ON_JOB'
        AND u.`status`= 'ACTIVE'
        AND u.is_delete = 0
        <if test="country != null and country != ''">
            AND u.location_country = #{country}
        </if>
        <if test="countries != null and countries.size() > 0">
            AND u.location_country IN
            <foreach collection="countries" item="country" open="(" separator="," close=")">
                #{country}
            </foreach>
        </if>
        <if test="userIds != null and userIds.size() > 0">
            AND u.id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="deptIds != null and deptIds.size() > 0">
            AND u.dept_id IN
            <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="codeOrNameLike != null and codeOrNameLike != ''">
            AND (u.user_code LIKE CONCAT('%', #{codeOrNameLike}, '%')
            OR u.user_name LIKE CONCAT('%', #{codeOrNameLike}, '%'))
        </if>
        <if test="employeeTypeList != null and employeeTypeList.size() > 0">
            AND u.employee_type IN
            <foreach collection="employeeTypeList" item="employeeType" open="(" separator="," close=")">
                #{employeeType}
            </foreach>
        </if>
        AND r.biz_id IS NULL
    </select>

</mapper>
