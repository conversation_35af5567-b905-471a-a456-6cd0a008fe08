<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.report.mapper.AttendanceDayReportFormMapper">

    <resultMap id="BaseResultMap"
               type="com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportFormDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="day_report_id" jdbcType="BIGINT" property="dayReportId"/>
        <result column="form_id" jdbcType="BIGINT" property="formId"/>
        <result column="application_code" jdbcType="VARCHAR" property="applicationCode"/>
        <result column="approval_id" jdbcType="BIGINT" property="approvalId"/>
        <result column="form_type" jdbcType="VARCHAR" property="formType"/>
        <result column="form_status" jdbcType="VARCHAR" property="formStatus"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="record_version" jdbcType="BIGINT" property="recordVersion"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate"/>
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode"/>
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
        day_report_id,
        form_id,
        application_code,
        approval_id,
        form_type,
        form_status,
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name
    </sql>

</mapper>
