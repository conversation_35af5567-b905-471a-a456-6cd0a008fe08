<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.rule.mapper.OverTimeConfigMapper">
    <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="config_no" property="configNo" jdbcType="VARCHAR"/>
        <result column="config_name" property="configName" jdbcType="VARCHAR"/>
        <result column="overtime_config" property="overtimeConfig" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="country" property="country" jdbcType="VARCHAR"/>
        <result column="dept_ids" property="deptIds" jdbcType="VARCHAR"/>
        <result column="is_latest" property="isLatest" jdbcType="TINYINT"/>
        <result column="effect_time" property="effectTime" jdbcType="TIMESTAMP"/>
        <result column="expire_time" property="expireTime" jdbcType="TIMESTAMP"/>
        <result column="effect_timestamp" property="effectTimestamp" jdbcType="BIGINT"/>
        <result column="expire_timestamp" property="expireTimestamp" jdbcType="BIGINT"/>
        <result column="is_country_level" property="isCountryLevel" jdbcType="TINYINT"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="record_version" property="recordVersion" jdbcType="BIGINT"/>
        <result column="create_date" property="createDate" jdbcType="TIMESTAMP"/>
        <result column="create_user_code" property="createUserCode" jdbcType="VARCHAR"/>
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
        <result column="last_upd_date" property="lastUpdDate" jdbcType="TIMESTAMP"/>
        <result column="last_upd_user_code" property="lastUpdUserCode" jdbcType="VARCHAR"/>
        <result column="last_upd_user_name" property="lastUpdUserName" jdbcType="VARCHAR"/>
        <result column="working_out_start_time" property="workingOutStartTime" jdbcType="DECIMAL"/>
        <result column="working_effective_time" property="workingEffectiveTime" jdbcType="DECIMAL"/>
        <result column="working_subsidy_type" property="workingSubsidyType" jdbcType="VARCHAR"/>
        <result column="rest_effective_time" property="restEffectiveTime" jdbcType="DECIMAL"/>
        <result column="rest_subsidy_type" property="restSubsidyType" jdbcType="VARCHAR"/>
        <result column="holiday_effective_time" property="holidayEffectiveTime" jdbcType="DECIMAL"/>
        <result column="holiday_subsidy_type" property="holidaySubsidyType" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, config_no, config_name, overtime_config, `status`, country,
        dept_ids, is_latest, effect_time, expire_time, effect_timestamp, expire_timestamp, is_country_level,
        is_delete, record_version, create_date, create_user_code, create_user_name, last_upd_date, last_upd_user_code, last_upd_user_name,
        working_out_start_time, working_effective_time, working_subsidy_type,
        rest_effective_time, rest_subsidy_type, holiday_effective_time, holiday_subsidy_type
    </sql>

    <sql id="Join_Column_List">
        oc.id, oc.config_no, oc.config_name, oc.overtime_config, oc.status, oc.country,
        oc.dept_ids, oc.is_latest, oc.effect_time, oc.expire_time, oc.effect_timestamp, oc.expire_timestamp, oc.is_country_level,
        oc.is_delete,oc.record_version, oc.create_date, oc.create_user_code,
        oc.create_user_name, oc.last_upd_date, oc.last_upd_user_code, oc.last_upd_user_name,
        oc.working_out_start_time, oc.working_effective_time, oc.working_subsidy_type,
        oc.rest_effective_time, oc.rest_subsidy_type, oc.holiday_effective_time, oc.holiday_subsidy_type
    </sql>

    <select id="listCountryLevelConfigsByCountries"
            resultType="com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO">
        SELECT
        <include refid="Join_Column_List"/>
        FROM
        over_time_config oc
        LEFT JOIN over_time_config_range ocr ON oc.id = ocr.rule_config_id
        WHERE
        ocr.id IS NULL
        AND oc.is_latest = 1 AND oc.is_delete = 0
        AND ( oc.dept_ids IS NULL OR oc.dept_ids = '' )
        <if test="countryList != null and countryList.size() != 0">
            <foreach collection="countryList" item="country" open="AND oc.country in (" close=")" separator=",">
                #{country}
            </foreach>
        </if>
    </select>

    <select id="pageQuery"
            parameterType="com.imile.attendance.infrastructure.repository.rule.query.OverTimeConfigPageQuery"
            resultType="com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO">
       select
       <include refid="Base_Column_List"/>
       from over_time_config
       where is_delete = 0 and is_latest = 1
       <if test="configName!=null and configName!=''">
           and config_name = #{configName}
       </if>
       <if test="status!=null and status!=''">
           and status = #{status}
       </if>
       <if test="configIds!=null and configIds.size()>0">
           <foreach collection="configIds" item="configId" open="and id in (" close=")" separator=",">
               #{configId}
           </foreach>
       </if>

        <if test="countryList!=null and countryList.size()>0">
            <foreach collection="countryList" item="country" open="and country in (" close=")"
                     separator=",">
                #{country}
            </foreach>
        </if>

        <if test="deptIds != null and deptIds.size() > 0">
            AND (
            <foreach collection="deptIds" item="deptId" separator=" OR ">
                FIND_IN_SET(#{deptId}, dept_ids)
            </foreach>
            )
        </if>
        order by create_date desc
    </select>
</mapper>
