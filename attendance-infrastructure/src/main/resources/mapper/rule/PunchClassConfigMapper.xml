<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.rule.mapper.PunchClassConfigMapper">

    <select id="pageQuery"
            parameterType="com.imile.attendance.infrastructure.repository.rule.query.PunchClassConfigQuery"
            resultType="com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO">
        SELECT
        pcc.id,pcc.status,pcc.class_name,pcc.config_no,pcc.class_nature,pcc.country,pcc.legal_working_hours, pcc.attendance_hours,
        pcc.dept_ids,pcc.create_date,pcc.create_user_name,pcc.last_upd_date,pcc.last_upd_user_name,pcc.is_from_hr_history_config
        FROM punch_class_config pcc
        <where>
            pcc.is_delete = 0 AND pcc.is_latest = 1 and pcc.is_from_hr_history_config = 0
            <if test="classNature != null and classNature!=''">
                AND pcc.class_nature = #{classNature}
            </if>

            <if test="status != null and status !=''">
                AND pcc.status = #{status}
            </if>

            <if test="className != null and className !=''">
                AND pcc.class_name  LIKE CONCAT('%', #{className}, '%')
            </if>

            <if test="ids != null and ids.size() > 0">
                AND pcc.id IN
                <foreach item="id" collection="ids" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>

            <include refid="permissionCountryAndDeptConditions"/>
        </where>
        order by pcc.create_date desc
    </select>


    <select id="export"
            parameterType="com.imile.attendance.infrastructure.repository.rule.query.PunchClassConfigQuery"
            resultType="com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigExportDTO">
        SELECT
        pcc.id,pcc.status,pcc.class_name,pcc.class_type,pcc.country,pcc.legal_working_hours as totalLegalWorkingHours,
        pcc.attendance_hours as totalAttendanceHours,pcc.dept_ids,pcc.create_date,pcc.create_user_name,pcc.last_upd_date,pcc.last_upd_user_name,
        pcic.id as itemClassId,pcic.sort_no, pcic.punch_in_time,pcic.punch_out_time,pcic.earliest_punch_in_time,pcic.latest_punch_in_time,
        pcic.latest_punch_out_time,pcic.elastic_time,pcic.rest_start_time,pcic.rest_end_time,pcic.legal_working_hours,pcic.attendance_hours
        FROM punch_class_config pcc
        LEFT JOIN punch_class_item_config pcic on pcc.id = pcic.punch_class_id
        <where>
            pcc.is_delete = 0 AND  pcic.is_delete = 0 AND pcc.is_latest = 1 and pcc.is_from_hr_history_config = 0
            <if test="classNature != null and classNature!=''">
                AND pcc.class_nature = #{classNature}
            </if>

            <if test="status != null and status !=''">
                AND pcc.status = #{status}
            </if>

            <if test="className != null and className !=''">
                AND pcc.class_name = #{className}
            </if>

            <if test="ids != null and ids.size() > 0">
                AND pcc.id IN
                <foreach item="id" collection="ids" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>

            <include refid="permissionCountryAndDeptConditions"/>
        </where>
        order by pcc.create_date desc
    </select>

    <!-- 权限和部门条件片段 -->
    <sql id="permissionCountryAndDeptConditions">
        <if test="countryList!=null and countryList.size()>0">
            <foreach collection="countryList" item="country" open="and pcc.country in (" close=")"
                     separator=",">
                #{country}
            </foreach>
        </if>

        <if test="deptIds != null and deptIds.size() > 0">
            AND (
            <foreach collection="deptIds" item="deptId" separator=" OR ">
                FIND_IN_SET(#{deptId}, pcc.dept_ids)
            </foreach>
            )
        </if>
    </sql>

</mapper>
