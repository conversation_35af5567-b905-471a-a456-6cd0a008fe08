<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.shift.mapper.UserShiftConfigMapper">
    <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="user_id" jdbcType="BIGINT" property="userId" />
        <result column="attendance_config_id" jdbcType="BIGINT" property="attendanceConfigId" />
        <result column="punch_class_config_id" jdbcType="BIGINT" property="punchClassConfigId" />
        <result column="class_time" jdbcType="TIMESTAMP" property="classTime" />
        <result column="day_id" jdbcType="BIGINT" property="dayId" />
        <result column="day_shift_rule" jdbcType="VARCHAR" property="dayShiftRule" />
        <result column="shift_type" jdbcType="VARCHAR" property="shiftType" />
        <result column="data_source" jdbcType="VARCHAR" property="dataSource" />
        <result column="task_flag" jdbcType="VARCHAR" property="taskFlag" />
        <result column="is_latest" jdbcType="TINYINT" property="isLatest" />
        <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
        <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id, attendance_config_id, punch_class_config_id, class_time, day_id, day_shift_rule, shift_type,
    data_source, task_flag, is_latest, is_delete, record_version, create_date, create_user_code,
    create_user_name, last_upd_date, last_upd_user_code, last_upd_user_name
    </sql>

    <select id="page" resultType="com.imile.attendance.infrastructure.repository.shift.dto.UserShiftConfigDTO"
    parameterType="com.imile.attendance.infrastructure.repository.shift.query.UserShiftConfigQuery">

        select
        ui.id as id,
        ui.id as userId,
        ui.user_code as userCode,
        ui.user_name as userName,
        ui.dept_id as deptId,
        ui.post_id as postId,
        ui.location_country as country,
        ui.employee_type as employeeType,
        ui.work_status as workStatus,
        ui.location_country as locationCountry,
        ui.location_province as locationProvince,
        ui.location_city as locationCity,
        ui.is_driver as isDriver,
        ui.is_global_relocation as isGlobalRelocation,
        ui.class_nature as classNature
        from
        user_info ui
        <if test="attendanceConfigNo!=null and attendanceConfigNo!=''">
            inner join calendar_config_range as acr on acr.biz_id = ui.id and acr.is_delete=0 and acr.is_latest=1
            inner join calendar_config as ac on ac.id = acr.attendance_config_id and ac.is_delete=0 and ac.is_latest=1
            and ac.attendance_config_no = #{attendanceConfigNo}
        </if>
        where ui.is_delete = 0 and ui.status = 'ACTIVE' and ui.work_status = 'ON_JOB' and ui.is_driver = 0
        <if test="userCodeOrName != null and userCodeOrName != ''">
            and (ui.user_code like concat('%',#{userCodeOrName},'%') or
            ui.user_name like concat('%',#{userCodeOrName},'%') or
            ui.user_name_en like concat('%',#{userCodeOrName},'%'))
        </if>
        <if test="userCode != null and userCode != ''">
            and ui.user_code = #{userCode}
        </if>
        <if test="classNature != null and classNature != ''">
            and ui.class_nature = #{classNature}
        </if>
        <if test="postId != null and postId != ''">
            and ui.post_id = #{postId}
        </if>
        <if test="postIdList != null and postIdList.size() > 0">
            <foreach collection="postIdList" separator="," open="and ui.post_id in (" close=")" item="postId">
                #{postId}
            </foreach>
        </if>
        <if test="country != null and country != ''">
            AND ui.location_country = #{country}
        </if>
        <if test="deptIds != null and deptIds.size() > 0">
            and ui.dept_id in
            <foreach collection="deptIds" item="deptId" separator="," open="(" close=")">
                #{deptId}
            </foreach>
        </if>

        <if test="conditionUserIds != null and conditionUserIds.size() > 0">
            <choose>
                <!-- 当ID数量较少时使用普通IN查询 -->
                <when test="conditionUserIds.size() &lt;= 1000">
                    <foreach collection="conditionUserIds" separator="," open="and ui.id in (" close=")" item="conditionUserId">
                        #{conditionUserId}
                    </foreach>
                </when>
                <!-- 当ID数量较多时分批处理 -->
                <otherwise>
                    and (
                    <foreach collection="batchedConditionUserIds" separator=" or " item="batch">
                        ui.id in
                        <foreach collection="batch" open="(" separator="," close=")" item="conditionUserId">
                            #{conditionUserId}
                        </foreach>
                    </foreach>
                    )
                </otherwise>
            </choose>
        </if>

        <include refid="permissionAndNormalCountryConditions"/>

        <if test="isNeedQuerySpecialCountry!=null and isNeedQuerySpecialCountry == true">
        union

            select
            ui.id as id,
            ui.id as userId,
            ui.user_code as userCode,
            ui.user_name as userName,
            ui.dept_id as deptId,
            ui.post_id as postId,
            ui.location_country as country,
            ui.employee_type as employeeType,
            ui.work_status as workStatus,
            ui.location_country as locationCountry,
            ui.location_province as locationProvince,
            ui.location_city as locationCity,
            ui.is_driver as isDriver,
            ui.is_global_relocation as isGlobalRelocation,
            ui.class_nature as classNature
            from
            user_info ui
            <if test="attendanceConfigNo!=null and attendanceConfigNo!=''">
                inner join calendar_config_range as acr on acr.biz_id = ui.id and acr.is_delete=0 and acr.is_latest=1
                inner join calendar_config as ac on ac.id = acr.attendance_config_id and ac.is_delete=0 and ac.is_latest=1
                and ac.attendance_config_no = #{attendanceConfigNo}
            </if>
            where ui.is_delete = 0 and ui.status = 'ACTIVE' and ui.work_status = 'ON_JOB' and ui.is_driver = 0
            <if test="userCodeOrName != null and userCodeOrName != ''">
                and (ui.user_code like concat('%',#{userCodeOrName},'%') or
                ui.user_name like concat('%',#{userCodeOrName},'%') or
                ui.user_name_en like concat('%',#{userCodeOrName},'%'))
            </if>
            <if test="userCode != null and userCode != ''">
                and ui.user_code = #{userCode}
            </if>
            <if test="classNature != null and classNature != ''">
                and ui.class_nature = #{classNature}
            </if>
            <if test="postId != null and postId != ''">
                and ui.post_id = #{postId}
            </if>
            <if test="postIdList != null and postIdList.size() > 0">
                <foreach collection="postIdList" separator="," open="and ui.post_id in (" close=")" item="postId">
                    #{postId}
                </foreach>
            </if>

            <if test="country != null and country != ''">
                AND ui.location_country = #{country}
            </if>

            <if test="deptIds != null and deptIds.size() > 0">
                and ui.dept_id in
                <foreach collection="deptIds" item="deptId" separator="," open="(" close=")">
                    #{deptId}
                </foreach>
            </if>

            <if test="conditionUserIds != null and conditionUserIds.size() > 0">
                <choose>
                    <!-- 当ID数量较少时使用普通IN查询 -->
                    <when test="conditionUserIds.size() &lt;= 1000">
                        <foreach collection="conditionUserIds" separator="," open="and ui.id in (" close=")" item="conditionUserId">
                            #{conditionUserId}
                        </foreach>
                    </when>
                    <!-- 当ID数量较多时分批处理 -->
                    <otherwise>
                        and (
                        <foreach collection="batchedConditionUserIds" separator=" or " item="batch">
                            ui.id in
                            <foreach collection="batch" open="(" separator="," close=")" item="conditionUserId">
                                #{conditionUserId}
                            </foreach>
                        </foreach>
                        )
                    </otherwise>
                </choose>
            </if>
            <include refid="permissionAndSpecialCountryConditions"/>
        </if>


    </select>


    <!-- 权限和用工类型条件片段 -->
    <sql id="permissionAndNormalCountryConditions">
        <if test="normalDeptList!=null and normalDeptList.size()>0">
            and (ui.dept_id in
            <foreach collection="normalDeptList" item="deptId" index="i" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
            <if test="normalCountryList!=null and normalCountryList.size()>0">
                or ui.location_country in
                <foreach collection="normalCountryList" item="locationCountry" index="i" open="(" close=")"
                         separator=",">
                    #{locationCountry}
                </foreach>
            </if>
            )
        </if>

        <if test="normalDeptList==null or normalDeptList.size()==0">
            <if test="normalCountryList!=null and normalCountryList.size()>0">
                and ui.location_country in
                <foreach collection="normalCountryList" item="locationCountry" index="i" open="(" close=")"
                         separator=",">
                    #{locationCountry}
                </foreach>
            </if>
        </if>

        <if test="normalEmployeeTypeList != null and normalEmployeeTypeList.size() > 0">
            <foreach collection="normalEmployeeTypeList" separator="," open="and ui.employee_type in (" close=")"
                     item="normalEmployeeType">
                #{normalEmployeeType}
            </foreach>
        </if>
    </sql>

    <sql id="permissionAndSpecialCountryConditions">
        <if test="specialDeptList!=null and specialDeptList.size()>0">
            and (ui.dept_id in
            <foreach collection="specialDeptList" item="deptId" index="i" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
            <if test="specialCountryList!=null and specialCountryList.size()>0">
                or ui.location_country in
                <foreach collection="specialCountryList" item="locationCountry" index="i" open="(" close=")"
                         separator=",">
                    #{locationCountry}
                </foreach>
            </if>
            )
        </if>

        <if test="specialDeptList==null or specialDeptList.size()==0">
            <if test="specialCountryList!=null and specialCountryList.size()>0">
                and ui.location_country in
                <foreach collection="specialCountryList" item="locationCountry" index="i" open="(" close=")"
                         separator=",">
                    #{locationCountry}
                </foreach>
            </if>
        </if>

        <if test="specialEmployeeTypeList != null and specialEmployeeTypeList.size() > 0">
            <foreach collection="specialEmployeeTypeList" separator="," open="and ui.employee_type in (" close=")"
                     item="specialEmployeeType">
                #{specialEmployeeType}
            </foreach>
        </if>
    </sql>


</mapper>
