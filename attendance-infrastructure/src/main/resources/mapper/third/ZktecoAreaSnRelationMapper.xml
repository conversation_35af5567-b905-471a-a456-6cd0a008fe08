<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.third.mapper.ZktecoAreaSnRelationMapper">
    <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.third.model.ZktecoAreaSnRelationDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="country" jdbcType="VARCHAR" property="country"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="zkteco_area_id" jdbcType="INTEGER" property="zktecoAreaId"/>
        <result column="zkteco_area_name" jdbcType="VARCHAR" property="zktecoAreaName"/>
        <result column="zkteco_area_code" jdbcType="VARCHAR" property="zktecoAreaCode"/>
        <result column="terminal_sn" jdbcType="VARCHAR" property="terminalSn"/>
        <result column="dept_ids" jdbcType="VARCHAR" property="deptIds"/>
        <result column="user_ids" jdbcType="VARCHAR" property="userIds"/>
        <result column="is_latest" jdbcType="TINYINT" property="isLatest"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="record_version" jdbcType="VARCHAR" property="recordVersion"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate"/>
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode"/>
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, country, company_id, zkteco_area_id, zkteco_area_name, zkteco_area_code, terminal_sn, dept_ids,
        user_ids, is_latest, remark, is_delete, record_version, create_date, create_user_code,
        create_user_name, last_upd_date, last_upd_user_code, last_upd_user_name
    </sql>
</mapper>
