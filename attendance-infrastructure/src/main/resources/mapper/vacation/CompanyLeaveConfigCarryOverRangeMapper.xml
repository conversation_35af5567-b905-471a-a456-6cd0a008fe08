<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.vacation.mapper.CompanyLeaveConfigCarryOverRangeMapper">
  <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverRangeDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
    <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
    <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
    <result column="carry_over_id" jdbcType="BIGINT" property="carryOverId" />
    <result column="symbol_left" jdbcType="TINYINT" property="symbolLeft" />
    <result column="entry_date_left" jdbcType="INTEGER" property="entryDateLeft" />
    <result column="symbol_right" jdbcType="TINYINT" property="symbolRight" />
    <result column="entry_date_right" jdbcType="INTEGER" property="entryDateRight" />
    <result column="invalid_year" jdbcType="TINYINT" property="invalidYear" />
    <result column="invalid_date" jdbcType="INTEGER" property="invalidDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, is_delete, record_version, create_date, create_user_code, create_user_name, last_upd_date, 
    last_upd_user_code, last_upd_user_name, carry_over_id, symbol_left, entry_date_left,
    symbol_right,entry_date_right, invalid_year, invalid_date
  </sql>
</mapper>