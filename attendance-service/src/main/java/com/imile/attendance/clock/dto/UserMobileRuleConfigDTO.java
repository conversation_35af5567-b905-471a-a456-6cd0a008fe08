package com.imile.attendance.clock.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/19 
 * @Description 员工手机打卡规则配置
 */
@Data
public class UserMobileRuleConfigDTO {

    //============打卡规则===============

    /**
     * 打卡规则编码
     */
    private String punchConfigNo;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;

    /**
     * 打卡规则类型 PunchConfigTypeEnum
     */
    private String punchConfigType;

    /**
     * 打卡规则上下班时间间隔
     */
    private BigDecimal punchTimeInterval;

    //============补卡规则===============

    /**
     * 补卡次数
     */
    private Integer maxRepunchNumber;


    //============班次规则===============

    private List<PunchClassRule> punchClassRuleList;


    @Data
    public static class PunchClassRule {
        /**
         * 班次规则编码
         */
        private String configNo;

        /**
         * 班次名称
         */
        private String className;
    }
}
