package com.imile.attendance;

import cn.hutool.core.net.NetUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR> chen
 * @Date 2025/1/15 
 * @Description
 */
@Slf4j
@EnableCaching
@EnableAsync
@SpringBootApplication(scanBasePackages = {"com.imile"})
@EnableDubbo
@EnableAspectJAutoProxy(exposeProxy = true)
@MapperScan(annotationClass = Mapper.class, basePackages = {"com.imile.attendance.infrastructure.repository"})
public class AttendanceApplication {

    public static void main(String[] args) {
        System.setProperty("local.ip", NetUtil.getLocalhostStr());
        System.setProperty("log4j2.isThreadContextMapInheritable", "true");
        SpringApplication.run(AttendanceApplication.class, args);
    }
}
