package com.imile.attendance.controller.abnormal;

import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceService;
import com.imile.attendance.abnormal.param.AbnormalAttendanceReminderParam;
import com.imile.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 考勤相关-企微功能入口
 *
 * <AUTHOR>
 * @since 2025/6/4
 */
@Slf4j
@RequestMapping("/attendance/wechat")
@RestController
public class EmployeeAttendanceWeChatController {
    @Resource
    private EmployeeAbnormalAttendanceService abnormalAttendanceService;

    /**
     * 考勤异常员工-发送企业微信提醒
     *
     * @return Result<Boolean>
     */
    @PostMapping("sendAbnormalAttendanceReminder")
    public Result<Boolean> sendAbnormalAttendanceReminder(@RequestBody List<AbnormalAttendanceReminderParam> param) {
        abnormalAttendanceService.sendAbnormalAttendanceReminder(param);
        return Result.ok(Boolean.TRUE);
    }
}
