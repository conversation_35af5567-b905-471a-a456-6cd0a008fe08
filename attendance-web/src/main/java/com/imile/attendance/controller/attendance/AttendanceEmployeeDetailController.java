package com.imile.attendance.controller.attendance;

import com.imile.attendance.annon.NoAuthRequired;
import com.imile.attendance.annon.NoLoginAuthRequired;
import com.imile.attendance.attendance.EmployeeDetailService;
import com.imile.attendance.attendance.converter.AttendanceClassDetailConverter;
import com.imile.attendance.attendance.dto.AttendanceClassCalendarDTO;
import com.imile.attendance.attendance.dto.AttendanceClassDetailDTO;
import com.imile.attendance.attendance.query.AttendanceEmployeeDetailClassQuery;
import com.imile.attendance.attendance.vo.AttendanceClassCalendarVO;
import com.imile.attendance.attendance.vo.AttendanceClassDetailVO;
import com.imile.attendance.controller.BaseController;
import com.imile.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/6
 */
@Slf4j
@RequestMapping("/attendance/employee")
@RestController
public class AttendanceEmployeeDetailController extends BaseController {

    @Resource
    private EmployeeDetailService employeeDetailService;

    /**
     * 员工日历排班明细
     */
    @NoLoginAuthRequired
    @NoAuthRequired
    @PostMapping("class/detail")
    public Result<List<AttendanceClassDetailVO>> classDetail(@RequestBody AttendanceEmployeeDetailClassQuery query) {
        List<AttendanceClassDetailDTO> attendanceClassDetailList = employeeDetailService.detailUserClassDTO(query);
        return Result.ok(AttendanceClassDetailConverter.INSTANCE.mapToAttendanceClassDetailVOList(attendanceClassDetailList));
    }

    /**
     * 员工未排班日历查询
     */
    @NoLoginAuthRequired
    @NoAuthRequired
    @GetMapping("class/calendar")
    public Result<List<AttendanceClassCalendarVO>> classCalendar(@RequestParam Integer year, Long userId) {
        List<AttendanceClassCalendarDTO> attendanceClassCalendarList = employeeDetailService.detailUserClassCalendar(year, userId);
        return Result.ok(AttendanceClassDetailConverter.INSTANCE.mapToAttendanceClassCalendarVOList(attendanceClassCalendarList));
    }
}
