package com.imile.attendance.controller.calendar;

import com.imile.attendance.calendar.application.CalendarConfigApplicationService;
import com.imile.attendance.calendar.command.CalendarAddCommand;
import com.imile.attendance.calendar.command.CalendarStatusSwitchCommand;
import com.imile.attendance.calendar.command.CalendarUpdateCommand;
import com.imile.attendance.calendar.dto.CalendarConfigDTO;
import com.imile.attendance.calendar.dto.CalendarConfigDetailDTO;
import com.imile.attendance.calendar.dto.CalendarConfigRangeDTO;
import com.imile.attendance.calendar.dto.CalendarConfigSelectDTO;
import com.imile.attendance.calendar.dto.CalendarDayConfigDTO;
import com.imile.attendance.calendar.dto.CalendarTypeDTO;
import com.imile.attendance.calendar.param.CalendarApiParam;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.AttendanceTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.convert.ConverterService;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigDetailQuery;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigQuery;
import com.imile.attendance.migration.MigrationService;
import com.imile.common.component.repeat.DuplicateSubmit;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.enums.StatusEnum;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.common.validator.Groups;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description 考勤日历设置
 */
@RequestMapping("attendance/config")
@RestController
public class CalendarConfigController {

    @Resource
    private ConverterService converterService;
    @Resource
    private CalendarConfigApplicationService calendarConfigApplicationService;
    @Resource
    private MigrationService migrationService;

    /**
     * 考勤日历方案列表查询
     */
    @PostMapping("/list")
    public Result<PaginationResult<CalendarConfigDTO>> list(@RequestBody CalendarConfigQuery query) {
        PaginationResult<CalendarConfigDTO> paginationResult = calendarConfigApplicationService.list(query);
        // 返回时区处理
        converterService.withAnnotation(paginationResult.getResults());
        return Result.ok(paginationResult);
    }

    /**
     * 考勤日历方案详情
     */
    @PostMapping("/detail")
    public Result<CalendarConfigDetailDTO> detail(@Validated @RequestBody CalendarConfigDetailQuery configDetailQuery) {
        CalendarConfigDetailDTO calendarConfigDetailDTO = calendarConfigApplicationService.detail(configDetailQuery);
        return Result.ok(calendarConfigDetailDTO);
    }

    /**
     * 添加考勤方案
     */
    @DuplicateSubmit
    @PostMapping("/add")
    public Result<List<CalendarConfigRangeDTO>> add(@Validated(Groups.Add.class) @RequestBody CalendarAddCommand calendarAddCommand) {
        Set<String> backupOperatorUserCodes = migrationService.getBackupOperatorUserCodes();
        if (CollectionUtils.isEmpty(backupOperatorUserCodes) ||
                !backupOperatorUserCodes.contains(RequestInfoHolder.getUserCode())) {
            List<String> allGrayScaleSwitchedCountries = migrationService.getAllGrayScaleSwitchedCountry();
            if (!allGrayScaleSwitchedCountries.contains(calendarAddCommand.getCountry())) {
                throw BusinessLogicException.getException(ErrorCodeEnum.SYSTEM_SWITCHING_CONTACT_ADMIN);
            }
        }
        // 参数校验
        AttendanceTypeEnum attendanceTypeEnum = AttendanceTypeEnum.valueOf(calendarAddCommand.getType());
        if (AttendanceTypeEnum.DEFAULT.equals(attendanceTypeEnum)) {
            calendarConfigApplicationService.addDefault(calendarAddCommand);
            return Result.ok(new ArrayList<>());
        }
        // 自定义日历需要校验日历名称不能为空
        BusinessLogicException.checkTrue(StringUtils.isBlank(calendarAddCommand.getAttendanceConfigName()),
                MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "attendanceConfigName");
        List<CalendarConfigRangeDTO> calendarConfigRangeDTOList = calendarConfigApplicationService.addCustom(calendarAddCommand);
        if (CollectionUtils.isNotEmpty(calendarConfigRangeDTOList)) {
            return Result.ok(calendarConfigRangeDTOList);
        }
        return Result.ok(Collections.emptyList());
    }

    /**
     * 编辑考勤方案
     */
    @PostMapping("/update")
    public Result<List<CalendarConfigRangeDTO>> update(@Validated(Groups.Update.class) @RequestBody CalendarUpdateCommand calendarUpdateCommand) {
        Set<String> backupOperatorUserCodes = migrationService.getBackupOperatorUserCodes();
        if (CollectionUtils.isEmpty(backupOperatorUserCodes) ||
                !backupOperatorUserCodes.contains(RequestInfoHolder.getUserCode())) {
            List<String> allGrayScaleSwitchedCountries = migrationService.getAllGrayScaleSwitchedCountry();
            if (!allGrayScaleSwitchedCountries.contains(calendarUpdateCommand.getCountry())) {
                throw BusinessLogicException.getException(ErrorCodeEnum.SYSTEM_SWITCHING_CONTACT_ADMIN);
            }
        }
        List<CalendarConfigRangeDTO> calendarConfigRangeDTOList =
                calendarConfigApplicationService.update(calendarUpdateCommand);
        if (CollectionUtils.isNotEmpty(calendarConfigRangeDTOList)) {
            return Result.ok(calendarConfigRangeDTOList);
        }
        try {
            // 睡眠300毫秒返回 避免事务未提交
            Thread.sleep(300L);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        return Result.ok(Collections.emptyList());
    }

    /**
     * 停启用状态修改
     */
    @PostMapping("/status/switch")
    public Result<List<CalendarConfigRangeDTO>> statusSwitch(@Validated(Groups.Update.class) @RequestBody CalendarStatusSwitchCommand statusSwitchParamDTO) {
        StatusEnum statusEnum = StatusEnum.getStatusEnum(statusSwitchParamDTO.getStatus());
        if (statusEnum == null) {
            return Result.fail(MsgCodeConstant.PARAM_INVALID, "status");
        }
        List<CalendarConfigRangeDTO> calendarConfigRangeDTOList = calendarConfigApplicationService.statusSwitch(statusSwitchParamDTO);
        if (CollectionUtils.isNotEmpty(calendarConfigRangeDTOList)) {
            return Result.ok(calendarConfigRangeDTOList);
        }
        return Result.ok(Collections.emptyList());
    }

    /**
     * 考勤日历名称下拉框
     */
    @PostMapping("/select/list")
    public Result<List<CalendarConfigSelectDTO>> selectList(@RequestParam String country) {
        List<CalendarConfigSelectDTO> list = calendarConfigApplicationService.selectList(country);
        return Result.ok(list);
    }

    @PostMapping("/getCalendarByCondition")
    public Result<CalendarDayConfigDTO> getCalendarByCondition(@RequestBody CalendarApiParam param) {
        return Result.ok(calendarConfigApplicationService.getCalendarByCondition(param));
    }

    /**
     * 根据国家获取日历类型
     */
    @GetMapping("/getCalendarTypeByCountry")
    public Result<CalendarTypeDTO> getCalendarTypeByCountry(String country){
        return Result.ok(calendarConfigApplicationService.getCalendarTypeByCountry(country));
    }
}
