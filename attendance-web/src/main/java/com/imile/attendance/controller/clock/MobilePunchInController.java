package com.imile.attendance.controller.clock;

import com.imile.attendance.annon.NoAuthRequired;
import com.imile.attendance.annon.NoLoginAuthRequired;
import com.imile.attendance.clock.application.MobilePunchApplicationService;
import com.imile.attendance.clock.command.PunchInEncryptAddCommand;
import com.imile.attendance.clock.dto.UserDayMobilePunchDetailDTO;
import com.imile.attendance.clock.query.MobileDayPunchDetailQuery;
import com.imile.attendance.clock.query.MobilePunchDetailQuery;
import com.imile.attendance.clock.vo.UserDayMobilePunchDetailVO;
import com.imile.attendance.deviceConfig.application.AttendanceGpsConfigApplicationService;
import com.imile.attendance.deviceConfig.application.AttendanceWifiConfigApplicationService;
import com.imile.attendance.deviceConfig.dto.AttendanceGpsConfigDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceWifiConfigDTO;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceGpsConfigQuery;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceWifiConfigQuery;
import com.imile.common.component.repeat.DuplicateSubmit;
import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.result.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/21
 * @Description 移动打卡
 */
@RestController
@RequestMapping("/mobile/punch")
public class MobilePunchInController {

    @Resource
    private MobilePunchApplicationService mobilePunchApplicationService;

    /**
     * 通过经纬度获取时间
     */
    @NoLoginAuthRequired
    @NoAuthRequired
    @GetMapping("/timezone")
    public Result<Date> getTimeZone(@NotBlank(message = ValidCodeConstant.NOT_BLANK) String lat,
                                    @NotBlank(message = ValidCodeConstant.NOT_BLANK) String lng) {
        return Result.ok(mobilePunchApplicationService.getTimeZone(lat, lng));
    }

    /**
     * 移动打卡
     */
    @DuplicateSubmit
    @NoLoginAuthRequired
    @NoAuthRequired
    @PostMapping("/punchIn")
    public Result<Void> punchIn(@RequestBody @Validated PunchInEncryptAddCommand punchInEncryptAddCommand){
        mobilePunchApplicationService.punchIn(punchInEncryptAddCommand);
        return Result.ok();
    }

    /**
     * 个人考勤详情
     */
    @PostMapping("/detail")
    @NoLoginAuthRequired
    @NoAuthRequired
    public Result<UserDayMobilePunchDetailVO> userDetail(@RequestBody @Validated MobilePunchDetailQuery query) {
        return Result.ok(mobilePunchApplicationService.userDetail(query));
    }

    /**
     * 个人考勤统计详情
     */
    @PostMapping("/statistic/detail")
    @NoLoginAuthRequired
    @NoAuthRequired
    public Result<UserDayMobilePunchDetailVO> userDetailByDayId(@RequestBody @Validated MobileDayPunchDetailQuery query){
        return Result.ok(mobilePunchApplicationService.userDetailByDayId(query));
    }
}
