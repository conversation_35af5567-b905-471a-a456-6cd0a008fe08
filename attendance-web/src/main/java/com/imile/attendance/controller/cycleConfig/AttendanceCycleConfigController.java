package com.imile.attendance.controller.cycleConfig;

import com.imile.attendance.cycleConfig.application.AttendanceCycleConfigApplicationService;
import com.imile.attendance.cycleConfig.command.AttendanceCycleConfigAddCommand;
import com.imile.attendance.cycleConfig.command.AttendanceCycleConfigStatusSwitchCommand;
import com.imile.attendance.cycleConfig.command.AttendanceCycleConfigUpdateCommand;
import com.imile.attendance.cycleConfig.dto.AttendanceCycleConfigDetailDTO;
import com.imile.attendance.cycleConfig.dto.AttendanceCycleConfigPageDTO;
import com.imile.attendance.cycleConfig.query.AttendanceCycleConfigDetailQuery;
import com.imile.attendance.cycleConfig.query.AttendanceCycleConfigListQuery;
import com.imile.attendance.cycleConfig.vo.CycleDetailSelectVO;
import com.imile.common.component.repeat.DuplicateSubmit;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.enums.StatusEnum;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description 考勤周期配置表控制层
 */
@RestController
@RequestMapping("/attendance/cycle/config")
public class AttendanceCycleConfigController {

    @Resource
    private AttendanceCycleConfigApplicationService cycleConfigApplicationService;


    /**
     * 获取周期类型下拉数据
     *
     * @param cycleType 考勤周期类型 MONTH/WEEK
     * @return 考勤周期详情列表
     */
    @GetMapping("/getCycleDetailList")
    public Result<List<CycleDetailSelectVO>> getCycleDetailList(String cycleType){
        return Result.ok(cycleConfigApplicationService.getCycleDetailList(cycleType));
    }

    /**
     * 新增考勤周期方案
     */
    @DuplicateSubmit
    @PostMapping("add")
    public Result<Boolean> add(@RequestBody @Valid AttendanceCycleConfigAddCommand addCommand) {
        cycleConfigApplicationService.add(addCommand);
        return Result.ok(true);
    }

    /**
     * 修改考勤周期方案
     */
    @DuplicateSubmit
    @PostMapping("update")
    public Result<Boolean> update(@RequestBody @Valid AttendanceCycleConfigUpdateCommand updateCommand) {
        cycleConfigApplicationService.update(updateCommand);
        return Result.ok(true);
    }

    /**
     * 更新考勤周期方案状态
     */
    @DuplicateSubmit
    @PostMapping("/status/switch")
    public Result<Boolean> statusSwitch(@RequestBody @Valid AttendanceCycleConfigStatusSwitchCommand statusSwitchCommand) {
        StatusEnum statusEnum = StatusEnum.getStatusEnum(statusSwitchCommand.getStatus());
        if (statusEnum == null) {
            return Result.fail(MsgCodeConstant.PARAM_INVALID, "status");
        }
        cycleConfigApplicationService.statusSwitch(statusSwitchCommand);
        return Result.ok(true);
    }

    /**
     * 查看考勤周期方案详情
     */
    @PostMapping("/detail")
    public Result<AttendanceCycleConfigDetailDTO> detail(@RequestBody @Valid AttendanceCycleConfigDetailQuery detailQuery) {
        return Result.ok(cycleConfigApplicationService.detail(detailQuery));
    }

    /**
     * 查询考勤周期方案
     */
    @PostMapping("list")
    public Result<PaginationResult<AttendanceCycleConfigPageDTO>> query(@RequestBody AttendanceCycleConfigListQuery listQuery) {
        return Result.ok(cycleConfigApplicationService.list(listQuery));
    }
}
