package com.imile.attendance.controller.deviceConfig;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.annon.NoAuthRequired;
import com.imile.attendance.annon.NoLoginAuthRequired;
import com.imile.attendance.controller.BaseController;
import com.imile.attendance.deviceConfig.application.AttendanceGpsConfigApplicationService;
import com.imile.attendance.deviceConfig.command.AttendanceGpsConfigAddCommand;
import com.imile.attendance.deviceConfig.command.AttendanceGpsConfigDeleteCommand;
import com.imile.attendance.deviceConfig.command.AttendanceGpsConfigUpdateCommand;
import com.imile.attendance.deviceConfig.dto.AttendanceGpsConfigDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceGpsConfigExportDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceGpsConfigImportDTO;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceConfigFilterQuery;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceGpsConfigQuery;
import com.imile.attendance.infrastructure.repository.vacation.query.CompanyLeaveTypeQuery;
import com.imile.attendance.vacation.dto.CompanyLeaveTypeDTO;
import com.imile.attendance.vacation.mapstruct.CompanyLeaveConfigTypeMapstruct;
import com.imile.attendance.vacation.vo.CompanyLeaveTypeVO;
import com.imile.common.component.repeat.DuplicateSubmit;
import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.excel.ExcelCallBackParam;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.common.validator.Groups;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;

/**
 * H5/移动 考勤打卡GPS配置相关接口
 *
 * <AUTHOR>
 * @menu GPS打卡地址配置
 * @date 2025/4/15
 */
@RestController
@RequestMapping("/attendance/gps")
public class AttendanceGpsConfigController extends BaseController {

    @Resource
    private AttendanceGpsConfigApplicationService gpsConfigApplicationService;

    /**
     * 添加gps配置
     *
     * @return
     */
    @DuplicateSubmit
    @PostMapping("/add")
    public Result<Boolean> add(@Validated(Groups.Add.class) @RequestBody AttendanceGpsConfigAddCommand addCommand) {
        gpsConfigApplicationService.add(addCommand);
        return Result.ok(true);
    }

    /**
     * 更新gps配置
     *
     * @return
     */
    @DuplicateSubmit
    @PostMapping("/update")
    public Result<Boolean> update(@Validated(Groups.Update.class) @RequestBody AttendanceGpsConfigUpdateCommand updateCommand) {
        gpsConfigApplicationService.update(updateCommand);
        return Result.ok(true);
    }

    /**
     * gps配置列表查询
     *
     * @return
     */
    @NoAuthRequired
    @PostMapping("/list")
    public Result<PaginationResult<AttendanceGpsConfigDTO>> list(@RequestBody AttendanceGpsConfigQuery query) {
        PaginationResult<AttendanceGpsConfigDTO> list = gpsConfigApplicationService.list(query);
        return Result.ok(list);
    }

    /**
     * 删除gps配置
     *
     * @return
     */
    @DuplicateSubmit
    @GetMapping("/delete")
    public Result<Boolean> delete(@NotNull(message = ValidCodeConstant.NOT_NULL) @RequestParam Long gpsConfigId) {
        gpsConfigApplicationService.delete(AttendanceGpsConfigDeleteCommand.of(gpsConfigId));
        return Result.ok(true);
    }

    /**
     * 导入gps配置
     *
     * @return
     */
    @DuplicateSubmit
    @PostMapping("/import")
    public Result<List<AttendanceGpsConfigImportDTO>> importGpsConfig(HttpServletRequest request) {
        ExcelCallBackParam callBackParam = new ExcelCallBackParam(request);
        List<AttendanceGpsConfigImportDTO> importList = JSON.parseArray(callBackParam.getPageData(), AttendanceGpsConfigImportDTO.class);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(importList)) {
            return Result.ok(Collections.emptyList());
        }
        List<AttendanceGpsConfigImportDTO> failImportList = gpsConfigApplicationService.importGpsConfig(importList);
        return Result.ok(failImportList);
    }

    /**
     * 导出gps配置
     *
     * @return
     */
    @PostMapping("/export")
    public Result<PaginationResult<AttendanceGpsConfigExportDTO>> export(HttpServletRequest request, AttendanceGpsConfigQuery query) {
        setExcelCallBackParam(request, query);
        PaginationResult<AttendanceGpsConfigExportDTO> result = gpsConfigApplicationService.export(query);
        return Result.ok(result);
    }

    /**
     * gps国家/城市条件查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    @PostMapping("/selectFilterList")
    public Result<List<String>> selectFilterList(@RequestBody @Validated AttendanceConfigFilterQuery query) {
        return Result.ok(gpsConfigApplicationService.selectFilterList(query));
    }

    /**
     * 获取所有GPS配置
     * 用于移动端打卡时获取可用的GPS配置列表
     */
    @PostMapping("/all/config")
    @NoLoginAuthRequired
    @NoAuthRequired
    public Result<List<AttendanceGpsConfigDTO>> getAllGpsConfig() {
        return Result.ok(gpsConfigApplicationService.getAllGpsConfig());
    }


}
