package com.imile.attendance.controller.deviceConfig;

import com.imile.attendance.annon.NoAuthRequired;
import com.imile.attendance.annon.NoLoginAuthRequired;
import com.imile.attendance.deviceConfig.application.AttendanceMobileConfigApplicationService;
import com.imile.attendance.deviceConfig.command.AttendanceMobileConfigAddCommand;
import com.imile.attendance.deviceConfig.command.AttendanceMobileConfigDeleteCommand;
import com.imile.attendance.deviceConfig.dto.AttendanceMobileConfigDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceMobileHistoryConfigDTO;
import com.imile.attendance.infrastructure.repository.deviceConfig.dto.AttendanceMobileConfigListDTO;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceMobileConfigListQuery;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 员工考勤常用手机表控制层
 *
 * <AUTHOR>
 * @menu 考勤手机管理
 * @date 2025/4/16
 */
@RestController
@RequestMapping("/attendance/mobile/config")
public class AttendanceMobileConfigController {

    @Resource
    private AttendanceMobileConfigApplicationService mobileConfigApplicationService;

    /**
     * 考勤手机列表查询
     */
    @PostMapping("/list")
    public Result<PaginationResult<AttendanceMobileConfigListDTO>> list(@RequestBody @Validated AttendanceMobileConfigListQuery param) {
        PaginationResult<AttendanceMobileConfigListDTO> list = mobileConfigApplicationService.list(param);
        return Result.ok(list);
    }

    /**
     * 考勤手机解绑
     */
    @PostMapping("/unbind")
    public Result<Boolean> unbind(@RequestBody @Validated AttendanceMobileConfigDeleteCommand deleteCommand) {
        mobileConfigApplicationService.unbind(deleteCommand);
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 考勤手机：新增数据
     */
    @PostMapping("/save")
    public Result<Boolean> save(@RequestBody @Validated AttendanceMobileConfigAddCommand addCommand) {
        mobileConfigApplicationService.add(addCommand);
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 用户考勤手机查询
     */
    @NoAuthRequired
    @NoLoginAuthRequired
    @GetMapping("/selectByUserCode")
    public Result<List<AttendanceMobileConfigDTO>> selectByUserCode(@NotBlank(message = "用户编码不能为空") @RequestParam("userCode") String userCode) {
        List<AttendanceMobileConfigDTO> resultList = mobileConfigApplicationService.selectByUserCodeForDetail(userCode);
        return Result.ok(resultList);
    }

    /**
     * 用户考勤手机查询(优化打卡用)
     */
    @NoAuthRequired
    @NoLoginAuthRequired
    @GetMapping("/selectByUserCodeForPunch")
    public Result<List<AttendanceMobileConfigDTO>> selectByUserCodeForPunch(@NotBlank(message = "用户编码不能为空") @RequestParam("userCode") String userCode) {
        List<AttendanceMobileConfigDTO> resultList = mobileConfigApplicationService.selectByUserCodeForPunch(userCode);
        return Result.ok(resultList);
    }

    /**
     * 用户考勤手机历史记录查询
     */
    @NoAuthRequired
    @NoLoginAuthRequired
    @GetMapping("/selectHistoryByUserCode")
    public Result<List<AttendanceMobileHistoryConfigDTO>> selectHistoryByUserCode(@NotBlank(message = "用户编码不能为空") @RequestParam("userCode") String userCode) {
        List<AttendanceMobileHistoryConfigDTO> resultList = mobileConfigApplicationService.selectHistoryByUserCode(userCode);
        return Result.ok(resultList);
    }

//    /**
//     * 用户考勤规则国家查询
//     */
//    @GetMapping("/selectMobileConfigCountryByUserCode")
//    public Result<List<String>> selectMobileConfigCountryByUserCode(@NotBlank(message = "用户编码不能为空") @RequestParam("userCode") String userCode) {
//        List<String> countryList = hrmsAttendanceMobileConfigService.selectMobileConfigCountryByUserCode(userCode);
//        return Result.ok(countryList);
//    }
}
