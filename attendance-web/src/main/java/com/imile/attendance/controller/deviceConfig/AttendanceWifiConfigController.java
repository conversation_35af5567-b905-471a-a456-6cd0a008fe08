package com.imile.attendance.controller.deviceConfig;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.annon.NoAuthRequired;
import com.imile.attendance.annon.NoLoginAuthRequired;
import com.imile.attendance.controller.BaseController;
import com.imile.attendance.deviceConfig.application.AttendanceWifiConfigApplicationService;
import com.imile.attendance.deviceConfig.command.AttendanceWifiConfigAddCommand;
import com.imile.attendance.deviceConfig.command.AttendanceWifiConfigDeleteCommand;
import com.imile.attendance.deviceConfig.command.AttendanceWifiConfigUpdateCommand;
import com.imile.attendance.deviceConfig.dto.AttendanceWifiConfigDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceWifiConfigExportDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceWifiConfigImportDTO;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceConfigFilterQuery;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceWifiConfigQuery;
import com.imile.common.component.repeat.DuplicateSubmit;
import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.excel.ExcelCallBackParam;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.common.validator.Groups;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;

/**
 * H5/移动 考勤打卡Wifi配置相关接口
 *
 * <AUTHOR>
 * @menu Wifi Mac 地址配置
 * @date 2025/4/16
 */
@RestController
@RequestMapping("/attendance/wifi")
public class AttendanceWifiConfigController extends BaseController {

    @Resource
    private AttendanceWifiConfigApplicationService wifiConfigApplicationService;

    /**
     * 添加wifi配置
     *
     * @return
     */
    @DuplicateSubmit
    @PostMapping("/add")
    public Result<Boolean> add(@Validated(Groups.Add.class) @RequestBody AttendanceWifiConfigAddCommand addCommand) {
        wifiConfigApplicationService.add(addCommand);
        return Result.ok(true);
    }

    /**
     * wifi配置列表查询
     *
     * @return
     */
    @PostMapping("/list")
    public Result<PaginationResult<AttendanceWifiConfigDTO>> list(@RequestBody AttendanceWifiConfigQuery query) {
        PaginationResult<AttendanceWifiConfigDTO> list = wifiConfigApplicationService.list(query);
        return Result.ok(list);
    }

    /**
     * wifi配置选择框查询
     *
     * @return
     */
    @NoAuthRequired
    @PostMapping("/select/list")
    public Result<List<AttendanceWifiConfigDTO>> selectList(@RequestBody AttendanceWifiConfigQuery query) {
        List<AttendanceWifiConfigDTO> list = wifiConfigApplicationService.selectList(query);
        return Result.ok(list);
    }


    /**
     * 更新wifi配置
     *
     * @return
     */
    @DuplicateSubmit
    @PostMapping("/update")
    public Result<Boolean> update(@Validated(Groups.Update.class) @RequestBody AttendanceWifiConfigUpdateCommand updateCommand) {
        wifiConfigApplicationService.update(updateCommand);
        return Result.ok(true);
    }

    /**
     * wifi规则详情
     *
     * @return
     */
    @PostMapping("/detail")
    public Result<AttendanceWifiConfigDTO> detail(@Validated @RequestBody AttendanceWifiConfigQuery query) {
        AttendanceWifiConfigDTO detailDTO = wifiConfigApplicationService.detail(query);
        return Result.ok(detailDTO);
    }

    /**
     * 删除wifi配置
     *
     * @return
     */
    @DuplicateSubmit
    @GetMapping("/delete")
    public Result<Boolean> delete(@NotNull(message = ValidCodeConstant.NOT_NULL) @RequestParam Long wifiConfigId) {
        wifiConfigApplicationService.delete(AttendanceWifiConfigDeleteCommand.of(wifiConfigId));
        return Result.ok(true);
    }

    /**
     * 导入wifi配置
     *
     * @return
     */
    @DuplicateSubmit
    @PostMapping("/import")
    public Result<List<AttendanceWifiConfigImportDTO>> importWifiConfig(HttpServletRequest request) {
        ExcelCallBackParam callBackParam = new ExcelCallBackParam(request);
        List<AttendanceWifiConfigImportDTO> importList = JSON.parseArray(callBackParam.getPageData(), AttendanceWifiConfigImportDTO.class);
        if (CollectionUtils.isEmpty(importList)) {
            return Result.ok(Collections.emptyList());
        }
        List<AttendanceWifiConfigImportDTO> failImportList = wifiConfigApplicationService.importWifiConfig(importList);
        return Result.ok(failImportList);
    }

    /**
     * 导出wifi配置
     *
     * @return
     */
    @PostMapping("/export")
    public Result<PaginationResult<AttendanceWifiConfigExportDTO>> export(HttpServletRequest request, AttendanceWifiConfigQuery query) {
        setExcelCallBackParam(request, query);
        PaginationResult<AttendanceWifiConfigExportDTO> result = wifiConfigApplicationService.export(query);
        return Result.ok(result);
    }

    /**
     * wifi国家/城市条件查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    @PostMapping("/selectFilterList")
    public Result<List<String>> selectFilterList(@RequestBody @Validated AttendanceConfigFilterQuery query) {
        return Result.ok(wifiConfigApplicationService.selectFilterList(query));
    }


    /**
     * 获取所有WiFi配置
     * 用于移动端打卡时获取可用的WiFi配置列表
     */
    @PostMapping("/all/config")
    @NoLoginAuthRequired
    @NoAuthRequired
    public Result<List<AttendanceWifiConfigDTO>> getAllWifiConfig() {
        return Result.ok(wifiConfigApplicationService.getAllWifiConfig());
    }


}
