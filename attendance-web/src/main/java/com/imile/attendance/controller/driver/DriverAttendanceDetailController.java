package com.imile.attendance.controller.driver;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.controller.BaseController;
import com.imile.attendance.driver.DriverAttendanceDetailService;
import com.imile.attendance.driver.dto.DriverAttendanceDetailExportParam;
import com.imile.attendance.driver.dto.DriverAttendanceDetailMonthExportParam;
import com.imile.attendance.driver.dto.DriverAttendanceDetailMonthParam;
import com.imile.attendance.driver.dto.DriverAttendanceDetailParam;
import com.imile.attendance.driver.dto.DriverAttendanceInfoDetailParam;
import com.imile.attendance.driver.vo.DriverAttendanceDetailMonthVO;
import com.imile.attendance.driver.vo.DriverAttendanceDetailVO;
import com.imile.attendance.driver.vo.DriverAttendanceInfoDetailVO;
import com.imile.attendance.infrastructure.convert.ConverterService;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceInfoDetailDTO;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceMonthTitleExportDTO;
import com.imile.attendance.util.CollectionUtils;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.util.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 司机考勤日月报
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description 司机每日考勤信息表(driver_attendance_detail)表控制层
 */
@RestController
@RequestMapping("/driver/attendance")
public class DriverAttendanceDetailController extends BaseController {

    @Resource
    private ConverterService converterService;
    @Resource
    private DriverAttendanceDetailService driverAttendanceDetailService;

    /**
     * 司机考勤日报列表
     *
     * @param param 参数
     * @return 列表
     */
    @PostMapping("/detail/list")
    public Result<PaginationResult<DriverAttendanceDetailVO>> queryDriverAttendance(@RequestBody @Valid DriverAttendanceDetailParam param) {
        PaginationResult<DriverAttendanceDetailVO> driverAttendanceVo = driverAttendanceDetailService.queryDriverAttendance(param);
        converterService.withAnnotation(driverAttendanceVo.getResults());
        return Result.ok(driverAttendanceVo);
    }

    /**
     * 每日、每月司机考勤查询详情
     *
     * @param param 参数
     * @return DriverAttendanceInfoDetailVO
     */
    @PostMapping("/detail")
    public Result<DriverAttendanceInfoDetailVO> queryDriverAttendanceDetail(@RequestBody @Valid DriverAttendanceInfoDetailParam param) {
        DriverAttendanceInfoDetailDTO driverAttendanceDetailDto = driverAttendanceDetailService.queryDriverAttendanceDetail(param);
        DriverAttendanceInfoDetailVO driverAttendanceDetailVO = CollectionUtils.convertSingle(driverAttendanceDetailDto, DriverAttendanceInfoDetailVO.class);
        converterService.withAnnotationForSingle(driverAttendanceDetailVO);
        return Result.ok(driverAttendanceDetailVO);
    }


    /**
     * 每日考勤导出功能
     *
     * @param param 查询参数
     * @return Result<PaginationResult < EmployeeAbnormalAttendanceVO>>
     */
    @PostMapping("/detail/list/export")
    public Result<PaginationResult<DriverAttendanceDetailVO>> export(HttpServletRequest request, DriverAttendanceDetailExportParam param) {
        DriverAttendanceDetailParam driverAttendanceDetailParam = BeanUtils.convert(param, DriverAttendanceDetailParam.class);
        // 参数转换：String 转 List
        if (ObjectUtil.isNotEmpty(param.getAttendanceTypeList())) {
            List<Integer> attendanceTypeList = JSON.parseArray(param.getAttendanceTypeList(), Integer.class).stream().collect(Collectors.toList());
            driverAttendanceDetailParam.setAttendanceTypeList(attendanceTypeList);
        }
        if (ObjectUtil.isNotEmpty(param.getEmployeeTypeList())) {
            List<String> employeeTypeList = JSON.parseArray(param.getEmployeeTypeList(), String.class).stream().collect(Collectors.toList());
            driverAttendanceDetailParam.setEmployeeTypeList(employeeTypeList);
        }
        if (ObjectUtil.isNotEmpty(param.getWorkStatusList())) {
            List<String> workStatusList = JSON.parseArray(param.getWorkStatusList(), String.class).stream().collect(Collectors.toList());
            driverAttendanceDetailParam.setWorkStatusList(workStatusList);
        }
        if (ObjectUtil.isNotEmpty(param.getDeptIdList())) {
            List<Long> deptIdList = JSON.parseArray(param.getDeptIdList(), Long.class).stream().collect(Collectors.toList());
            driverAttendanceDetailParam.setDeptIdList(deptIdList);
        }
        if (ObjectUtil.isNotEmpty(param.getDayDate())) {
            driverAttendanceDetailParam.setDayDate(DateUtil.parse(param.getDayDate(), DatePattern.NORM_DATE_PATTERN));
        }

        setExcelCallBackParam(request, driverAttendanceDetailParam);
        PaginationResult<DriverAttendanceDetailVO> driverAttendanceVo = driverAttendanceDetailService.queryExportDriverAttendance(driverAttendanceDetailParam);
        // 处理注解
        converterService.withAnnotation(driverAttendanceVo.getResults());
        return Result.ok(driverAttendanceVo);
    }

    /**
     * 司机考勤：月报报表列表
     *
     * @param param 参数
     * @return Result<PaginationResult<DriverAttendanceDetailMonthVO>>
     */
    @PostMapping("/detail/list/month")
    public Result<PaginationResult<DriverAttendanceDetailMonthVO>> queryMonthDriverAttendance(@RequestBody @Valid DriverAttendanceDetailMonthParam param) {
        PaginationResult<DriverAttendanceDetailMonthVO> driverAttendanceMonthVo = driverAttendanceDetailService.queryMonthDriverAttendance(param);
        converterService.withAnnotation(driverAttendanceMonthVo.getResults());
        return Result.ok(driverAttendanceMonthVo);
    }


    /**
     * 司机考勤：月报报表字段导出
     * @param param 参数
     * @return Result<List<DriverAttendanceMonthTitleExportDTO>>
     */
    @PostMapping("/detail/list/month/title/export")
    public Result<List<DriverAttendanceMonthTitleExportDTO>> attendanceMonthTitleExport(@RequestBody DriverAttendanceDetailMonthParam param) {
        List<DriverAttendanceMonthTitleExportDTO> result = driverAttendanceDetailService.driverAttendanceMonthTitleExport(param);
        return Result.ok(result);
    }


    /**
     * 司机考勤：月报报表导出
     * @param request 请求
     * @param param 参数
     * @return Result<PaginationResult<Map<String, String>>>
     */
    @PostMapping("/detail/list/month/export")
    public Result<PaginationResult<Map<String, String>>> monthExport(HttpServletRequest request, DriverAttendanceDetailMonthExportParam param) {
        // 使用service层的参数转换方法
        DriverAttendanceDetailMonthParam driverAttendanceDetailMonthParam = driverAttendanceDetailService.convertExportParam(param);
        // 设置Excel回调参数
        setExcelCallBackParam(request, driverAttendanceDetailMonthParam);
        PaginationResult<Map<String, String>> mapPaginationResult = driverAttendanceDetailService.queryExportMonthDriverAttendance(driverAttendanceDetailMonthParam);
        return Result.ok(mapPaginationResult);
    }


}
