package com.imile.attendance.controller.driver;

import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.DriverAttendanceOperateRecordEnum;
import com.imile.attendance.enums.DriverAttendanceOperationTypeEnum;
import com.imile.attendance.enums.DriverAttendanceSourceTypeEnum;
import com.imile.attendance.enums.DriverAttendanceTypeEnum;
import com.imile.attendance.vo.OptionVO;
import com.imile.common.result.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 司机考勤枚举接口
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description 司机考勤枚举接口
 */
@RestController
@RequestMapping("/driver/attendance/enum")
public class DriverAttendanceEnumController {

    /**
     * 获取司机考勤 打卡记录 操作类型列表：DLD签收、轨迹打卡、请假、修改考勤
     * @return List<OptionVO>
     */
    @GetMapping("/option/operation/type")
    public Result<List<OptionVO>> getDriverAttendanceOperationType() {
        return Result.ok(Arrays.stream(DriverAttendanceOperationTypeEnum.values())
                .filter(e -> !e.equals(DriverAttendanceOperationTypeEnum.DEFAULT))
                .map(item -> {
                    OptionVO option = new OptionVO();
                    option.setType(item.getType());
                    option.setDesc(RequestInfoHolder.isChinese() ? item.getDesc() : item.getDescEn());
                    return option;
                })
                .collect(Collectors.toList()));
    }

    /**
     * 获取司机考勤操作记录枚举列表：修改考勤、导出
     * @return List<OptionVO>
     */
    @GetMapping("/option/operation/record")
    public Result<List<OptionVO>> getDriverAttendanceOperationRecord() {
        return Result.ok(Arrays.stream(DriverAttendanceOperateRecordEnum.values())
                .filter(e -> !e.equals(DriverAttendanceOperateRecordEnum.DEFAULT))
                .map(item -> {
                    OptionVO option = new OptionVO();
                    option.setType(item.getType());
                    option.setDesc(RequestInfoHolder.isChinese() ? item.getDesc() : item.getDescEn());
                    return option;
                })
                .collect(Collectors.toList()));
    }

    /**
     * 获取司机考勤来源类型枚举列表：TMS、司机App、考勤系统
     * @return List<OptionVO>
     */
    @GetMapping("/option/source/type")
    public Result<List<OptionVO>> getDriverAttendanceSourceType() {
        return Result.ok(Arrays.stream(DriverAttendanceSourceTypeEnum.values())
                .filter(e -> !e.equals(DriverAttendanceSourceTypeEnum.DEFAULT))
                .map(item -> {
                    OptionVO option = new OptionVO();
                    option.setType(item.getType());
                    option.setDesc(RequestInfoHolder.isChinese() ? item.getDesc() : item.getDescEn());
                    return option;
                })
                .collect(Collectors.toList()));
    }


    /**
     * 获取司机 考勤类型枚举列表：出勤、缺勤、请假
     * @return List<OptionVO>
     */
    @GetMapping("/option/type")
    public Result<List<OptionVO>> getDriverAttendanceType() {
        return Result.ok(Arrays.stream(DriverAttendanceTypeEnum.values())
                .filter(e -> !e.equals(DriverAttendanceTypeEnum.DEFAULT))
                .map(item -> {
                    OptionVO option = new OptionVO();
                    option.setType(item.getType());
                    option.setDesc(RequestInfoHolder.isChinese() ? item.getDesc() : item.getDescEn());
                    return option;
                })
                .collect(Collectors.toList()));
    }
}
