package com.imile.attendance.controller.driver;

import com.imile.attendance.driver.DriverPunchRecordService;
import com.imile.attendance.driver.dto.DriverPunchRecordParam;
import com.imile.attendance.driver.dto.ModifyDriverAttendanceParam;
import com.imile.attendance.driver.vo.DriverPunchRecordVO;
import com.imile.attendance.infrastructure.convert.ConverterService;
import com.imile.common.result.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 司机打卡记录表
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description 司机打卡记录表(driver_punch_record)表控制层
 */
@RestController
@RequestMapping("/driver/punch/record")
public class DriverPunchRecordController {

    @Autowired
    private ConverterService converterService;
    @Resource
    private DriverPunchRecordService driverPunchRecordService;

    /**
     * 每日司机打卡记录列表
     * @param param 查询参数
     * @return 司机打卡记录列表
     */
    @PostMapping("/detail")
    public Result<List<DriverPunchRecordVO>> queryDriverPunchRecordDetail(@RequestBody @Valid DriverPunchRecordParam param) {
        List<DriverPunchRecordVO> driverPunchRecordVo = driverPunchRecordService.selectPunchRecordDetail(param);
        // 处理注解
        converterService.withAnnotation(driverPunchRecordVo);
        return Result.ok(driverPunchRecordVo);
    }

    /**
     * 修改司机过去考勤
     *
     * @param param 参数
     * @return 布尔
     */
    @PostMapping("/modify")
    public Result<Boolean> modifyDriverAttendanceDetail(@RequestBody ModifyDriverAttendanceParam param) {
        driverPunchRecordService.modifyDriverAttendanceDetail(param);
        return Result.ok(Boolean.TRUE);
    }
}
