package com.imile.attendance.controller.form;

import com.imile.attendance.annon.ExportParamFill;
import com.imile.attendance.annon.NoAttendanceLoginAuthRequired;
import com.imile.attendance.annon.NoLoginAuthRequired;
import com.imile.attendance.controller.BaseController;
import com.imile.attendance.form.CommonFormOperationService;
import com.imile.attendance.form.param.ApplicationFormCancelParam;
import com.imile.attendance.form.param.ApplicationFormDeleteParam;
import com.imile.attendance.form.param.ApplicationFormDetailParam;
import com.imile.attendance.form.param.AttendanceApprovalInfoParam;
import com.imile.attendance.form.param.DurationDetailParam;
import com.imile.attendance.form.param.UserAuthParam;
import com.imile.attendance.form.vo.AttendanceApplicationFromDetailVO;
import com.imile.attendance.form.vo.AttendanceApprovalInfoVO;
import com.imile.attendance.form.vo.DurationDetailVO;
import com.imile.attendance.form.vo.FormInfoExportVO;
import com.imile.attendance.form.vo.UserAuthVO;
import com.imile.attendance.user.UserLeaveService;
import com.imile.attendance.user.query.UserLeaveResidualQuery;
import com.imile.attendance.user.vo.UserLeaveResidualVO;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @description: HR考勤审批所有入口
 * @author: taokang
 * @createDate: 2023-6-21
 * @version: 1.0
 */
@Slf4j
@RestController
@RequestMapping("/attendance/approval")
public class CommonApprovalController extends BaseController {

    @Resource
    private CommonFormOperationService commonFormOperationService;
    @Resource
    private UserLeaveService userLeaveService;

    /**
     * 获取用户假期余额信息
     */
    @PostMapping("/user/leave/residual")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<List<UserLeaveResidualVO>> selectUserLeaveResidual(@RequestBody @Validated UserLeaveResidualQuery param) {
        List<UserLeaveResidualVO> resultVO = userLeaveService.selectUserLeaveResidual(param.getUserId());
        return Result.ok(resultVO);
    }

    /**
     * 列表查询
     */
    @PostMapping("/list")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<PaginationResult<AttendanceApprovalInfoVO>> list(@RequestBody @Validated AttendanceApprovalInfoParam param) {
        PaginationResult<AttendanceApprovalInfoVO> resultVO = commonFormOperationService.list(param);
        return Result.ok(resultVO);
    }

    /**
     * 审批记录相关权限（HRMS迁移过来 暂时不改）
     */
    @PostMapping("/user/auth/list")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<List<UserAuthVO>> userAuthList(@RequestBody @Validated UserAuthParam param) {
        List<UserAuthVO> resultVOList = commonFormOperationService.userAuthList(param);
        return Result.ok(resultVOList);
    }

    /**
     * 请假/外勤获取冲突单据和具体时长计算信息
     */
    @PostMapping("/duration/detail")
    @NoLoginAuthRequired
    public Result<DurationDetailVO> durationDetail(@RequestBody @Validated DurationDetailParam param) {
        DurationDetailVO resultVO = commonFormOperationService.durationDetail(param);
        return Result.ok(resultVO);
    }

    /**
     * 取消
     */
    @PostMapping("/cancel")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<Boolean> cancel(@RequestBody @Validated ApplicationFormCancelParam param) {
        commonFormOperationService.cancel(param.getFormId());
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 详情
     */
    @PostMapping("/detail")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<AttendanceApplicationFromDetailVO> detail(@RequestBody ApplicationFormDetailParam param) {
        AttendanceApplicationFromDetailVO detail = commonFormOperationService.getFromDetail(param.getFormId());
        return Result.ok(detail);
    }

    /**
     * 删除
     */
    @PostMapping("/delete")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<Boolean> delete(@RequestBody @Validated ApplicationFormDeleteParam param) {
        commonFormOperationService.delete(param.getFormId());
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 单据信息导出
     */
    @PostMapping("/export")
    @ExportParamFill
    public Result<PaginationResult<FormInfoExportVO>> listExport(HttpServletRequest request,
                                                                 AttendanceApprovalInfoParam param) {
        setExcelCallBackParam(request, param);
        PaginationResult<FormInfoExportVO> res = commonFormOperationService.listExport(param);
        return Result.ok(res);
    }
}
