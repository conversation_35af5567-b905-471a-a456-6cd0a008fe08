package com.imile.attendance.controller.logRecord;

import com.imile.attendance.logRecord.LogOperationRecordService;
import com.imile.attendance.infrastructure.repository.log.dto.LogRecordPageDTO;
import com.imile.attendance.infrastructure.repository.log.query.LogRecordPageQuery;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31 
 * @Description 日志操作记录
 */
@RestController
@RequestMapping("/logOperationRecord")
public class LogOperationRecordController {

    @Resource
    private LogOperationRecordService logOperationRecordService;

    /**
     * 分页查询操作日志记录
     */
    @PostMapping("/page")
    public Result<PaginationResult<LogRecordPageDTO>> page(@RequestBody LogRecordPageQuery logRecordPageQuery) {
        return Result.ok(logOperationRecordService.page(logRecordPageQuery));
    }
}
