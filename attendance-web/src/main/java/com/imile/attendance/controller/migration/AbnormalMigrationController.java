package com.imile.attendance.controller.migration;

import com.imile.attendance.annon.NoAuthRequired;
import com.imile.attendance.migration.AbnormalMigrationService;
import com.imile.attendance.migration.dto.AbnormalSyncDTO;
import com.imile.common.result.Result;
import com.imile.ucenter.api.authenticate.NoLoginRequired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 用户异常相关迁移
 *
 * <AUTHOR>
 * @since 2025/6/16
 */
@RequestMapping("/abnormal/migration")
@RestController
public class AbnormalMigrationController {

    @Resource
    private AbnormalMigrationService abnormalMigrationService;


    /**
     * 同步老系统员工异常、出勤明细、相关快照表到新系统
     */
    @PostMapping("/syncNewSystemAbnormalRecord")
    @NoAuthRequired
    @NoLoginRequired
    public Result<Void> syncNewSystemAbnormalRecord(@RequestBody @Validated AbnormalSyncDTO abnormalSyncDTO) {
        abnormalMigrationService.syncNewSystemAbnormalRecord(abnormalSyncDTO);
        return Result.ok();
    }

    /**
     * 同步老系统打卡记录表到新系统
     */
    @PostMapping("/syncNewSystemPunchRecord")
    @NoAuthRequired
    @NoLoginRequired
    public Result<Void> syncNewSystemPunchRecord(@RequestBody @Validated AbnormalSyncDTO abnormalSyncDTO) {
        abnormalMigrationService.syncEmployeePunchRecordToNewSystem(abnormalSyncDTO);
        return Result.ok();
    }

//    @GetMapping("/remove/punchRecord")
//    @NoAuthRequired
//    @NoLoginRequired
//    public Result<Void> removePunchRecord(String country, Long id) {
//        abnormalMigrationService.removeEmployeePunchRecord(country,id);
//        return Result.ok();
//    }
//
//    @GetMapping("/remove/hrPunchRecord")
//    @NoAuthRequired
//    @NoLoginRequired
//    public Result<Void> removeHrPunchRecord(Long punchRecordId) {
//        abnormalMigrationService.removeHrEmployeePunchRecord(punchRecordId);
//        return Result.ok();
//    }
}
