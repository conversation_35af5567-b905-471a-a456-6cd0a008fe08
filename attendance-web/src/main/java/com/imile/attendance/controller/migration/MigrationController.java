package com.imile.attendance.controller.migration;

import com.imile.attendance.annon.NoAuthRequired;
import com.imile.attendance.annon.NoLoginAuthRequired;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.migration.RuleMigrationService;
import com.imile.common.result.Result;
import com.imile.ucenter.api.authenticate.NoLoginRequired;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 系统迁移相关接口
 *
 * <AUTHOR> chen
 * @Date 2025/5/27
 * @Description 提供系统迁移相关的HTTP接口
 */
@Slf4j
@RequestMapping("/migration")
@RestController
public class MigrationController {

    @Resource
    private MigrationService migrationService;
    @Resource
    private RuleMigrationService ruleMigrationService;

    /**
     * 获取启用新考勤系统的国家列表
     *
     * @return 国家列表
     */
    @GetMapping("/getEnableNewAttendanceCountry")
    @NoLoginAuthRequired
    @NoAuthRequired
    @NoLoginRequired
    public Result<List<String>> getEnableNewAttendanceCountry() {
        return Result.ok(migrationService.getEnableNewAttendanceCountry());
    }

    /**
     * 获取当前用户编码
     *
     * @return 用户编码
     */
    @GetMapping("/getCurrentUserCode")
    @NoLoginAuthRequired
    @NoAuthRequired
    @NoLoginRequired
    public Result<String> getCurrentUserCode() {
        return Result.ok(migrationService.getCurrentUserCode());
    }

    /**
     * 验证用户是否启用新考勤系统
     *
     * @return 验证结果
     */
    @GetMapping("/verifyUserIsEnableNewAttendance")
    @NoLoginAuthRequired
    @NoAuthRequired
    @NoLoginRequired
    public Result<Boolean> verifyUserIsEnableNewAttendance() {
        return Result.ok(migrationService.verifyUserIsEnableNewAttendance());
    }


    /**
     * HR考勤组到新考勤规则的迁移
     */
    @NoLoginRequired
    @ApiOperation("HR考勤组到新考勤规则的迁移")
    @PostMapping("/attendance-rules")
    public Result<Boolean> migrateAttendanceRules(
            @ApiParam(value = "国家代码", required = true, example = "CHN")
            @RequestParam String country) {
        CompletableFuture.runAsync(
                () -> {
                    log.info("开始迁移HR考勤组到新考勤规则, country: {}", country);
                    try {
                        Boolean result = ruleMigrationService.migrateAttendanceRules(country);
                        log.info("迁移HR考勤组到新考勤规则完成, country: {}, result: {}", country, result);
                    } catch (Exception e) {
                        log.error("迁移HR考勤组到新考勤规则失败, country: {}", country, e);
                    }
                }
        );
        return Result.ok();
    }

    @NoLoginRequired
    @ApiOperation("根据国家回滚考勤规则")
    @PostMapping("/rollback-attendance-rules")
    public Result<Boolean> rollbackAttendanceRules(
            @ApiParam(value = "国家代码", required = true, example = "CHN")
            @RequestParam String country) {
        CompletableFuture.runAsync(
                () -> {
                    log.info("开始回滚考勤规则, country: {}", country);

                    try {
                        Boolean result = ruleMigrationService.rollbackAttendanceRules(country);
                        log.info("回滚考勤规则完成, country: {}, result: {}", country, result);
                    } catch (Exception e) {
                        log.error("回滚考勤规则失败, country: {}", country, e);
                    }
                }
        );
        return Result.ok();
    }
}
