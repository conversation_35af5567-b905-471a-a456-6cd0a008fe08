package com.imile.attendance.controller.migration;

import com.imile.attendance.migration.UserShiftConfigMigrationService;
import com.imile.attendance.migration.dto.UserShiftConfigMigrationParam;
import com.imile.common.result.Result;
import com.imile.ucenter.api.authenticate.NoLoginRequired;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

/**
 * 排班数据迁移测试控制器
 * 用于测试和手动触发迁移任务
 *
 * <AUTHOR> chen
 * @since 2025/6/18
 */
@Slf4j
@RestController
@RequestMapping("/migration/user-shift-config")
@Api(tags = "排班数据迁移测试接口")
public class UserShiftConfigMigrationController {

    @Resource
    private UserShiftConfigMigrationService migrationService;

    /**
     * 执行完整的排班数据迁移
     */
    @PostMapping("/full-migration")
    @ApiOperation("执行完整的排班数据迁移")
    @NoLoginRequired
    public Result<Void> executeFullMigration(
            @ApiParam(value = "国家代码", required = true) @RequestParam String country) {

        log.info("手动触发完整排班数据迁移, country: {}", country);
        CompletableFuture.runAsync(
                () -> migrationService.executeFullMigration(country)
        );
        return Result.ok();
    }

    /**
     * 仅迁移历史数据
     */
    @PostMapping("/history-migration")
    @ApiOperation("仅迁移历史数据")
    @NoLoginRequired
    public Result<Void> migrateHistoryData(
            @ApiParam(value = "国家代码", required = true) @RequestParam String country,
            @ApiParam(value = "开始日期ID", required = false) @RequestParam(required = false) Long startDayId,
            @ApiParam(value = "结束日期ID", required = false) @RequestParam(required = false) Long endDayId) {

        log.info("手动触发历史排班数据迁移, country: {}, dayId范围: {} - {}",
                country, startDayId, endDayId);
        CompletableFuture.runAsync(
                () -> migrationService.migrateHistoryDataOnly(country, startDayId, endDayId)
        );
        return Result.ok();
    }

    /**
     * 仅迁移当前数据
     */
    @PostMapping("/current-migration")
    @ApiOperation("仅迁移当前数据")
    @NoLoginRequired
    public Result<Void> migrateCurrentData(
            @ApiParam(value = "国家代码", required = true) @RequestParam String country,
            @ApiParam(value = "开始日期ID", required = false) @RequestParam(required = false) Long startDayId,
            @ApiParam(value = "结束日期ID", required = false) @RequestParam(required = false) Long endDayId) {

        log.info("手动触发当前排班数据迁移, country: {}, dayId范围: {} - {}",
                country, startDayId, endDayId);
        CompletableFuture.runAsync(
                () -> migrationService.migrateCurrentDataOnly(country, startDayId, endDayId)
        );
        return Result.ok();
    }

    /**
     * 通过参数对象执行迁移
     */
    @PostMapping("/execute-migration")
    @ApiOperation("通过参数对象执行迁移")
    @NoLoginRequired
    public Result<Void> executeMigration(@RequestBody UserShiftConfigMigrationParam param) {

        log.info("手动触发排班数据迁移, 参数: {}", param);

        try {
            // 验证参数
            if (!param.isValid()) {
                return Result.fail("参数验证失败: " + param.toString());
            }

            Boolean result = false;
            switch (param.getMigrationTypeEnum()) {
                case FULL:
                    CompletableFuture.runAsync(
                            () -> migrationService.executeFullMigration(param.getCountry())
                    );
                    break;
                case HISTORY:
                    CompletableFuture.runAsync(
                            () -> migrationService.migrateHistoryDataOnly(
                                    param.getCountry(), param.getStartDayId(), param.getEndDayId())
                    );
                    break;
                case CURRENT:
                    CompletableFuture.runAsync(
                            () -> migrationService.migrateCurrentDataOnly(
                                    param.getCountry(), param.getStartDayId(), param.getEndDayId())
                    );
                    break;
                default:
                    return Result.fail("不支持的迁移类型: " + param.getMigrationType());
            }

            return Result.ok();

        } catch (Exception e) {
            log.error("排班数据迁移异常, 参数: {}", param, e);
            return Result.fail("排班数据迁移异常: " + e.getMessage());
        }
    }

    /**
     * 根据国家为非灰度用户执行排班数据迁移
     *
     * <p>该接口用于为指定国家的非灰度用户迁移排班数据，执行流程：</p>
     * <ol>
     *   <li>识别指定国家的所有非灰度用户</li>
     *   <li>删除这些用户的历史排班数据</li>
     *   <li>为这些用户迁移新的排班数据</li>
     * </ol>
     *
     * @param country         国家
     * @param shiftStartDayId 排班开始日期ID（必填，格式：yyyyMMdd，如：20250723）
     * @return 迁移结果，成功返回true，失败返回false
     * @example POST /migration/user-shift-config/migrate-by-country-no-grayscale
     * Params:
     * - country=CHN
     * - shiftStartDayId=20250723
     */
    @PostMapping("/migrate-by-country-no-grayscale")
    @ApiOperation("根据国家为非灰度用户执行排班数据迁移")
    @NoLoginRequired
    public Result<Boolean> migrateByCountryNoGrayscaleUser(
            @ApiParam(value = "国家代码", required = true) @RequestParam String country,
            @ApiParam(value = "排班开始日期ID", required = true) @RequestParam Long shiftStartDayId) {

        CompletableFuture.runAsync(
                RunnableWrapper.of(
                        () -> {
                            log.info("开始为非灰度用户迁移排班数据, country: {}, shiftStartDayId: {}", country, shiftStartDayId);

                            try {
                                // 参数验证
                                if (StringUtils.isBlank(country)) {
                                    log.error("国家参数不能为空");
                                    return;
                                }

                                if (shiftStartDayId == null || shiftStartDayId <= 0) {
                                    log.error("排班开始日期ID无效: {}", shiftStartDayId);
                                    return;
                                }

                                // 执行迁移
                                Boolean result = migrationService.migrateByCountryNoGrayscaleUser(country, shiftStartDayId);

                                if (Boolean.TRUE.equals(result)) {
                                    log.info("为非灰度用户迁移排班数据成功, country: {}, shiftStartDayId: {}", country, shiftStartDayId);
                                } else {
                                    log.error("为非灰度用户迁移排班数据失败, country: {}, shiftStartDayId: {}", country, shiftStartDayId);
                                }

                            } catch (Exception e) {
                                log.error("为非灰度用户迁移排班数据时发生异常, country: {}, shiftStartDayId: {}", country, shiftStartDayId, e);
                            }
                        }
                )
        );
        return Result.ok();
    }

    /**
     * 回滚当前数据迁移操作
     *
     * @param country    国家代码，用于限定回滚范围（必填）
     * @param startDayId 开始日期ID，用于限定回滚的日期范围（可选，格式：yyyyMMdd）
     * @param endDayId   结束日期ID，用于限定回滚的日期范围（可选，格式：yyyyMMdd）
     * @return 回滚结果，成功返回true，失败返回false
     * <p>
     * POST /migration/user-shift-config/rollback-current-data
     * Params:
     * - country: "MEX"
     * - startDayId: 20250101 (可选)
     * - endDayId: 20251231 (可选)
     */
    @PostMapping("/rollback-current-data")
    @ApiOperation("回滚当前数据迁移操作")
    @NoLoginRequired
    public Result<Void> rollbackCurrentData(
            @ApiParam(value = "国家代码", required = true) @RequestParam String country,
            @ApiParam(value = "开始日期ID", required = false) @RequestParam(required = false) Long startDayId,
            @ApiParam(value = "结束日期ID", required = false) @RequestParam(required = false) Long endDayId) {

        log.info("手动触发当前排班数据迁移回滚, country: {}, dayId范围: {} - {}",
                country, startDayId, endDayId);

        try {
            // 参数验证
            if (StringUtils.isBlank(country)) {
                return Result.fail("国家代码不能为空");
            }

            // 异步执行回滚操作
            CompletableFuture.runAsync(
                    RunnableWrapper.of(
                            () -> migrationService.rollbackCurrentDataOnly(country, startDayId, endDayId)
                    )
            );

            return Result.ok();

        } catch (Exception e) {
            log.error("触发当前排班数据迁移回滚时发生异常, country: {}, dayId范围: {} - {}",
                    country, startDayId, endDayId, e);
            return Result.fail("回滚操作触发失败: " + e.getMessage());
        }
    }
}
