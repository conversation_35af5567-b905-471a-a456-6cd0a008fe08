package com.imile.attendance.controller.permission;

import com.imile.attendance.permission.AttendancePermissionService;
import com.imile.attendance.permission.vo.AttendanceDeptVO;
import com.imile.common.result.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 考勤权限相关服务
 *
 * <AUTHOR>
 * @since 2025/4/24
 */
@RestController
public class AttendancePermissionController {

    @Resource
    private AttendancePermissionService attendancePermissionService;

    /**
     * 获取登录用户有权限国家范围
     *
     * @return Result<List < String>>
     */
    @GetMapping("/getUserCountryPermission")
    public Result<List<String>> getUserCountryPermission() {
        return Result.ok(attendancePermissionService.getUserCountryPermission());
    }

    /**
     * 获取登录用户有常驻国权限国家范围
     *
     * @return Result<List < String>>
     */
    @GetMapping("/getUserLocationCountryPermission")
    public Result<List<String>> getUserLocationCountryPermission() {
        return Result.ok(attendancePermissionService.getUserLocationCountryPermission());
    }

    /**
     * 通过地理国获取登录用户有权限部门范围
     *
     * @param country 国家
     * @return Result<List < AttendanceDept>>
     */
    @GetMapping("/getUserDeptPermissionByCountry")
    public Result<List<AttendanceDeptVO>> getUserDeptPermissionByCountry(String country) {
        return Result.ok(attendancePermissionService.getUserDeptPermissionByCountry(country));
    }

//    /**
//     * 获取部门树
//     *
//     * @return
//     */
//    @PostMapping("getDeptTree")
//    public Result<List<AttendanceDeptTreeVO>> getDeptTree(@RequestBody DeptQuery query) {
//        List<AttendanceDeptTreeDTO> deptTree = attendancePermissionService.getDeptTree(RequestInfoHolder.getUserId(), query);
//        return Result.ok(PermissionMapstruct.INSTANCE.mapToDeptTree(deptTree));
//    }
}
