package com.imile.attendance.controller.report;

import com.imile.attendance.annon.ExportParamFill;
import com.imile.attendance.controller.BaseController;
import com.imile.attendance.report.day.AttendanceDayReportService;
import com.imile.attendance.report.day.query.DayReportDetailQuery;
import com.imile.attendance.report.day.query.DayReportListQuery;
import com.imile.attendance.report.day.vo.UserDayReportExportVO;
import com.imile.attendance.report.day.vo.UserDayReportListVO;
import com.imile.attendance.report.day.dto.UserDayReportDTO;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 考勤月报相关接口
 *
 * <AUTHOR>
 * @menu 考勤日报
 * @date 2025/6/14
 */
@Slf4j
@RequestMapping("/report/day")
@RestController
public class AttendanceDayReportController extends BaseController {

    @Resource
    private AttendanceDayReportService attendanceDayReportService;

    /**
     * 考勤日报明细
     *
     * @param query
     * @return
     */
    @PostMapping("/list")
    public Result<PaginationResult<UserDayReportListVO>> list(@RequestBody DayReportListQuery query) {
        PaginationResult<UserDayReportListVO> list = attendanceDayReportService.list(query);
        return Result.ok(list);
    }

    /**
     * 考勤日报详情
     *
     * @return
     */
    @PostMapping("/detail")
    public Result<UserDayReportDTO> detail(@Validated @RequestBody DayReportDetailQuery query) {
        UserDayReportDTO detailDTO = attendanceDayReportService.detail(query);
        return Result.ok(detailDTO);
    }

    /**
     * 考勤日报导出
     *
     * @param query
     * @return
     */
    @PostMapping("/export")
    @ExportParamFill
    public Result<PaginationResult<UserDayReportExportVO>> export(HttpServletRequest request,
                                                                  DayReportListQuery query) {
        setExcelCallBackParam(request, query);
        PaginationResult<UserDayReportExportVO> resultVO = attendanceDayReportService.export(query);
        return Result.ok(resultVO);
    }
}
