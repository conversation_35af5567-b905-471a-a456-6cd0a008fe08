package com.imile.attendance.controller.report;

import com.imile.attendance.annon.ExportParamFill;
import com.imile.attendance.annon.NoAuthRequired;
import com.imile.attendance.annon.NoLoginAuthRequired;
import com.imile.attendance.controller.BaseController;
import com.imile.attendance.infrastructure.excel.header.ExcelTitleExportDTO;
import com.imile.attendance.infrastructure.repository.abnormal.dto.UserAttendanceDTO;
import com.imile.attendance.infrastructure.repository.abnormal.query.AttendanceEmployeeDetailQuery;
import com.imile.attendance.infrastructure.repository.report.query.MonthReportListQuery;
import com.imile.attendance.report.day.query.DayReportListQuery;
import com.imile.attendance.report.day.vo.UserDayReportListVO;
import com.imile.attendance.report.month.AttendanceMonthReportExportService;
import com.imile.attendance.report.month.AttendanceMonthReportService;
import com.imile.attendance.report.month.vo.UserMonthReportListVO;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 考勤月报相关接口
 *
 * <AUTHOR>
 * @menu 考勤月报
 * @date 2025/6/9
 */
@Slf4j
@RequestMapping("/report/month")
@RestController
public class AttendanceMonthReportController extends BaseController {

    @Resource
    private AttendanceMonthReportService attendanceMonthReportService;
    @Resource
    private AttendanceMonthReportExportService monthReportExportService;


    /**
     * 分页查询考勤月报（详情传用户id，currentPage=1，showCount=1）
     */
    @PostMapping("/list")
    public Result<PaginationResult<UserMonthReportListVO>> list(@Validated @RequestBody MonthReportListQuery query) {
        return Result.ok(attendanceMonthReportService.list(query));
    }


    /**
     * 月报报表字段导出
     */
    @PostMapping("/title/export")
    public Result<List<ExcelTitleExportDTO>> attendanceMonthTitleExport(@RequestBody MonthReportListQuery query) {
        return Result.ok(monthReportExportService.titleExport(query));
    }


    /**
     * 月报报表导出 ruleId:700023 (表头调用/title/export接口)
     */
    @PostMapping("/export")
    @ExportParamFill
    public Result<PaginationResult<Map<String, String>>> attendanceMonthExport(@Validated MonthReportListQuery query) {
//        query.setShowCount(200);
        return Result.ok(monthReportExportService.monthReportExport(query));
    }


    /**
     * 员工出勤明细
     *
     * @param query
     * @return
     */
    @NoLoginAuthRequired
    @NoAuthRequired
    @PostMapping("/detail")
    public Result<UserAttendanceDTO> detail(@RequestBody AttendanceEmployeeDetailQuery query) {
        UserAttendanceDTO userAttendanceDTO = attendanceMonthReportService.detail(query);
        return Result.ok(userAttendanceDTO);
    }
}
