package com.imile.attendance.controller.rule;

import com.imile.attendance.controller.BaseController;
import com.imile.attendance.annon.ExportParamFill;
import com.imile.attendance.infrastructure.convert.ConverterService;
import com.imile.attendance.rule.application.PunchClassConfigApplicationService;
import com.imile.attendance.rule.command.PunchClassConfigAddCommand;
import com.imile.attendance.rule.command.PunchClassConfigStatusSwitchCheckCommand;
import com.imile.attendance.rule.command.PunchClassConfigStatusSwitchCommand;
import com.imile.attendance.rule.dto.RuleConfigUserInfoDTO;
import com.imile.attendance.rule.query.PunchClassConfigDetailQuery;
import com.imile.attendance.rule.query.PunchClassConfigListQuery;
import com.imile.attendance.rule.query.PunchClassConfigUserQuery;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.rule.vo.PunchClassConfigAddConfirmVO;
import com.imile.attendance.rule.vo.PunchClassConfigAddVO;
import com.imile.attendance.rule.vo.PunchClassConfigDetailVO;
import com.imile.attendance.rule.vo.PunchClassConfigDisabledCheckConfirmVO;
import com.imile.attendance.rule.vo.PunchClassConfigExportVO;
import com.imile.attendance.rule.vo.PunchClassConfigUpdateConfirmVO;
import com.imile.attendance.rule.vo.PunchClassConfigVO;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.common.validator.Groups;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 班次服务
 *
 * <AUTHOR>
 * @since 2025/4/7
 */
@RestController
@RequestMapping("/class/config")
public class PunchClassConfigController extends BaseController {
    @Resource
    private PunchClassConfigApplicationService punchClassConfigApplicationService;
    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;
    @Resource
    private ConverterService converterService;

    /**
     * 班次列表
     */
    @PostMapping("/list")
    public Result<PaginationResult<PunchClassConfigVO>> list(@RequestBody @Validated PunchClassConfigListQuery query) {
        PaginationResult<PunchClassConfigVO> result = punchClassConfigQueryService.list(query);
        return Result.ok(result);
    }

    /**
     * 班次详情
     */
    @PostMapping("/detail")
    public Result<PunchClassConfigDetailVO> detail(@RequestBody @Validated PunchClassConfigDetailQuery query) {
        PunchClassConfigDetailVO result = punchClassConfigQueryService.detail(query);
        return Result.ok(result);
    }

    /**
     * 分页查询班次配置的用户列表
     */
    @PostMapping("/page/applyUser")
    public Result<PaginationResult<RuleConfigUserInfoDTO>> pageApplyUser(@RequestBody @Validated PunchClassConfigUserQuery query) {
        PaginationResult<RuleConfigUserInfoDTO> result = punchClassConfigQueryService.pagePunchClassConfigUserList(query);
        converterService.withAnnotation(result.getResults());
        return Result.ok(result);
    }

    /**
     * 班次新增前置处理
     */
    @PostMapping("/add/pre/processor")
    public Result<PunchClassConfigAddConfirmVO> addPreProcessor(@Validated(Groups.Add.class) @RequestBody PunchClassConfigAddCommand command) {
        return Result.ok(punchClassConfigApplicationService.addPreProcessor(command));
    }

    /**
     * 班次新增
     */
    @PostMapping("/add")
    public Result<PunchClassConfigAddVO> add(@Validated(Groups.Add.class) @RequestBody PunchClassConfigAddCommand command) {
        return Result.ok(punchClassConfigApplicationService.add(command));
    }

    /**
     * 班次编辑前置处理
     */
    @PostMapping("/update/pre/processor")
    public Result<PunchClassConfigUpdateConfirmVO> updatePreProcessor(@Validated(Groups.Update.class) @RequestBody PunchClassConfigAddCommand command) {
        return Result.ok(punchClassConfigApplicationService.updatePreProcessor(command));
    }

    /**
     * 班次编辑
     */
    @PostMapping("/update")
    public Result<PunchClassConfigAddVO> update(@Validated(Groups.Update.class) @RequestBody PunchClassConfigAddCommand command) {
        return Result.ok(punchClassConfigApplicationService.update(command));
    }

    /**
     * 班次状态停用检查
     */
    @PostMapping("/disabled/check")
    public Result<PunchClassConfigDisabledCheckConfirmVO> disabledCheck(@RequestBody @Validated PunchClassConfigStatusSwitchCheckCommand command) {
        return Result.ok(punchClassConfigApplicationService.disabledCheck(command.getId()));
    }

    /**
     * 班次状态启用检查
     */
    @PostMapping("/enable/check")
    public Result<Void> enableCheck(@RequestBody @Validated PunchClassConfigStatusSwitchCheckCommand command) {
        punchClassConfigApplicationService.enableCheck(command.getId());
        return Result.ok();
    }

    /**
     * 班次状态启停用
     */
    @PostMapping("/status/switch")
    public Result<Boolean> statusSwitch(@RequestBody @Validated PunchClassConfigStatusSwitchCommand command) {
        return Result.ok(punchClassConfigApplicationService.statusSwitch(command));
    }

    /**
     * 班次导出
     */
    @PostMapping("/export")
    @ExportParamFill
    public Result<PaginationResult<PunchClassConfigExportVO>> export(PunchClassConfigListQuery query) {
        PaginationResult<PunchClassConfigExportVO> result = punchClassConfigQueryService.export(query);
        converterService.withAnnotation(result.getResults());
        return Result.ok(result);
    }
}
