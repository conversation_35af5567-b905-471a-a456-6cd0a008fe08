package com.imile.attendance.controller.rule;

import javax.annotation.Resource;

import com.imile.attendance.infrastructure.repository.rule.query.PunchConfigPageQuery;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.imile.attendance.controller.BaseController;
import com.imile.attendance.annon.ExportParamFill;
import com.imile.attendance.rule.PunchConfigService;
import com.imile.attendance.rule.command.PunchConfigAddCommand;
import com.imile.attendance.rule.command.PunchConfigStatusSwitchCommand;
import com.imile.attendance.rule.command.PunchConfigUpdateCommand;
import com.imile.attendance.rule.dto.PunchConfigDetailDTO;
import com.imile.attendance.rule.dto.PunchConfigPageDTO;
import com.imile.attendance.rule.dto.RuleConfigChangeCheckDTO;
import com.imile.attendance.rule.dto.RuleConfigUserInfoDTO;
import com.imile.attendance.rule.dto.UpdateRuleReflectResult;
import com.imile.attendance.rule.query.PunchConfigUserQuery;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;

/**
 * 打卡规则
 * <AUTHOR> chen
 * @Date 2025/4/15 
 * @Description
 */
@RestController
@RequestMapping("/punch/config")
public class PunchConfigController extends BaseController {

    @Resource
    private PunchConfigService punchConfigService;


    /**
     * 打卡规则列表
     */
    @PostMapping("/page")
    public Result<PaginationResult<PunchConfigPageDTO>> page(@RequestBody PunchConfigPageQuery query) {
        return Result.ok(punchConfigService.pagePunchConfigList(query));
    }

    /**
     * 导出打卡规则列表
     */
    @PostMapping("/page/export")
    @ExportParamFill
    public Result<PaginationResult<PunchConfigPageDTO>> pageExport(PunchConfigPageQuery query) {
        query.setArePageExport(true);
        return Result.ok(punchConfigService.pagePunchConfigList(query));
    }

    /**
     * 查询打卡配置详情
     *
     * @param configNo 配置编号
     * @return Result<PunchConfigDetailDTO>
     */
    @GetMapping("/detail")
    public Result<PunchConfigDetailDTO> detail(String configNo) {
        return Result.ok(punchConfigService.queryPunchConfigDetail(configNo));
    }


    /**
     * 分页查询打卡配置的用户列表
     */
    @PostMapping("/page/applyUser")
    public Result<PaginationResult<RuleConfigUserInfoDTO>> pageApplyUser(@RequestBody PunchConfigUserQuery query) {
        return Result.ok(punchConfigService.pagePunchConfigUserList(query));
    }


    /**
     * 添加
     */
    @PostMapping("/add")
    public Result<RuleConfigChangeCheckDTO> add(@RequestBody PunchConfigAddCommand addCommand) {
        return Result.ok(punchConfigService.add(addCommand));
    }

    /**
     * 检查打卡规则更新的影响范围，分析规则变更受影响的用户数量
     */
    @PostMapping("/checkUpdateRule")
    public Result<UpdateRuleReflectResult> checkUpdateRule(@RequestBody @Validated PunchConfigUpdateCommand updateCommand) {
        return Result.ok(punchConfigService.checkUpdateRule(updateCommand));
    }

    /**
     * 更新
     * @param updateCommand 包含更新信息的命令对象
     * @return 操作结果
     */
    @PostMapping("/update")
    public Result<RuleConfigChangeCheckDTO> update(@RequestBody @Validated PunchConfigUpdateCommand updateCommand) {
        return Result.ok(punchConfigService.update(updateCommand));
    }


    /**
     * 检查打卡规则状态切换的影响范围,分析状态变更（启用/停用）对用户范围的影响
     */
    @PostMapping("/checkStatusSwitch")
    public Result<UpdateRuleReflectResult> checkStatusSwitch(@RequestBody @Validated PunchConfigStatusSwitchCommand statusSwitchCommand) {
        return Result.ok(punchConfigService.checkStatusSwitch(statusSwitchCommand));
    }

    /**
     * 启用或停用
     */
    @PostMapping("/statusSwitch")
    public Result<RuleConfigChangeCheckDTO> statusSwitch(@RequestBody @Validated PunchConfigStatusSwitchCommand statusSwitchCommand) {
        return Result.ok(punchConfigService.statusSwitch(statusSwitchCommand));
    }
}
