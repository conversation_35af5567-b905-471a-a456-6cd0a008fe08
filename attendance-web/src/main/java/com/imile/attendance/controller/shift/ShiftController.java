package com.imile.attendance.controller.shift;

import com.imile.attendance.annon.ExportParamFill;
import com.imile.attendance.controller.BaseController;
import com.imile.attendance.infrastructure.excel.header.ExcelTitleExportDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigSelectDTO;
import com.imile.attendance.infrastructure.repository.shift.dto.UserDayShiftRuleDTO;
import com.imile.attendance.infrastructure.repository.shift.dto.UserShiftConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.query.UserShiftConfigQuery;
import com.imile.attendance.shift.application.ShiftApplicationService;
import com.imile.attendance.shift.command.BatchShiftCommand;
import com.imile.attendance.shift.command.CancelCycleShiftCommand;
import com.imile.attendance.shift.command.CycleShiftCommand;
import com.imile.attendance.shift.command.UserShiftConfigAddCommand;
import com.imile.attendance.shift.dto.UserShiftCheckResultDTO;
import com.imile.attendance.shift.dto.UserShiftImportDTO;
import com.imile.attendance.shift.param.BatchShiftUserShiftRuleParam;
import com.imile.attendance.shift.param.CheckCustomShiftParam;
import com.imile.attendance.shift.param.CheckCycleShiftParam;
import com.imile.attendance.shift.param.CycleShiftUserShiftRuleParam;
import com.imile.attendance.shift.param.UserDayShiftRuleParam;
import com.imile.attendance.shift.query.UserArchiveShiftQuery;
import com.imile.attendance.shift.vo.UserArchiveShiftVO;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> chen
 * @Date 2025/4/21
 * @Description
 */
@RestController
@RequestMapping("/shift")
public class ShiftController extends BaseController {

    @Resource
    private ShiftApplicationService applicationService;

    /**
     * 分页
     */
    @PostMapping("/page")
    public Result<PaginationResult<UserShiftConfigDTO>> page(@RequestBody UserShiftConfigQuery query) {
        return Result.ok(applicationService.page(query));
    }

    /**
     * 根据班次类型下拉选择班次列表
     */
    @GetMapping("/queryClassSelects")
    public Result<List<PunchClassConfigSelectDTO>> queryClassSelects(String classNature) {
        return Result.ok(applicationService.queryClassSelects(classNature));
    }

    /**
     * 查询批量排班的用户的排班规则
     */
    @PostMapping("/queryBatchShiftUserShiftRule")
    public Result<List<UserDayShiftRuleDTO>> queryBatchShiftUserShiftRule(@RequestBody BatchShiftUserShiftRuleParam shiftRuleParam) {
        return Result.ok(applicationService.queryBatchShiftUserShiftRule(shiftRuleParam));
    }

    /**
     * 查询循环排班的用户的排班规则
     */
    @PostMapping("/queryCycleShiftUserShiftRuleParam")
    public Result<List<UserDayShiftRuleDTO>> queryCycleShiftUserShiftRuleParam(@RequestBody CycleShiftUserShiftRuleParam shiftRuleParam) {
        return Result.ok(applicationService.queryCycleShiftUserShiftRuleParam(shiftRuleParam));
    }

    /**
     * 根据用户id和dayId 获取当天可选择的排班规则（班次，OFF，H）
     */
    @PostMapping("/queryUserDayShiftRule")
    public Result<List<UserDayShiftRuleDTO>> queryUserDayShiftRule(@RequestBody UserDayShiftRuleParam userDayShiftRuleParam) {
        return Result.ok(applicationService.queryUserDayShiftRule(userDayShiftRuleParam));
    }

    /**
     * 页面手动排班
     */
    @PostMapping("/addShift")
    public Result<Void> addShift(@RequestBody @Validated UserShiftConfigAddCommand addCommand){
        addCommand.setFromPage(Boolean.TRUE);
        applicationService.addShift(addCommand);
        return Result.ok();
    }

    /**
     * 检查批量排班
     */
    @PostMapping("/checkBatchShift")
    public Result<UserShiftCheckResultDTO> checkBatchShift(@RequestBody CheckCustomShiftParam checkCustomShiftParam) {
        return Result.ok(applicationService.checkBatchShift(checkCustomShiftParam));
    }

    /**
     * 页面批量排班
     */
    @PostMapping("/batchShift")
    public Result<Void> batchShift(@RequestBody @Validated BatchShiftCommand batchShiftCommand){
        batchShiftCommand.setFromPage(Boolean.TRUE);
        applicationService.batchShift(batchShiftCommand);
        return Result.ok();
    }

    /**
     * 检查循环排班
     */
    @PostMapping("/checkCycleShift")
    public Result<UserShiftCheckResultDTO> checkCycleShift(@RequestBody CheckCycleShiftParam param) {
        return Result.ok(applicationService.checkCycleShift(param));
    }

    /**
     * 循环排班
     */
    @PostMapping("/cycleShift")
    public Result<Void> cycleShift(@RequestBody @Validated CycleShiftCommand cycleShiftCommand){
        cycleShiftCommand.setFromPage(Boolean.TRUE);
        applicationService.cycleShift(cycleShiftCommand);
        return Result.ok();
    }

    /**
     * 取消循环排班
     */
    @PostMapping("/cancelCycleShift")
    public Result<Void> cancelCycleShift(@RequestBody @Validated CancelCycleShiftCommand cancelCycleShiftCommand){
        applicationService.cancelCycleShift(cancelCycleShiftCommand);
        return Result.ok();
    }

    /**
     * 排班规则导入 ruleId:700007
     */
    @PostMapping("/import")
    public Result<List<UserShiftImportDTO>> shiftImport(HttpServletRequest request) {
        return Result.ok(applicationService.shiftImport(request));
    }

    /**
     * 排班导出excel的表头
     */
    @PostMapping("/title/export")
    public Result<List<ExcelTitleExportDTO>> titleExport(@RequestBody UserShiftConfigQuery query) {
        return Result.ok(applicationService.titleExport(query));
    }

    /**
     * 排班导出 ruleId:700008 (表头调用/title/export接口)
     */
    @PostMapping("/export")
    @ExportParamFill
    public Result<PaginationResult<Map<String, String>>> shiftExport(UserShiftConfigQuery query) {
        return Result.ok(applicationService.shiftExport(query));
    }

    /**
     * 员工考勤档案排班情况
     */
    @PostMapping("/attendance/archive")
    public Result<UserArchiveShiftVO> shiftAttendanceArchive(@RequestBody @Validated UserArchiveShiftQuery query) {
        return Result.ok(applicationService.shiftAttendanceArchive(query));
    }
}
