package com.imile.attendance.controller.third;

import com.imile.attendance.controller.BaseController;
import com.imile.attendance.infrastructure.repository.third.query.ZktecoAreaRelationQueryDTO;
import com.imile.attendance.third.ZktecoAreaSnRelationService;
import com.imile.attendance.third.dto.ZktecoAreaRelationDTO;
import com.imile.attendance.third.param.ZktecoAreaRelationListParam;
import com.imile.attendance.third.param.ZktecoAreaRelationUpdateParam;
import com.imile.attendance.third.vo.ZktecoAreaRelationVO;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.util.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> chen
 * @Date 2025/3/26 
 * @Description 中控通
 */
@RestController
@RequestMapping("/zkteco")
public class ZktecoAreaSnRelationController extends BaseController {

    @Resource
    private ZktecoAreaSnRelationService zktecoAreaSnRelationService;

    /**
     * 考勤机关联信息查询
     */
    @PostMapping("/relation/list")
    public Result<PaginationResult<ZktecoAreaRelationVO>> zktecoAreaRelationList(@RequestBody ZktecoAreaRelationListParam param) {
        ZktecoAreaRelationQueryDTO queryDTO = BeanUtils.convert(param, ZktecoAreaRelationQueryDTO.class);
        PaginationResult<ZktecoAreaRelationDTO> pageResult = zktecoAreaSnRelationService.zktecoAreaRelationList(queryDTO);
        PaginationResult<ZktecoAreaRelationVO> relationVOPaginationResult = this.convertPage(pageResult, ZktecoAreaRelationVO.class);
        return Result.ok(relationVOPaginationResult);
    }

    /**
     * 更新考勤机关联信息
     */
    @PostMapping("/relation/update")
    public Result<Boolean> zktecoAreaRelationUpdate(@RequestBody ZktecoAreaRelationUpdateParam param) {
        return Result.ok(zktecoAreaSnRelationService.zktecoAreaRelationUpdate(
                param.getId(), param.getCountry(),param.getDeptIdList(), param.getUserIdList()));
    }

    /**
     * 同步考勤机区域和机器编码的关联信息到考勤
     */
    @PostMapping("/relation/sync")
    public Result<Boolean> zktecoAreaRelationSync() {
        return Result.ok(zktecoAreaSnRelationService.zktecoAreaRelationSync());
    }

}
