package com.imile.attendance.controller.user;

import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.user.UserService;
import com.imile.attendance.user.vo.UserInfoVO;
import com.imile.attendance.user.vo.UserOptionVO;
import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.result.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.List;


/**
 * 考勤人员服务
 *
 * <AUTHOR>
 * @since 2025/4/19
 */
@RestController
@RequestMapping("/attendance/user")
public class AttendanceUserController {
    @Resource
    private UserService userService;

    /**
     * 获取人员联想列表
     * 最多返回满足条件的前50条
     */
    @PostMapping("/associate/list")
    public Result<List<UserOptionVO>> getUserAssociateList(@RequestBody UserDaoQuery query) {
        return Result.ok(userService.getUserAssociateList(query));
    }


    /**
     * 获取人员信息
     *
     * @param userCode 人员编码
     */
    @GetMapping("/info")
    public Result<UserInfoVO> getUserInfo(@NotBlank(message = ValidCodeConstant.NOT_NULL) @RequestParam String userCode) {
        return Result.ok(userService.getUserInfo(userCode));
    }
}
