package com.imile.attendance.controller.user;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.annon.ExportParamFill;
import com.imile.attendance.archive.query.AttendanceArchiveListQuery;
import com.imile.attendance.controller.BaseController;
import com.imile.attendance.user.UserLeaveIpepService;
import com.imile.attendance.user.UserLeaveService;
import com.imile.attendance.user.command.UserLeaveUpdateParam;
import com.imile.attendance.user.convert.UserLeaveConvert;
import com.imile.attendance.user.dto.UserAvailableLeaveDTO;
import com.imile.attendance.user.dto.UserLeaveBalanceImportDTO;
import com.imile.attendance.user.dto.UserLeaveDTO;
import com.imile.attendance.user.dto.UserLeaveInfoDTO;
import com.imile.attendance.user.dto.UserLeaveRecordDTO;
import com.imile.attendance.user.dto.UserLeaveUpdateDTO;
import com.imile.attendance.user.query.UserLeaveDetailInfoQuery;
import com.imile.attendance.user.query.UserLeaveInfoQuery;
import com.imile.attendance.user.query.UserLeavePageQuery;
import com.imile.attendance.user.query.UserLeaveRecordPageQuery;
import com.imile.attendance.user.vo.UserLeaveExportVO;
import com.imile.attendance.user.vo.UserLeaveInfoVO;
import com.imile.attendance.user.vo.UserLeaveRecordVO;
import com.imile.attendance.user.vo.UserLeaveVO;
import com.imile.common.excel.ExcelCallBackParam;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 员工假期余额详情相关接口
 *
 * <AUTHOR>
 * @menu 假期余额
 * @date 2025/5/8
 */
@Slf4j
@RestController
@RequestMapping("/user/leave")
public class UserLeaveController extends BaseController {

    @Resource
    private UserLeaveService userLeaveService;
    @Resource
    private UserLeaveIpepService userLeaveIpepService;

    /**
     * 查询该员工还可以请的假期类型(有改假期，并且有假期余额)
     *
     * @return
     */
    @PostMapping("/own/type")
    public Result<List<String>> selectUserLeaveType(@RequestBody UserLeaveInfoQuery query) {
        List<UserAvailableLeaveDTO> availableLeaveDTOList = userLeaveService.selectUserOwnLeaveType(query.getUserId(), query.getUserCode());
        List<String> userLeaverTypeList = availableLeaveDTOList.stream().map(record -> record.getLeaveName()).collect(Collectors.toList());
        return Result.ok(userLeaverTypeList);
    }

    /**
     * 员工假期分页列表查询
     *
     * @param query
     * @return
     */
    @PostMapping("/list")
    public Result<PaginationResult<UserLeaveVO>> list(@RequestBody UserLeavePageQuery query) {
        PaginationResult<UserLeaveDTO> list = userLeaveService.list(query);
        PaginationResult<UserLeaveVO> resultVO = this.convertPage(list, UserLeaveVO.class);
        return Result.ok(resultVO);
    }


    /**
     * 员工假期余额详情
     */
    @PostMapping("/detail")
    public Result<UserLeaveInfoVO> detail(@RequestBody UserLeaveDetailInfoQuery query) {
        UserLeaveInfoDTO detail = userLeaveService.detail(query);
        UserLeaveInfoVO vo = UserLeaveConvert.convertFromDTO(detail);
        return Result.ok(vo);
    }

    /**
     * 员工假期余额修改
     */
    @PostMapping("/update")
    public Result<Boolean> update(@RequestBody UserLeaveUpdateParam param) {
        UserLeaveUpdateDTO updateDTO = UserLeaveConvert.convertFromParam(param);
        return Result.ok(userLeaveService.update(updateDTO));
    }

    /**
     * 员工假期余额记录
     */
    @PostMapping("/record")
    public Result<PaginationResult<UserLeaveRecordVO>> record(@RequestBody UserLeaveRecordPageQuery query) {
        PaginationResult<UserLeaveRecordDTO> result = userLeaveService.record(query);
        PaginationResult<UserLeaveRecordVO> resultVO = this.convertPage(result, UserLeaveRecordVO.class);
        return Result.ok(resultVO);
    }

    /**
     * 员工假期余额导入
     */
    @PostMapping("/import")
    public Result<List<UserLeaveBalanceImportDTO>> leaveBalanceImport(HttpServletRequest request) {
        ExcelCallBackParam callBackParam = new ExcelCallBackParam(request);

        log.info("------->import leave balance jobId:{}", callBackParam.getJobId());
        long startTime = System.currentTimeMillis();

        List<UserLeaveBalanceImportDTO> importList = JSON.parseArray(callBackParam.getPageData(), UserLeaveBalanceImportDTO.class);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(importList)) {
            return Result.ok(Collections.emptyList());
        }

        List<UserLeaveBalanceImportDTO> failImportList = userLeaveIpepService.leaveBalanceImport(importList);
        log.info(" ------------ import leave balance,spendTime:{}ms --------------", System.currentTimeMillis() - startTime);

        return Result.ok(failImportList);
    }

    /**
     * 员工假期余额导出
     *
     * @return
     */
    @PostMapping("/export")
    @ExportParamFill
    public Result<PaginationResult<UserLeaveExportVO>> export(HttpServletRequest request,
                                                              AttendanceArchiveListQuery query) {
        setExcelCallBackParam(request, query);
        PaginationResult<UserLeaveExportVO> result = userLeaveIpepService.leaveBalanceExport(query);
        return Result.ok(result);
    }

}
