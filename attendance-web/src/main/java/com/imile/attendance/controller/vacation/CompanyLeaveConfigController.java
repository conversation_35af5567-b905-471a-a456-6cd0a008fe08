package com.imile.attendance.controller.vacation;

import com.imile.attendance.controller.BaseController;
import com.imile.attendance.vacation.application.CompanyLeaveConfigApplicationService;
import com.imile.attendance.vacation.command.CompanyLeaveConfigAddCommand;
import com.imile.attendance.vacation.command.CompanyLeaveConfigStatusUpdateCommand;
import com.imile.attendance.vacation.command.CompanyLeaveConfigUpdateCommand;
import com.imile.attendance.vacation.dto.CompanyLeaveDetailDTO;
import com.imile.attendance.vacation.query.CompanyLeaveConfigDetailQuery;
import com.imile.attendance.vacation.query.CompanyLeaveConfigQuery;
import com.imile.attendance.vacation.vo.CompanyLeaveConfigVO;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 假期规则-福利假相关
 *
 * <AUTHOR>
 * @menu 假期规则
 * @date 2025/4/16
 */
@Slf4j
@RestController
@RequestMapping("/welfare/leave/config")
public class CompanyLeaveConfigController extends BaseController {

    @Resource
    private CompanyLeaveConfigApplicationService companyLeaveConfigApplicationService;

    /**
     * 公司假期配置分页查询
     *
     * @param param 查询参数
     * @return 分页结果
     */
    @PostMapping("list")
    public Result<PaginationResult<CompanyLeaveDetailDTO>> list(@RequestBody @Validated CompanyLeaveConfigQuery param) {
        PaginationResult<CompanyLeaveDetailDTO> list = companyLeaveConfigApplicationService.list(param);
        return Result.ok(list);
    }

    /**
     * 新增福利假期配置
     *
     * @return 是否成功
     */
    @PostMapping("add")
    public Result<Boolean> add(@RequestBody @Validated CompanyLeaveConfigAddCommand param) {
        return Result.ok(companyLeaveConfigApplicationService.add(param));
    }

    /**
     * 福利假期配置详情
     *
     * @param param 查询参数
     * @return 详情
     */
    @PostMapping("detail")
    public Result<CompanyLeaveConfigVO> detail(@RequestBody @Validated CompanyLeaveConfigDetailQuery param) {
        CompanyLeaveConfigVO leaveConfigVO = companyLeaveConfigApplicationService.detail(param);
        return Result.ok(leaveConfigVO);
    }

    /**
     * 福利假期配置更新
     *
     * @param updateCommand 更新参数
     * @return 是否成功
     */
    @PostMapping("/update")
    public Result<Boolean> update(@RequestBody @Validated CompanyLeaveConfigUpdateCommand updateCommand) {
        return Result.ok(companyLeaveConfigApplicationService.update(updateCommand));
    }

    /**
     * 福利假期配置更新状态
     *
     * @param updateCommand 更新参数
     * @return 是否成功
     */
    @PostMapping("/update/status")
    public Result<Boolean> updateStatus(@RequestBody @Validated CompanyLeaveConfigStatusUpdateCommand updateCommand) {
        return Result.ok(companyLeaveConfigApplicationService.updateStatus(updateCommand));
    }

}
