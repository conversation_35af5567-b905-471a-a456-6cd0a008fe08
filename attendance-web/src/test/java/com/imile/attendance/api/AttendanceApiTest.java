package com.imile.attendance.api;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.report.AttendanceReportApi;
import com.imile.attendance.report.param.AttendanceMonthReportParam;
import com.imile.attendance.util.DateHelper;
import com.imile.rpc.common.RpcResult;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class AttendanceApiTest extends BaseTest {

    @Resource
    private AttendanceReportApi reportApi;


    @Test
    public void testMonthReportApi() {
        AttendanceMonthReportParam param = new AttendanceMonthReportParam();
        String date = "2025-06-10";
        param.setStartTime(DateHelper.parseYYYYMMDD(date));
        param.setEndTime(new Date());
        param.setUserCodeList(Arrays.asList("2103499001"));
        RpcResult<List<Map<String, String>>> listRpcResult = reportApi.selectAttendanceMonthReport(param);
        System.out.println("result:" + listRpcResult.getResult());
    }
}
