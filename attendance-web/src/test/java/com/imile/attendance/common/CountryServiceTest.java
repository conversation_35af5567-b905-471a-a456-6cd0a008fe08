package com.imile.attendance.common;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> chen
 * @Date 2025/4/28 
 * @Description
 */
public class CountryServiceTest extends BaseTest {

    @Resource
    private CountryService countryService;

    @Test
    public void teslistAllCountry(){
        List<CountryDTO> countryDTOS = countryService.listAllCountry();
        Optional.ofNullable(countryDTOS).ifPresent(System.out::println);
    }
}
