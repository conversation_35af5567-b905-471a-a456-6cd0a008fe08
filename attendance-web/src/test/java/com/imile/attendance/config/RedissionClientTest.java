package com.imile.attendance.config;

import com.imile.attendance.base.BaseTest;
import org.junit.Test;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;

import javax.annotation.Resource;

/**
 * <AUTHOR> chen
 * @Date 2025/08/06
 * @Time 09:43
 * @Description
 */
public class RedissionClientTest extends BaseTest {

    @Resource
    private RedissonClient redissonClient;

    @Test
    public void testCodec() {
        Codec codec = redissonClient.getConfig().getCodec();
        System.out.println(codec.getClass().getName());
    }
}
