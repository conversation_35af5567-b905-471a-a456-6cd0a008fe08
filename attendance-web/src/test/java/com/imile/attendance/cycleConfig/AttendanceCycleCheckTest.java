package com.imile.attendance.cycleConfig;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.imile.attendance.base.BaseTest;
import com.imile.attendance.cycleConfig.dto.AttendanceCycleCheckResult;
import com.imile.attendance.cycleConfig.enums.AttendanceCycleTypeEnum;
import com.imile.attendance.cycleConfig.enums.CycleTypeEnum;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import org.junit.Before;
import org.junit.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.doReturn;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
public class AttendanceCycleCheckTest extends BaseTest {

    @SpyBean
    private AttendanceCycleConfigService cycleConfigService;

    @Before
    public void setUp() {
//        AttendanceCycleConfigDO mockCycleConfig = buildNonFullMonthCycleConfig();
        AttendanceCycleConfigDO mockCycleConfig = buildFullMonthCycleConfig();
        doReturn(mockCycleConfig).when(cycleConfigService).getUserAttendanceCycleConfig(anyLong());
    }

    public AttendanceCycleConfigDO buildNonFullMonthCycleConfig() {
        AttendanceCycleConfigDO cycleConfig = new AttendanceCycleConfigDO();
        cycleConfig.setCycleType(AttendanceCycleTypeEnum.MONTH.getType());
        cycleConfig.setCycleStart("26");
        cycleConfig.setCycleEnd("25");
        cycleConfig.setAbnormalExpired(2);
        return cycleConfig;
    }

    public AttendanceCycleConfigDO buildFullMonthCycleConfig() {
        AttendanceCycleConfigDO cycleConfig = new AttendanceCycleConfigDO();
        cycleConfig.setCycleType(AttendanceCycleTypeEnum.MONTH.getType());
        cycleConfig.setCycleStart("1");
        cycleConfig.setCycleEnd("END_OF_MONTH");
        cycleConfig.setAbnormalExpired(2);
        return cycleConfig;
    }


    @Test
    public void testCheckDateInUserAttendanceCycle_FullMonth() {
        // 准备测试数据
        Long userId = 1001L;
//        Date dateToCheck = DateUtil.parse("2025-06-30", DatePattern.NORM_DATE_PATTERN);
        Date dateToCheck = DateUtil.parse("2025-07-01", DatePattern.NORM_DATE_PATTERN);

        // 执行测试
        AttendanceCycleCheckResult result = cycleConfigService.checkDateInUserAttendanceCycle(userId, dateToCheck);

        // 验证结果
        System.out.println(result);
    }

    @Test
    public void testgetCycleDetailList() {
        List<CycleTypeEnum.CycleDetail> cycleDetailList = cycleConfigService.getCycleDetailList(CycleTypeEnum.MONTH.name());
        Optional.ofNullable(cycleDetailList).ifPresent(System.out::println);

        List<CycleTypeEnum.CycleDetail> weekCycleDetailList = cycleConfigService.getCycleDetailList(CycleTypeEnum.WEEK.name());
        Optional.ofNullable(weekCycleDetailList).ifPresent(System.out::println);
    }
}
