package com.imile.attendance.cycleConfig;

import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.cycleConfig.command.AttendanceCycleConfigAddCommand;
import com.imile.attendance.cycleConfig.command.AttendanceCycleConfigUpdateCommand;
import com.imile.attendance.cycleConfig.enums.AttendanceCycleTypeEnum;
import com.imile.attendance.cycleConfig.enums.CycleTypeEnum;
import com.imile.attendance.cycleConfig.factory.AttendanceCycleConfigFactory;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.cycleConfig.dao.AttendanceCycleConfigDao;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
public class AttendanceCycleConfigFactoryTest {

    @Mock
    private AttendanceCycleConfigDao attendanceCycleConfigDao;

    @Mock
    private DefaultIdWorker defaultIdWorker;

    @InjectMocks
    private AttendanceCycleConfigFactory attendanceCycleConfigFactory;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        // 模拟 ID 生成
        when(defaultIdWorker.nextId()).thenReturn(1L);
    }

    @Test
    public void test() {
        System.out.println("test");
    }

    // 正常场景：测试有效的月维度配置
    @Test
    public void testAddValidMonthlyConfig() {
        // 准备测试数据
        AttendanceCycleConfigAddCommand command = new AttendanceCycleConfigAddCommand();
        command.setCountry("USA");
        command.setCycleType(AttendanceCycleTypeEnum.MONTH.getType());
        command.setCycleStart("1");
        command.setAbnormalExpired(2);

        // 模拟 DAO 行为 - 没有重复配置
        when(attendanceCycleConfigDao.selectByCondition(any())).thenReturn(new ArrayList<>());

        // 执行测试
        attendanceCycleConfigFactory.add(command);

        // 验证
        verify(attendanceCycleConfigDao, times(1)).save(any(AttendanceCycleConfigDO.class));
        assertEquals("MONTH", command.getCycleTypeString()); // 验证周期类型字符串已设置
    }

    // 正常场景：测试MEX国家的周维度配置
    @Test
    public void testAddValidWeeklyConfigForMEX() {
        // 准备测试数据
        AttendanceCycleConfigAddCommand command = new AttendanceCycleConfigAddCommand();
        command.setCountry(CountryCodeEnum.MEX.getCode());
        command.setCycleType(AttendanceCycleTypeEnum.WEEK.getType());
        command.setCycleStart("MONDAY"); // 周一
        command.setAbnormalExpired(2);

        // 模拟 DAO 行为 - 没有重复配置
        when(attendanceCycleConfigDao.selectByCondition(any())).thenReturn(new ArrayList<>());

        // 执行测试
        attendanceCycleConfigFactory.add(command);

        // 验证
        verify(attendanceCycleConfigDao, times(1)).save(any(AttendanceCycleConfigDO.class));
        assertEquals("WEEK", command.getCycleTypeString()); // 验证周期类型字符串已设置
    }

    // 正常场景：测试BRA国家的周维度配置
    @Test
    public void testAddValidWeeklyConfigForBRA() {
        // 准备测试数据
        AttendanceCycleConfigAddCommand command = new AttendanceCycleConfigAddCommand();
        command.setCountry(CountryCodeEnum.BRA.getCode());
        command.setCycleType(AttendanceCycleTypeEnum.WEEK.getType());
        command.setCycleStart("MONDAY"); // 周一
        command.setAbnormalExpired(2);

        // 模拟 DAO 行为 - 没有重复配置
        when(attendanceCycleConfigDao.selectByCondition(any())).thenReturn(new ArrayList<>());

        // 执行测试
        attendanceCycleConfigFactory.add(command);

        // 验证
        verify(attendanceCycleConfigDao, times(1)).save(any(AttendanceCycleConfigDO.class));
        assertEquals("WEEK", command.getCycleTypeString()); // 验证周期类型字符串已设置
    }

    // 异常场景：测试非MEX/BRA国家的周维度配置
    @Test
    public void testAddWeeklyConfigForUnsupportedCountry() {
        // 准备测试数据
        AttendanceCycleConfigAddCommand command = new AttendanceCycleConfigAddCommand();
        command.setCountry("USA");
        command.setCycleType(AttendanceCycleTypeEnum.WEEK.getType());
        command.setCycleStart("MONDAY");
        command.setAbnormalExpired(2);

        // 模拟 RequestInfoHolder 行为
        // 使用 Mockito.mockStatic 而不是 mockStatic
        try (MockedStatic<RequestInfoHolder> mockedStatic = Mockito.mockStatic(RequestInfoHolder.class)) {
            mockedStatic.when(RequestInfoHolder::isChinese).thenReturn(true);
            // 使用 Mockito.mockStatic 而不是 mockStatic
            try (MockedStatic<CountryCodeEnum> mockedCountry = Mockito.mockStatic(CountryCodeEnum.class)) {
                mockedCountry.when(() -> CountryCodeEnum.getCountryName(Mockito.eq("USA"), Mockito.eq(true)))
                        .thenReturn("美国");
                // 执行测试并验证异常
                BusinessException exception = assertThrows(BusinessException.class, () -> {
                    attendanceCycleConfigFactory.add(command);
                });
                System.out.println(exception);
            }
        }
    }

    // 异常场景：测试重复配置
    @Test
    public void testAddDuplicateConfig() {
        // 准备测试数据
        AttendanceCycleConfigAddCommand command = new AttendanceCycleConfigAddCommand();
        command.setCountry("USA");
        command.setCycleType(AttendanceCycleTypeEnum.MONTH.getType());
        command.setCycleStart("1");
        command.setAbnormalExpired(2);

        // 模拟已存在的配置
        List<AttendanceCycleConfigDO> existingConfigs = new ArrayList<>();
        AttendanceCycleConfigDO existingConfig = new AttendanceCycleConfigDO();
        existingConfig.setId(2L);
        existingConfig.setCountry("USA");
        existingConfig.setCycleType(AttendanceCycleTypeEnum.MONTH.getType());
        existingConfigs.add(existingConfig);

        // 模拟 DAO 行为 - 返回重复配置
        when(attendanceCycleConfigDao.selectByCondition(any())).thenReturn(existingConfigs);

        // 模拟 RequestInfoHolder 行为
        try (MockedStatic<RequestInfoHolder> mockedStatic = mockStatic(RequestInfoHolder.class)) {
            mockedStatic.when(RequestInfoHolder::isChinese).thenReturn(true);

            // 模拟 CountryCodeEnum 和 AttendanceCycleTypeEnum 行为
            try (MockedStatic<CountryCodeEnum> mockedCountry = mockStatic(CountryCodeEnum.class);
                 MockedStatic<AttendanceCycleTypeEnum> mockedCycleType = mockStatic(AttendanceCycleTypeEnum.class)) {

                mockedCountry.when(() -> CountryCodeEnum.getCountryName(eq("USA"), eq(true)))
                        .thenReturn("美国");

                AttendanceCycleTypeEnum monthEnum = AttendanceCycleTypeEnum.MONTH;
                mockedCycleType.when(() -> AttendanceCycleTypeEnum.getByType(eq(AttendanceCycleTypeEnum.MONTH.getType())))
                        .thenReturn(monthEnum);

                // 执行测试并验证异常
                BusinessException exception = assertThrows(BusinessException.class, () -> {
                    attendanceCycleConfigFactory.add(command);
                });

                System.out.println(exception.getCode());
            }
        }
    }


    // 正常场景：测试有效更新
    @Test
    public void testUpdateValidConfig() {
        // 准备测试数据
        AttendanceCycleConfigUpdateCommand command = new AttendanceCycleConfigUpdateCommand();
        command.setId(1L);
        command.setCountry("USA");
        command.setCycleType(AttendanceCycleTypeEnum.MONTH.getType());
        command.setCycleStart("1");
        command.setAbnormalExpired(3); // 修改异常过期时间

        // 模拟旧配置
        AttendanceCycleConfigDO oldConfig = new AttendanceCycleConfigDO();
        oldConfig.setId(1L);
        oldConfig.setCountry("USA");
        oldConfig.setCycleType(AttendanceCycleTypeEnum.MONTH.getType());
        oldConfig.setCycleStart("1");
        oldConfig.setAbnormalExpired(2);
        oldConfig.setStatus(StatusEnum.ACTIVE.getCode());

        // 模拟 DAO 行为
        when(attendanceCycleConfigDao.getById(1L)).thenReturn(oldConfig);
        when(attendanceCycleConfigDao.selectByCondition(any())).thenReturn(new ArrayList<>());

        // 执行测试
        attendanceCycleConfigFactory.update(command);

        // 验证
        verify(attendanceCycleConfigDao, times(1)).saveAndUpdateAttendanceCycleConfig(any(AttendanceCycleConfigDO.class), any(AttendanceCycleConfigDO.class));
        assertEquals(IsDeleteEnum.YES.getCode(), oldConfig.getIsDelete()); // 验证旧配置已标记为删除
    }

    // 异常场景：测试 ID 为空
    @Test
    public void testUpdateWithNullId() {
        // 准备测试数据
        AttendanceCycleConfigUpdateCommand command = new AttendanceCycleConfigUpdateCommand();
        command.setCountry("USA");
        command.setCycleType(AttendanceCycleTypeEnum.MONTH.getType());
        command.setCycleStart("1");
        command.setAbnormalExpired(2);
        // ID 为空

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            attendanceCycleConfigFactory.update(command);
        });

        System.out.println(exception.getCode());
    }

    // 异常场景：测试配置不存在
    @Test
    public void testUpdateNonExistentConfig() {
        // 准备测试数据
        AttendanceCycleConfigUpdateCommand command = new AttendanceCycleConfigUpdateCommand();
        command.setId(1L);
        command.setCountry("USA");
        command.setCycleType(AttendanceCycleTypeEnum.MONTH.getType());
        command.setCycleStart("1");
        command.setAbnormalExpired(2);

        // 模拟 DAO 行为 - 配置不存在
        when(attendanceCycleConfigDao.getById(1L)).thenReturn(null);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            attendanceCycleConfigFactory.update(command);
        });

        System.out.println(exception.getCode());
    }

    // 异常场景：测试更新为重复配置
    @Test
    public void testUpdateToDuplicateConfig() {
        // 准备测试数据
        AttendanceCycleConfigUpdateCommand command = new AttendanceCycleConfigUpdateCommand();
        command.setId(1L);
        command.setCountry("USA");
        command.setCycleType(AttendanceCycleTypeEnum.MONTH.getType());
        command.setCycleStart("1");
        command.setAbnormalExpired(2);

        // 模拟旧配置
        AttendanceCycleConfigDO oldConfig = new AttendanceCycleConfigDO();
        oldConfig.setId(1L);
        oldConfig.setCountry("USA");
        oldConfig.setCycleType(AttendanceCycleTypeEnum.MONTH.getType());
        oldConfig.setCycleStart("1");
        oldConfig.setAbnormalExpired(2);
        oldConfig.setStatus(StatusEnum.ACTIVE.getCode());

        // 模拟已存在的其他配置
        List<AttendanceCycleConfigDO> existingConfigs = new ArrayList<>();
        AttendanceCycleConfigDO existingConfig = new AttendanceCycleConfigDO();
        existingConfig.setId(2L);
        existingConfig.setCountry("USA");
        existingConfig.setCycleType(AttendanceCycleTypeEnum.MONTH.getType());
        existingConfigs.add(existingConfig);

        // 模拟 DAO 行为
        when(attendanceCycleConfigDao.getById(1L)).thenReturn(oldConfig);
        when(attendanceCycleConfigDao.selectByCondition(any())).thenReturn(existingConfigs);

        // 模拟 RequestInfoHolder 行为
        try (MockedStatic<RequestInfoHolder> mockedStatic = Mockito.mockStatic(RequestInfoHolder.class)) {
            mockedStatic.when(RequestInfoHolder::isChinese).thenReturn(true);

            // 模拟 CountryCodeEnum 和 AttendanceCycleTypeEnum 行为
            try (MockedStatic<CountryCodeEnum> mockedCountry = Mockito.mockStatic(CountryCodeEnum.class);
                 MockedStatic<AttendanceCycleTypeEnum> mockedCycleType = Mockito.mockStatic(AttendanceCycleTypeEnum.class)) {

                mockedCountry.when(() -> CountryCodeEnum.getCountryName(Mockito.eq("USA"), Mockito.eq(true)))
                        .thenReturn("美国");

                AttendanceCycleTypeEnum monthEnum = AttendanceCycleTypeEnum.MONTH;
                mockedCycleType.when(() -> AttendanceCycleTypeEnum.getByType(Mockito.eq(AttendanceCycleTypeEnum.MONTH.getType())))
                        .thenReturn(monthEnum);

                // 执行测试并验证异常
                BusinessException exception = assertThrows(BusinessException.class, () -> {
                    attendanceCycleConfigFactory.update(command);
                });

                System.out.println(exception.getCode());
            }
        }
    }

    // 异常场景：测试非法周期类型
    @Test
    public void testUpdateWithInvalidCycleType() {
        // 准备测试数据 - 尝试将美国配置更新为周维度
        AttendanceCycleConfigUpdateCommand command = new AttendanceCycleConfigUpdateCommand();
        command.setId(1L);
        command.setCountry("USA"); // 非 MEX/BRA 国家
        command.setCycleType(AttendanceCycleTypeEnum.WEEK.getType()); // 周维度
        command.setCycleStart("MONDAY");
        command.setAbnormalExpired(2);

        // 模拟旧配置
        AttendanceCycleConfigDO oldConfig = new AttendanceCycleConfigDO();
        oldConfig.setId(1L);
        oldConfig.setCountry("USA");
        oldConfig.setCycleType(AttendanceCycleTypeEnum.MONTH.getType());
        oldConfig.setCycleStart("1");
        oldConfig.setAbnormalExpired(2);
        oldConfig.setStatus(StatusEnum.ACTIVE.getCode());

        // 模拟 DAO 行为
        when(attendanceCycleConfigDao.getById(1L)).thenReturn(oldConfig);
        when(attendanceCycleConfigDao.selectByCondition(any())).thenReturn(new ArrayList<>());

        // 模拟 RequestInfoHolder 行为
        try (MockedStatic<RequestInfoHolder> mockedStatic = Mockito.mockStatic(RequestInfoHolder.class)) {
            mockedStatic.when(RequestInfoHolder::isChinese).thenReturn(true);

            // 模拟 CountryCodeEnum 行为
            try (MockedStatic<CountryCodeEnum> mockedCountry = Mockito.mockStatic(CountryCodeEnum.class)) {
                mockedCountry.when(() -> CountryCodeEnum.getCountryName(Mockito.eq("USA"), Mockito.eq(true)))
                        .thenReturn("美国");

                // 执行测试并验证异常
                BusinessException exception = assertThrows(BusinessException.class, () -> {
                    attendanceCycleConfigFactory.update(command);
                });

                System.out.println(exception.getCode());
            }
        }
    }

    @Test
    public void testBuildDefaultAttendanceCycleConfig() {
        // Create an instance of the factory
        AttendanceCycleConfigFactory factory = new AttendanceCycleConfigFactory();

        // Call the method to build default config
        AttendanceCycleConfigDO defaultConfig = factory.buildDefaultAttendanceCycleConfig();

        // Verify all default values are set correctly
        Assertions.assertNotNull(defaultConfig, "Default config should not be null");
        assertEquals(AttendanceCycleTypeEnum.MONTH.getType(), defaultConfig.getCycleType(),
                "Default cycle type should be MONTH");
        assertEquals(BusinessConstant.ONE.toString(), defaultConfig.getCycleStart(),
                "Default cycle start should be 1");
        assertEquals(CycleTypeEnum.END_OF_MONTH_CODE, defaultConfig.getCycleEnd(),
                "Default cycle end should be end of month");
        assertEquals(2, defaultConfig.getAbnormalExpired(),
                "Default abnormal expired should be 2");
    }

}
