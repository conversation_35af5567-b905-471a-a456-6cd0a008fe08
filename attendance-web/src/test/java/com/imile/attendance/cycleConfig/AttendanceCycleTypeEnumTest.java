package com.imile.attendance.cycleConfig;

import com.imile.attendance.cycleConfig.enums.AttendanceCycleTypeEnum;
import com.imile.attendance.enums.CountryCodeEnum;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
public class AttendanceCycleTypeEnumTest {


    @Test
    void testGetByType() {
        // Test valid types
        assertEquals(AttendanceCycleTypeEnum.MONTH, AttendanceCycleTypeEnum.getByType(1));
        assertEquals(AttendanceCycleTypeEnum.WEEK, AttendanceCycleTypeEnum.getByType(2));
        assertEquals(AttendanceCycleTypeEnum.DEFAULT, AttendanceCycleTypeEnum.getByType(0));

        // Test null and invalid type
        assertNull(AttendanceCycleTypeEnum.getByType(null));
        assertNull(AttendanceCycleTypeEnum.getByType(99));
    }

    @Test
    void testGetCycleTypeString() {
        // Test valid types
        assertEquals("MONTH", AttendanceCycleTypeEnum.getCycleTypeString(1));
        assertEquals("WEEK", AttendanceCycleTypeEnum.getCycleTypeString(2));
        assertEquals("DEFAULT", AttendanceCycleTypeEnum.getCycleTypeString(0));

        // Test null and invalid type
        assertNull(AttendanceCycleTypeEnum.getCycleTypeString(null));
        assertNull(AttendanceCycleTypeEnum.getCycleTypeString(99));
    }

    @Test
    void testGetCycleTypeByCountry() {
        // 测试使用周维度的国家
        assertEquals(Integer.valueOf(2), AttendanceCycleTypeEnum.getCycleTypeByCountry(CountryCodeEnum.MEX.getCode()));
        assertEquals(Integer.valueOf(2), AttendanceCycleTypeEnum.getCycleTypeByCountry(CountryCodeEnum.BRA.getCode()));

        // 测试使用月维度的国家
        assertEquals(Integer.valueOf(1), AttendanceCycleTypeEnum.getCycleTypeByCountry(CountryCodeEnum.CHN.getCode()));
        assertEquals(Integer.valueOf(1), AttendanceCycleTypeEnum.getCycleTypeByCountry(CountryCodeEnum.USA.getCode()));
        assertEquals(Integer.valueOf(1), AttendanceCycleTypeEnum.getCycleTypeByCountry("ANY_OTHER_COUNTRY"));

        // 测试边界情况
        assertEquals(Integer.valueOf(1), AttendanceCycleTypeEnum.getCycleTypeByCountry(null));
        assertEquals(Integer.valueOf(1), AttendanceCycleTypeEnum.getCycleTypeByCountry(""));
    }

    @Test
    void testEnumValues() {
        // Test enum properties
        assertEquals(0, AttendanceCycleTypeEnum.DEFAULT.getType());
        assertEquals("", AttendanceCycleTypeEnum.DEFAULT.getDesc());
        assertEquals("", AttendanceCycleTypeEnum.DEFAULT.getDescEn());

        assertEquals(1, AttendanceCycleTypeEnum.MONTH.getType());
        assertEquals("月", AttendanceCycleTypeEnum.MONTH.getDesc());
        assertEquals("month", AttendanceCycleTypeEnum.MONTH.getDescEn());

        assertEquals(2, AttendanceCycleTypeEnum.WEEK.getType());
        assertEquals("周", AttendanceCycleTypeEnum.WEEK.getDesc());
        assertEquals("week", AttendanceCycleTypeEnum.WEEK.getDescEn());
    }
}
