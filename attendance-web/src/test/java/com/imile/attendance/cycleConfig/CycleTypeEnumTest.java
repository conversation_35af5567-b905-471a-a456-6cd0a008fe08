package com.imile.attendance.cycleConfig;

import cn.hutool.core.date.DateUtil;
import com.imile.attendance.cycleConfig.enums.CycleTypeEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import static org.junit.jupiter.api.Assertions.*;

import java.util.Date;
import java.util.List;
import java.math.BigDecimal;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
public class CycleTypeEnumTest {

    private Date testDate;

    @BeforeEach
    public void setUp() {
        // 假设今天是2023年5月15日
        testDate = DateUtil.parse("2023-05-15");
    }

    //====================== 测试 MONTH 枚举 ======================

    @Test
    public void testMonthGetCycleDetailList() {
        List<CycleTypeEnum.CycleDetail> details = CycleTypeEnum.MONTH.getCycleDetailList();

        // 验证返回了正确数量的天数 (28天)
        assertEquals(28, details.size());

        // 验证第一天的数据
        CycleTypeEnum.CycleDetail firstDay = details.get(0);
        assertEquals("1", firstDay.getCycleStart());
        assertTrue(firstDay.getCycleStartDesc().contains("1"));

        // 验证最后一天的数据
        CycleTypeEnum.CycleDetail lastDay = details.get(27);
        assertEquals("28", lastDay.getCycleStart());
        assertTrue(lastDay.getCycleStartDesc().contains("28"));
    }

    @Test
    public void testMonthGetCycleDate() {
        // 测试当月15号
        Date result1 = CycleTypeEnum.MONTH.getCycleDate(testDate, "15", 0);
        assertEquals("2023-05-15", DateUtil.format(result1, "yyyy-MM-dd"));

        // 测试下月15号 (offset=1)
        Date result2 = CycleTypeEnum.MONTH.getCycleDate(testDate, "15", 1);
        assertEquals("2023-06-15", DateUtil.format(result2, "yyyy-MM-dd"));

        // 测试上月15号 (offset=-1)
        Date result3 = CycleTypeEnum.MONTH.getCycleDate(testDate, "15", -1);
        assertEquals("2023-04-15", DateUtil.format(result3, "yyyy-MM-dd"));

        // 测试月底
        Date result4 = CycleTypeEnum.MONTH.getCycleDate(testDate, "END_OF_MONTH", 0);
        assertEquals("2023-05-31", DateUtil.format(result4, "yyyy-MM-dd"));
    }

    @Test
    public void testMonthGetCurrentRange_FullMonth() {
        // 测试整月周期 (1号到月底)
        CycleTypeEnum.CycleRangDetail range = CycleTypeEnum.MONTH.getCurrentRange(
                testDate, "1", "END_OF_MONTH");

        // 验证范围是当月1号到当月最后一天
        assertEquals("2023-05-01", DateUtil.format(range.getCycleStartDate(), "yyyy-MM-dd"));
        assertEquals("2023-05-31", DateUtil.format(range.getCycleEndDate(), "yyyy-MM-dd"));
    }

    @Test
    public void testMonthGetCurrentRange_CrossMonth_CurrentInRange() {
        // 测试跨月周期 (10号到下月9号), 当前日期在周期中
        Date testDate = DateUtil.parse("2023-05-15"); // 15号在10-下月9日范围内
        CycleTypeEnum.CycleRangDetail range = CycleTypeEnum.MONTH.getCurrentRange(
                testDate, "10", "9");

        // 验证范围是当月10号到下月9号
        assertEquals("2023-05-10", DateUtil.format(range.getCycleStartDate(), "yyyy-MM-dd"));
        assertEquals("2023-06-09", DateUtil.format(range.getCycleEndDate(), "yyyy-MM-dd"));
    }

    @Test
    public void testMonthGetCurrentRange_CrossMonth_CurrentBeforeRange() {
        // 测试跨月周期 (20号到下月19号), 当前日期在周期开始前
        Date testDate = DateUtil.parse("2023-05-15"); // 15号在20-下月19日范围外
        CycleTypeEnum.CycleRangDetail range = CycleTypeEnum.MONTH.getCurrentRange(
                testDate, "20", "19");

        // 验证范围是上月20号到当月19号
        assertEquals("2023-04-20", DateUtil.format(range.getCycleStartDate(), "yyyy-MM-dd"));
        assertEquals("2023-05-19", DateUtil.format(range.getCycleEndDate(), "yyyy-MM-dd"));
    }

    @Test
    public void testMonthGetCycleEndDetail() {
        // 测试月底
        CycleTypeEnum.CycleDetail endDetail1 = CycleTypeEnum.MONTH.getCycleEndDetail("END_OF_MONTH");
        assertEquals("END_OF_MONTH", endDetail1.getCycleStart());
        assertTrue(endDetail1.getCycleStartDesc().contains("月底") || endDetail1.getCycleStartDesc().contains("End of this month"));

        // 测试月初
        CycleTypeEnum.CycleDetail endDetail2 = CycleTypeEnum.MONTH.getCycleEndDetail("1");
        assertEquals("1", endDetail2.getCycleStart());
        assertEquals("END_OF_MONTH", endDetail2.getCycleEnd());

        // 测试普通日期
        CycleTypeEnum.CycleDetail endDetail3 = CycleTypeEnum.MONTH.getCycleEndDetail("15");
        assertEquals("15", endDetail3.getCycleStart());
        assertEquals("14", endDetail3.getCycleEnd());
    }

    @Test
    public void testMonthGetActualAbnormalExpired() {
        // 测试当前日期在周期开始日之前（跳月）
        Date beforeDate = DateUtil.parse("2023-05-10");
        Integer result1 = CycleTypeEnum.MONTH.getActualAbnormalExpired(beforeDate, "15", "14", 2);
        assertEquals(-2, result1);
        System.out.println(CycleTypeEnum.MONTH.getCycleDate(beforeDate, "15", result1));

        // 测试当前日期在周期开始日之后
        Date afterDate = DateUtil.parse("2023-05-20");
        Integer result2 = CycleTypeEnum.MONTH.getActualAbnormalExpired(afterDate, "15", "14", 2);
        assertEquals(-1, result2);
        System.out.println(CycleTypeEnum.MONTH.getCycleDate(afterDate, "15", result2));
    }

    //====================== 测试 WEEK 枚举 ======================

    @Test
    public void testWeekGetCycleDetailList() {
        List<CycleTypeEnum.CycleDetail> details = CycleTypeEnum.WEEK.getCycleDetailList();

        // 验证返回了7天
        assertEquals(7, details.size());

        // 验证星期日的数据
        CycleTypeEnum.CycleDetail sunday = details.get(0);
        assertEquals("SUNDAY", sunday.getCycleStart());
        assertTrue(sunday.getCycleStartDesc().equals("周日") || sunday.getCycleStartDesc().equals("Sunday"));

        // 验证星期六的数据
        CycleTypeEnum.CycleDetail saturday = details.get(6);
        assertEquals("SATURDAY", saturday.getCycleStart());
        assertTrue(saturday.getCycleStartDesc().equals("周六") || saturday.getCycleStartDesc().equals("Saturday"));
    }

    @Test
    public void testWeekGetCycleDate() {
        // 测试当周的周一
        Date result1 = CycleTypeEnum.WEEK.getCycleDate(testDate, "MONDAY", 0);
        assertEquals("2023-05-15", DateUtil.format(result1, "yyyy-MM-dd"));

        // 测试下周的周一 (offset=1)
        Date result2 = CycleTypeEnum.WEEK.getCycleDate(testDate, "MONDAY", 1);
        assertEquals("2023-05-22", DateUtil.format(result2, "yyyy-MM-dd"));

        // 测试上周的周一 (offset=-1)
        Date result3 = CycleTypeEnum.WEEK.getCycleDate(testDate, "MONDAY", -1);
        assertEquals("2023-05-08", DateUtil.format(result3, "yyyy-MM-dd"));
    }

    @Test
    public void testWeekGetCurrentRange() {
        // 测试周一到周五的周期
        CycleTypeEnum.CycleRangDetail range = CycleTypeEnum.WEEK.getCurrentRange(
                testDate, "MONDAY", "FRIDAY");

        // 验证范围是本周一到本周五
        // 注意：具体日期需要根据testDate所在的实际周调整
        assertNotNull(range);
        assertNotNull(range.getCycleStartDate());
        assertNotNull(range.getCycleEndDate());
    }

    @Test
    public void testWeekGetCycleEndDetail() {
        // 测试周一
        CycleTypeEnum.CycleDetail endDetail1 = CycleTypeEnum.WEEK.getCycleEndDetail("MONDAY");
        assertEquals("MONDAY", endDetail1.getCycleStart());
        assertEquals("SUNDAY", endDetail1.getCycleEnd());

        // 测试周日
        CycleTypeEnum.CycleDetail endDetail2 = CycleTypeEnum.WEEK.getCycleEndDetail("SUNDAY");
        assertEquals("SUNDAY", endDetail2.getCycleStart());
        assertEquals("SATURDAY", endDetail2.getCycleEnd());
    }

    @Test
    public void testWeekGetActualAbnormalExpired() {
        Integer result = CycleTypeEnum.WEEK.getActualAbnormalExpired(testDate, "MONDAY", "SUNDAY",5);
        assertEquals(-4, result);
    }

    //====================== 测试静态方法 ======================

    @Test
    public void testGetInstance() {
        assertEquals(CycleTypeEnum.MONTH, CycleTypeEnum.getInstance("MONTH"));
        assertEquals(CycleTypeEnum.WEEK, CycleTypeEnum.getInstance("WEEK"));
        assertNull(CycleTypeEnum.getInstance("INVALID"));
    }

    @Test
    public void testGetCycleDetail() {
        // 测试有效参数
        CycleTypeEnum.CycleDetail detail = CycleTypeEnum.getCycleDetail("MONTH", "15");
        assertEquals("15", detail.getCycleStart());
        assertEquals("14", detail.getCycleEnd());

        // 测试无效参数 (这将抛出一个异常)
        // 由于可能涉及到业务异常处理，这里使用assertThrows
        assertThrows(Exception.class, () -> {
            CycleTypeEnum.getCycleDetail("INVALID", "15");
        });
    }
}
