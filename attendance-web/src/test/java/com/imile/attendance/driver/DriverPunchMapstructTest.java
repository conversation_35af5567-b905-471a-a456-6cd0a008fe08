package com.imile.attendance.driver;

import com.imile.attendance.driver.mapstruct.DriverPunchMapstruct;
import com.imile.attendance.driver.vo.DriverAttendanceOperateRecordVO;
import com.imile.attendance.enums.DriverAttendanceOperateRecordEnum;
import com.imile.attendance.infrastructure.repository.driver.model.DriverAttendanceOperateRecordDO;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description
 */
public class DriverPunchMapstructTest {

    private final DriverPunchMapstruct mapper = Mappers.getMapper(DriverPunchMapstruct.class);

    @Test
    void toOperateRecordVO_WithEmptyList_ShouldReturnEmptyList() {
        // Given
        List<DriverAttendanceOperateRecordDO> emptyList = Collections.emptyList();

        // When
        List<DriverAttendanceOperateRecordVO> result = mapper.toOperateRecordVO(emptyList);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void toOperateRecordVO_WithValidRecords_ShouldMapCorrectly() {
        // Given
        DriverAttendanceOperateRecordDO record1 = new DriverAttendanceOperateRecordDO();
        record1.setId(1L);
        record1.setOperationType(DriverAttendanceOperateRecordEnum.MODIFY_ATTENDANCE.getType());
        record1.setOperationContent("Content 1");
        record1.setOperationContentEn("Content 1");

        DriverAttendanceOperateRecordDO record2 = new DriverAttendanceOperateRecordDO();
        record2.setId(2L);
        record2.setOperationType(DriverAttendanceOperateRecordEnum.EXPORT.getType());
        record2.setOperationContent("Content 2");
        record2.setOperationContentEn("Content 2");

        List<DriverAttendanceOperateRecordDO> records = Arrays.asList(record1, record2);

        // When
        List<DriverAttendanceOperateRecordVO> result = mapper.toOperateRecordVO(records);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());

        DriverAttendanceOperateRecordVO vo1 = result.get(0);
        assertEquals(1L, vo1.getId());
        assertNotNull(vo1.getOperationTypeString());
        assertNotNull(vo1.getOperationContentView());
        System.out.println("vo1.getOperationTypeString() = " + vo1.getOperationTypeString());
        System.out.println("vo1.getOperationContentView() =" + vo1.getOperationContentView());

        DriverAttendanceOperateRecordVO vo2 = result.get(1);
        assertEquals(2L, vo2.getId());
        assertNotNull(vo2.getOperationTypeString());
        assertNotNull(vo2.getOperationContentView());
        System.out.println("vo2.getOperationTypeString() = " + vo2.getOperationTypeString());
        System.out.println("vo2.getOperationContentView() =" + vo2.getOperationContentView());
    }
}
