package com.imile.attendance.employee;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.employee.repository.EmployeeRepository;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Optional;

public class EmployeeRepositoryTest extends BaseTest {

    @Resource
    private EmployeeRepository employeeRepository;


    @Test
    public void testGetByCode() {
        Employee employee = employeeRepository.getEmployeeByCode("21032401");
        System.out.println(employee);
        Optional.ofNullable(employee).ifPresent(
                e -> System.out.println(e.isWarehouse())
        );
    }
}
