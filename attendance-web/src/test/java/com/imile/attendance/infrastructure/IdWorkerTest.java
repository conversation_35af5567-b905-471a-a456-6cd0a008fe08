package com.imile.attendance.infrastructure;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.enums.NoPrefixEnum;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import org.junit.Test;

import javax.annotation.Resource;
import java.io.Serializable;

import static org.junit.jupiter.api.Assertions.*;

public class IdWorkerTest extends BaseTest {

    @Resource
    private DefaultIdWorker idWorker;

    @Test
    public void testNextId() {
        long id = idWorker.nextId();
        System.out.println(id);
        assertTrue(id > 0);
    }

    @Test
    public void testNextNo() {
        String no = idWorker.nextNo(NoPrefixEnum.ATTENDANCE);
        System.out.println(no);
        assertNotNull(no);
        assertTrue(no.startsWith(NoPrefixEnum.ATTENDANCE.getPrefix()));
    }

    @Test
    public void testNextAttendanceConfigNo() {
        String no = idWorker.nextAttendanceConfigNo();
        System.out.println(no);
        assertNotNull(no);
        assertTrue(no.startsWith(NoPrefixEnum.ATTENDANCE.getPrefix()));
    }

    @Test
    public void testNextPunchConfigNo() {
        String no = idWorker.nextPunchConfigNo();
        System.out.println(no);
        assertNotNull(no);
        assertTrue(no.startsWith(NoPrefixEnum.PUNCH_CONFIG.getPrefix()));
    }

    @Test
    public void testUuid() {
        String uuid = idWorker.uuid();
        System.out.println(uuid);
        assertNotNull(uuid);
        assertEquals(32, uuid.length());
    }

    @Test
    public void testRecordVersion() {
        Serializable bizId = "testBizId";
        Long version = idWorker.recordVersion(bizId);
        System.out.println(version);
        assertNotNull(version);
        assertTrue(version > 0);
    }
}
