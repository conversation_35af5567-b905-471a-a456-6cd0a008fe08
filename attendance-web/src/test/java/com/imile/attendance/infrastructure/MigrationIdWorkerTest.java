package com.imile.attendance.infrastructure;

import com.imile.attendance.base.BaseTest;
import com.imile.idwork.IdWorkerUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashSet;
import java.util.Set;

import static org.junit.Assert.*;

/**
 * 迁移模式下的IdWorker测试
 * 
 * <AUTHOR> chen
 * @date 2025/6/20
 */
public class MigrationIdWorkerTest extends BaseTest {

    @Test
    public void testMigrationModeIdGeneration() {
        // 测试迁移模式下ID生成的正确性
        assertNotNull("IdWorker应该已经初始化", IdWorkerUtil.idWorker);
        
        // 验证DataCenterId在正确范围内[3,5]
        long datacenterId = IdWorkerUtil.idWorker.getDatacenterId();
        assertTrue("新系统DataCenterId应该在[3,5]范围内，当前值: " + datacenterId, 
                  datacenterId >= 3 && datacenterId <= 5);
        
        // 验证WorkerId在正确范围内[0,31]
        long workerId = IdWorkerUtil.idWorker.getWorkerId();
        assertTrue("WorkerId应该在[0,31]范围内，当前值: " + workerId, 
                  workerId >= 0 && workerId <= 31);
        
        System.out.println("迁移模式验证通过 - DataCenterId: " + datacenterId + ", WorkerId: " + workerId);
    }

    @Test
    public void testIdUniqueness() {
        // 测试ID唯一性
        Set<Long> generatedIds = new HashSet<>();
        int testCount = 10000;
        
        for (int i = 0; i < testCount; i++) {
            Long id = IdWorkerUtil.getId();
            assertNotNull("生成的ID不能为null", id);
            assertTrue("生成的ID必须为正数", id > 0);
            
            // 检查ID唯一性
            assertFalse("检测到重复ID: " + id, generatedIds.contains(id));
            generatedIds.add(id);
        }
        
        System.out.println("唯一性测试通过，生成了 " + testCount + " 个唯一ID");
    }

    @Test
    public void testIdStructure() {
        for (int i = 0; i < 1000; i++) {
            // 测试ID结构的正确性
            Long id = IdWorkerUtil.getId();

            // 解析雪花ID的各个部分
            long datacenterId = (id >> 17) & 0x1F;  // 5位DataCenterId
            long workerId = (id >> 12) & 0x1F;      // 5位WorkerId
            long sequence = id & 0xFFF;             // 12位序列号

            // 验证DataCenterId在新系统范围内
            assertTrue("从ID中解析的DataCenterId应该在[3,5]范围内，当前值: " + datacenterId,
                    datacenterId >= 3 && datacenterId <= 5);

            // 验证WorkerId在有效范围内
            assertTrue("从ID中解析的WorkerId应该在[0,31]范围内，当前值: " + workerId,
                    workerId >= 0 && workerId <= 31);

            // 验证序列号在有效范围内
            assertTrue("从ID中解析的序列号应该在[0,4095]范围内，当前值: " + sequence,
                    sequence >= 0 && sequence <= 4095);

            System.out.println("ID结构验证通过 - ID: " + id +
                    ", DataCenterId: " + datacenterId +
                    ", WorkerId: " + workerId +
                    ", Sequence: " + sequence);
            System.out.println("=============");
        }

    }

    @Test
    public void testIdGenerationPerformance() {
        // 测试ID生成性能
        int testCount = 100000;
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < testCount; i++) {
            IdWorkerUtil.getId();
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        double idsPerSecond = (double) testCount / duration * 1000;
        
        System.out.println("性能测试结果 - 生成 " + testCount + " 个ID耗时: " + duration + "ms");
        System.out.println("平均每秒生成ID数量: " + String.format("%.0f", idsPerSecond));
        
        // 验证性能满足要求（每秒至少10万个ID）
        assertTrue("ID生成性能应该满足要求（每秒至少10万个）", idsPerSecond >= 100000);
    }


    /**
     * 增强测试1：跨时间窗口的唯一性测试
     */
    @Test
    public void testCrossTimeWindowUniqueness() throws InterruptedException {
        Set<Long> allIds = new HashSet<>();
        int batchSize = 1000;
        int batchCount = 5;

        for (int batch = 0; batch < batchCount; batch++) {
            // 每批次间隔一段时间
            if (batch > 0) {
                Thread.sleep(10); // 10ms间隔，确保跨越不同毫秒
            }

            Set<Long> batchIds = new HashSet<>();

            // 在短时间内生成一批ID
            for (int i = 0; i < batchSize; i++) {
                Long id = IdWorkerUtil.getId();

                // 检查批次内唯一性
                assertFalse("批次" + batch + "内发现重复ID: " + id, batchIds.contains(id));
                batchIds.add(id);

                // 检查全局唯一性
                assertFalse("全局发现重复ID: " + id, allIds.contains(id));
                allIds.add(id);
            }

            System.out.println("批次 " + batch + " 完成，生成 " + batchSize + " 个唯一ID");
        }

        System.out.println("跨时间窗口唯一性测试通过，总计生成 " + allIds.size() + " 个唯一ID");
    }
}
