package com.imile.attendance.infrastructure.repository.calendar;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.infrastructure.repository.calendar.mapper.BaseDayInfoMapper;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7 
 * @Description
 */
public class BaseDayInfoTest extends BaseTest {

    @Resource
    private BaseDayInfoMapper baseDayInfoMapper;


    @Test
    public void testlistCycle(){
        System.out.println(baseDayInfoMapper.listCycle(2025));
    }
}
