package com.imile.attendance.infrastructure.repository.hrms;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserInfoDao;
import org.junit.Test;
import org.junit.jupiter.api.RepeatedTest;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * <AUTHOR> chen
 * @Date 2025/3/26 
 * @Description
 */
public class HrmsUserInfoTest extends BaseTest {

    @Resource
    private HrmsUserInfoDao hrmsUserInfoDao;

    @Test
    public void test() {
        System.out.println(hrmsUserInfoDao.getById(887405151751512065L));
        System.out.println(hrmsUserInfoDao.getById(887361920821100545L));
        System.out.println(hrmsUserInfoDao.getById(887374273251901441L));
        System.out.println(hrmsUserInfoDao.getById(887426868771291136L));
    }

    @Test
    public void testPage() {
        System.out.println(hrmsUserInfoDao.listByPage(1, 10));
    }

    //getByUserId
    @Test
    public void testGetByUserId() {
        System.out.println(hrmsUserInfoDao.getByUserId(917198626990587904L));
    }

    //getByUserCodeCache
    @Test
    @RepeatedTest(10)
    public void testGetByUserCodeCache() {
        System.out.println(hrmsUserInfoDao.getByUserCodeCache("21032382"));
    }

    //listUsersByIds
    @Test
    public void testListUsersByIds() {
        System.out.println(hrmsUserInfoDao.listUsersByIds(Arrays.asList(917198626990587904L, 930545667200651264L)));
    }
}
