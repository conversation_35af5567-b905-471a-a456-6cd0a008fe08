package com.imile.attendance.infrastructure.repository.log;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.infrastructure.repository.log.dao.LogOperationRecordDao;
import com.imile.attendance.infrastructure.repository.log.dto.LogRecordPageDTO;
import com.imile.attendance.infrastructure.repository.log.mapper.LogOperationRecordMapper;
import com.imile.attendance.infrastructure.repository.log.model.LogOperationRecordDO;
import com.imile.attendance.infrastructure.repository.log.query.LogRecordPageQuery;
import com.imile.attendance.infrastructure.repository.log.dao.LogOperationRecordDao;
import com.imile.attendance.infrastructure.repository.log.mapper.LogOperationRecordMapper;
import com.imile.attendance.infrastructure.repository.log.model.LogOperationRecordDO;
import com.imile.attendance.infrastructure.repository.log.query.LogRecordPageQuery;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31 
 * @Description
 */
public class LogOperationRecordTest extends BaseTest {

    @Resource
    private LogOperationRecordDao logOperationRecordDao;
    @Resource
    private LogOperationRecordMapper logOperationRecordMapper;

    @Test
    public void test1(){
        LogRecordPageQuery logRecordPageQuery = new LogRecordPageQuery();
        logRecordPageQuery.setOperationModule("CALENDAR_MODULE");
        List<LogOperationRecordDO> list = logOperationRecordDao.listPage(logRecordPageQuery);
        System.out.println(list);
    }

    @Test
    public void test2(){
        LogRecordPageQuery logRecordPageQuery = new LogRecordPageQuery();
        logRecordPageQuery.setOperationModule("ATTENDANCE_RULE_MODULE");
        List<LogOperationRecordDO> logOperationRecordDOS = logOperationRecordMapper.listPage(logRecordPageQuery);
        Optional.ofNullable(logOperationRecordDOS).ifPresent(System.out::println);
    }
}
