package com.imile.attendance.migration;

import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.migration.dto.HrAttendanceClassItemConfigDTO;
import com.imile.attendance.migration.util.ClassTimeUtil;
import com.imile.attendance.util.DateHelper;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Test;

import java.math.BigDecimal;

import static org.junit.Assert.*;

/**
 * ClassTimeUtil 工具类单元测试
 * 
 * <AUTHOR> chen
 * @Date 2025/6/16
 * @Description 测试班次时间计算工具类的各种场景
 */
public class ClassTimeUtilTest {

    @Test
    public void test_calculateWorkingHours_正常工作时间_无休息时间() {
        // 准备测试数据：9:00-18:00，无休息时间
        HrAttendanceClassItemConfigDTO hrItem = createHrItem("09:00:00", "18:00:00", null, null, false);
        
        // 执行测试
        Pair<BigDecimal, BigDecimal> result = ClassTimeUtil.calculateWorkingHours(hrItem);
        
        // 验证结果：出勤时长和法定工作时长都应该是9小时
        assertEquals("出勤时长应为9小时", new BigDecimal("9.00"), result.getLeft());
        assertEquals("法定工作时长应为9小时", new BigDecimal("9.00"), result.getRight());
    }

    @Test
    public void test_calculateWorkingHours_正常工作时间_有休息时间() {
        // 准备测试数据：9:00-18:00，12:00-13:00休息
        HrAttendanceClassItemConfigDTO hrItem = createHrItem("09:00:00", "18:00:00", "12:00:00", "13:00:00", false);
        
        // 执行测试
        Pair<BigDecimal, BigDecimal> result = ClassTimeUtil.calculateWorkingHours(hrItem);
        
        // 验证结果：出勤时长9小时，法定工作时长8小时（减去1小时休息）
        assertEquals("出勤时长应为9小时", new BigDecimal("9.00"), result.getLeft());
        assertEquals("法定工作时长应为8小时", new BigDecimal("8.00"), result.getRight());
    }

    @Test
    public void test_calculateWorkingHours_跨天工作时间_无休息时间() {
        // 准备测试数据：22:00-06:00（跨天），无休息时间
        HrAttendanceClassItemConfigDTO hrItem = createHrItem("22:00:00", "06:00:00", null, null, true);
        
        // 执行测试
        Pair<BigDecimal, BigDecimal> result = ClassTimeUtil.calculateWorkingHours(hrItem);
        
        // 验证结果：出勤时长和法定工作时长都应该是8小时
        assertEquals("出勤时长应为8小时", new BigDecimal("8.00"), result.getLeft());
        assertEquals("法定工作时长应为8小时", new BigDecimal("8.00"), result.getRight());
    }

    @Test
    public void test_calculateWorkingHours_跨天工作时间_有休息时间() {
        // 准备测试数据：22:00-07:00（跨天），02:00-03:00休息
        HrAttendanceClassItemConfigDTO hrItem = createHrItem("22:00:00", "07:00:00", "02:00:00", "03:00:00", true);
        
        // 执行测试
        Pair<BigDecimal, BigDecimal> result = ClassTimeUtil.calculateWorkingHours(hrItem);
        
        // 验证结果：出勤时长9小时，法定工作时长8小时（减去1小时休息）
        assertEquals("出勤时长应为9小时", new BigDecimal("9.00"), result.getLeft());
        assertEquals("法定工作时长应为8小时", new BigDecimal("8.00"), result.getRight());
    }

    @Test
    public void test_calculateWorkingHours_上班时间为空_返回零值() {
        // 准备测试数据：上班时间为空
        HrAttendanceClassItemConfigDTO hrItem = createHrItem(null, "18:00:00", null, null, false);
        
        // 执行测试
        Pair<BigDecimal, BigDecimal> result = ClassTimeUtil.calculateWorkingHours(hrItem);
        
        // 验证结果：都应该返回0
        assertEquals("出勤时长应为0", BigDecimal.ZERO, result.getLeft());
        assertEquals("法定工作时长应为0", BigDecimal.ZERO, result.getRight());
    }

    @Test
    public void test_calculateWorkingHours_下班时间为空_返回零值() {
        // 准备测试数据：下班时间为空
        HrAttendanceClassItemConfigDTO hrItem = createHrItem("09:00:00", null, null, null, false);
        
        // 执行测试
        Pair<BigDecimal, BigDecimal> result = ClassTimeUtil.calculateWorkingHours(hrItem);
        
        // 验证结果：都应该返回0
        assertEquals("出勤时长应为0", BigDecimal.ZERO, result.getLeft());
        assertEquals("法定工作时长应为0", BigDecimal.ZERO, result.getRight());
    }

    @Test(expected = BusinessLogicException.class)
    public void test_calculateWorkingHours_休息时间超过工作时间_抛出异常() {
        // 准备测试数据：9:00-10:00工作，但休息时间是8:00-12:00（超过工作时间）
        HrAttendanceClassItemConfigDTO hrItem = createHrItem("09:00:00", "10:00:00", "08:00:00", "12:00:00", false);
        
        // 执行测试，应该抛出BusinessLogicException异常
        ClassTimeUtil.calculateWorkingHours(hrItem);
    }

    @Test
    public void test_calculateWorkingHours_休息时间不合理_抛出异常() {
        // 准备测试数据：9:00-18:00工作，但休息时间在工作时间外（19:00-20:00）
        HrAttendanceClassItemConfigDTO hrItem = createHrItem("09:00:00", "18:00:00", "19:00:00", "20:00:00", false);
        
        // 执行测试
        ClassTimeUtil.calculateWorkingHours(hrItem);
    }

    @Test
    public void test_calculateWorkingHours_休息时间跨天_正常计算() {
        // 准备测试数据：20:00-08:00（跨天工作），23:00-01:00休息（跨天休息）
        HrAttendanceClassItemConfigDTO hrItem = createHrItem("20:00:00", "08:00:00", "23:00:00", "01:00:00", true);
        
        // 执行测试
        Pair<BigDecimal, BigDecimal> result = ClassTimeUtil.calculateWorkingHours(hrItem);
        
        // 验证结果：出勤时长12小时，法定工作时长10小时（减去2小时休息）
        assertEquals("出勤时长应为12小时", new BigDecimal("12.00"), result.getLeft());
        assertEquals("法定工作时长应为10小时", new BigDecimal("10.00"), result.getRight());
    }

    @Test
    public void test_calculateWorkingHours_边界值测试_最短工作时间() {
        // 准备测试数据：9:00-9:01，1分钟工作时间
        HrAttendanceClassItemConfigDTO hrItem = createHrItem("09:00:00", "09:01:00", null, null, false);
        
        // 执行测试
        Pair<BigDecimal, BigDecimal> result = ClassTimeUtil.calculateWorkingHours(hrItem);
        
        // 验证结果：出勤时长和法定工作时长都应该是0.02小时（1分钟）
        assertEquals("出勤时长应为0.02小时", new BigDecimal("0.02"), result.getLeft());
        assertEquals("法定工作时长应为0.02小时", new BigDecimal("0.02"), result.getRight());
    }

    @Test
    public void test_calculateWorkingHours_边界值测试_24小时工作() {
        // 准备测试数据：00:00-00:00（跨天，24小时工作）
        HrAttendanceClassItemConfigDTO hrItem = createHrItem("00:00:00", "00:00:00", null, null, true);
        
        // 执行测试
        Pair<BigDecimal, BigDecimal> result = ClassTimeUtil.calculateWorkingHours(hrItem);
        
        // 验证结果：出勤时长和法定工作时长都应该是24小时
        assertEquals("出勤时长应为24小时", new BigDecimal("24.00"), result.getLeft());
        assertEquals("法定工作时长应为24小时", new BigDecimal("24.00"), result.getRight());
    }

    @Test
    public void test_calculateWorkingHours_休息时间为零_正常计算() {
        // 准备测试数据：9:00-18:00工作，12:00-12:00休息（休息时间为0）
        HrAttendanceClassItemConfigDTO hrItem = createHrItem("09:00:00", "18:00:00", "12:00:00", "12:00:00", false);
        
        // 执行测试
        Pair<BigDecimal, BigDecimal> result = ClassTimeUtil.calculateWorkingHours(hrItem);
        
        // 验证结果：由于休息时间为0（不合理），应该忽略休息时间
        assertEquals("出勤时长应为9小时", new BigDecimal("9.00"), result.getLeft());
        assertEquals("法定工作时长应为9小时", new BigDecimal("9.00"), result.getRight());
    }

    @Test
    public void test_calculateWorkingHours_休息时间超过8小时_抛出异常() {
        // 准备测试数据：8:00-20:00工作，10:00-19:00休息（9小时休息，超过8小时限制）
        HrAttendanceClassItemConfigDTO hrItem = createHrItem("08:00:00", "20:00:00", "10:00:00", "19:00:00", false);

        // 执行测试
        ClassTimeUtil.calculateWorkingHours(hrItem);
    }

    @Test
    public void test_calculateWorkingHours_只有休息开始时间_忽略休息时间() {
        // 准备测试数据：9:00-18:00工作，只有休息开始时间12:00:00，没有结束时间
        HrAttendanceClassItemConfigDTO hrItem = createHrItem("09:00:00", "18:00:00", "12:00:00", null, false);

        // 执行测试
        Pair<BigDecimal, BigDecimal> result = ClassTimeUtil.calculateWorkingHours(hrItem);

        // 验证结果：由于休息时间不完整，应该忽略休息时间
        assertEquals("出勤时长应为9小时", new BigDecimal("9.00"), result.getLeft());
        assertEquals("法定工作时长应为9小时", new BigDecimal("9.00"), result.getRight());
    }

    @Test
    public void test_calculateWorkingHours_只有休息结束时间_忽略休息时间() {
        // 准备测试数据：9:00-18:00工作，只有休息结束时间13:00:00，没有开始时间
        HrAttendanceClassItemConfigDTO hrItem = createHrItem("09:00:00", "18:00:00", null, "13:00:00", false);

        // 执行测试
        Pair<BigDecimal, BigDecimal> result = ClassTimeUtil.calculateWorkingHours(hrItem);

        // 验证结果：由于休息时间不完整，应该忽略休息时间
        assertEquals("出勤时长应为9小时", new BigDecimal("9.00"), result.getLeft());
        assertEquals("法定工作时长应为9小时", new BigDecimal("9.00"), result.getRight());
    }

    @Test
    public void test_calculateWorkingHours_复杂跨天场景_休息时间跨天但工作不跨天() {
        // 准备测试数据：8:00-20:00工作（不跨天），23:00-01:00休息（跨天）
        HrAttendanceClassItemConfigDTO hrItem = createHrItem("08:00:00", "20:00:00", "23:00:00", "01:00:00", false);

        // 执行测试
        Pair<BigDecimal, BigDecimal> result = ClassTimeUtil.calculateWorkingHours(hrItem);

        // 验证结果：由于休息时间在工作时间外，应该忽略休息时间
        assertEquals("出勤时长应为12小时", new BigDecimal("12.00"), result.getLeft());
        assertEquals("法定工作时长应为12小时", new BigDecimal("12.00"), result.getRight());
    }

    @Test
    public void test_calculateWorkingHours_精确分钟计算() {
        // 准备测试数据：9:30-17:45工作，12:15-13:30休息
        HrAttendanceClassItemConfigDTO hrItem = createHrItem("09:30:00", "17:45:00", "12:15:00", "13:30:00", false);

        // 执行测试
        Pair<BigDecimal, BigDecimal> result = ClassTimeUtil.calculateWorkingHours(hrItem);

        // 验证结果：出勤时长8.25小时（8小时15分钟），法定工作时长7小时（减去1小时15分钟休息）
        assertEquals("出勤时长应为8.25小时", new BigDecimal("8.25"), result.getLeft());
        assertEquals("法定工作时长应为7.00小时", new BigDecimal("7.00"), result.getRight());
    }

    @Test
    public void test_calculateWorkingHours_全天候工作_有多次休息() {
        // 准备测试数据：0:00-23:59工作，12:00-13:00休息
        HrAttendanceClassItemConfigDTO hrItem = createHrItem("00:00:00", "23:59:00", "12:00:00", "13:00:00", false);

        // 执行测试
        Pair<BigDecimal, BigDecimal> result = ClassTimeUtil.calculateWorkingHours(hrItem);

        // 验证结果：出勤时长23.98小时（23小时59分钟），法定工作时长22.98小时（减去1小时休息）
        assertEquals("出勤时长应为23.98小时", new BigDecimal("23.98"), result.getLeft());
        assertEquals("法定工作时长应为22.98小时", new BigDecimal("22.98"), result.getRight());
    }

    /**
     * 创建HR班次时间配置DTO的辅助方法
     */
    private HrAttendanceClassItemConfigDTO createHrItem(String punchInTime, String punchOutTime,
                                                       String restStartTime, String restEndTime,
                                                       boolean isAcrossDay) {
        HrAttendanceClassItemConfigDTO hrItem = new HrAttendanceClassItemConfigDTO();

        if (punchInTime != null) {
            hrItem.setPunchInTime(DateHelper.appendDefaultDateToTime(punchInTime));
        }
        if (punchOutTime != null) {
            hrItem.setPunchOutTime(DateHelper.appendDefaultDateToTime(punchOutTime));
        }
        if (restStartTime != null) {
            hrItem.setRestStartTime(DateHelper.appendDefaultDateToTime(restStartTime));
        }
        if (restEndTime != null) {
            hrItem.setRestEndTime(DateHelper.appendDefaultDateToTime(restEndTime));
        }

        hrItem.setIsAcross(isAcrossDay ? 1 : 0);

        return hrItem;
    }
}
