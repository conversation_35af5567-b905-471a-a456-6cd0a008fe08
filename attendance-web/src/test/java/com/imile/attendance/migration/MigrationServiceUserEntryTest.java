package com.imile.attendance.migration;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.infrastructure.repository.employee.dto.UserEntryInfoDTO;
import com.imile.attendance.query.PageDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.*;

/**
 * MigrationService用户入职查询功能测试
 * 
 * <AUTHOR> chen
 * @date 2025/7/9
 */
@Slf4j
public class MigrationServiceUserEntryTest extends BaseTest {

    @Resource
    private MigrationService migrationService;

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 测试根据国家和入职确认时间查找用户 - 正常情况
     */
    @Test
    public void testFindUsersByCountryAndConfirmDate_Normal() throws ParseException {
        log.info("=== 测试根据国家和入职确认时间查找用户 - 正常情况 ===");
        
        String locationCountry = "CHN";
        Date confirmDate = dateFormat.parse("2024-01-01");
        
        List<UserEntryInfoDTO> result = migrationService.findUsersByCountryAndConfirmDate(locationCountry, confirmDate);
        
        assertNotNull("查询结果不应为null", result);
        log.info("查询结果数量: {}", result.size());
        
        // 验证结果数据
        if (!CollectionUtils.isEmpty(result)) {
            UserEntryInfoDTO firstUser = result.get(0);
            assertNotNull("用户ID不应为null", firstUser.getUserId());
            assertNotNull("用户编码不应为null", firstUser.getUserCode());
            assertNotNull("用户姓名不应为null", firstUser.getUserName());
            assertNotNull("确认日期不应为null", firstUser.getConfirmDate());
            assertEquals("国家代码应匹配", locationCountry, firstUser.getLocationCountry());
            
            log.info("第一个用户信息: userId={}, userCode={}, userName={}, confirmDate={}, locationCountry={}", 
                    firstUser.getUserId(), firstUser.getUserCode(), firstUser.getUserName(), 
                    firstUser.getConfirmDate(), firstUser.getLocationCountry());
        }
    }


    /**
     * 测试参数验证 - 国家代码为空
     */
    @Test(expected = RuntimeException.class)
    public void testFindUsersByCountryAndConfirmDate_EmptyCountry() throws ParseException {
        log.info("=== 测试参数验证 - 国家代码为空 ===");
        
        String locationCountry = "";
        Date confirmDate = dateFormat.parse("2024-01-01");
        
        migrationService.findUsersByCountryAndConfirmDate(locationCountry, confirmDate);
    }

    /**
     * 测试参数验证 - 确认日期为空
     */
    @Test(expected = RuntimeException.class)
    public void testFindUsersByCountryAndConfirmDate_NullDate() {
        log.info("=== 测试参数验证 - 确认日期为空 ===");
        
        String locationCountry = "CHN";
        Date confirmDate = null;
        
        migrationService.findUsersByCountryAndConfirmDate(locationCountry, confirmDate);
    }


    /**
     * 测试查询无结果的情况
     */
    @Test
    public void testFindUsersByCountryAndConfirmDate_NoResult() throws ParseException {
        log.info("=== 测试查询无结果的情况 ===");
        
        String locationCountry = "UNKNOWN_COUNTRY";
        Date confirmDate = dateFormat.parse("2030-01-01"); // 未来日期
        
        List<UserEntryInfoDTO> result = migrationService.findUsersByCountryAndConfirmDate(locationCountry, confirmDate);
        
        assertNotNull("查询结果不应为null", result);
        assertTrue("应该返回空列表", result.isEmpty());
        log.info("无结果查询测试通过");
    }

    /**
     * 测试不同国家代码的查询
     */
    @Test
    public void testFindUsersByCountryAndConfirmDate_DifferentCountries() throws ParseException {
        log.info("=== 测试不同国家代码的查询 ===");
        
        Date confirmDate = dateFormat.parse("2024-01-01");
        String[] countries = {"CHN", "USA", "GBR", "IND"};
        
        for (String country : countries) {
            List<UserEntryInfoDTO> result = migrationService.findUsersByCountryAndConfirmDate(country, confirmDate);
            assertNotNull("查询结果不应为null: " + country, result);
            log.info("国家 {} 的查询结果数量: {}", country, result.size());
        }
    }
}
