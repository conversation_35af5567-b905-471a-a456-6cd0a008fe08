package com.imile.attendance.notic;

import com.alibaba.fastjson.JSONObject;
import com.imile.attendance.base.BaseTest;
import com.imile.attendance.infrastructure.notice.WeChatAppApiService;
import com.imile.attendance.infrastructure.notice.WechatUserApiService;
import com.imile.attendance.infrastructure.notice.dto.UserWxInfoDTO;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/29 
 * @Description
 */
public class WechatApiServiceTest extends BaseTest {

    @Resource
    private WechatUserApiService wechatUserApiService;
    @Resource
    private WeChatAppApiService weChatAppApiService;

    @Test
    public void testBatchGetUserWxIds() {
        List<UserWxInfoDTO> userWxInfoDTOS =
                wechatUserApiService.batchGetUserWxIds(Arrays.asList("21032679","21032539","21032215"));
        System.out.println(userWxInfoDTOS);
    }

    @Test
    public void testSendAppMessage(){
        String templateName = "no_class_and_punch_rule_remind_template_cn";
        HashMap<String, Object> templateData = new HashMap<>();
        templateData.put("userName","张三");
        templateData.put("userCode","emp123");
        templateData.put("punchClassConfigUrl","https://dev-attendance-miniapp.52imile.cn/attendance-web/#/AttendaceManage/ShiftSetting/FixedShiftSetting");
        templateData.put("punchConfigUrl","https://dev-attendance-miniapp.52imile.cn/attendance-web/#/AttendaceManage/OfficeAttendance/PunchRule");

        Object response = weChatAppApiService.sendAppMessage(
                Collections.singletonList("2101390"),
                true,
                templateName,
                new JSONObject(templateData)
        );
        System.out.println(response);
    }
}
