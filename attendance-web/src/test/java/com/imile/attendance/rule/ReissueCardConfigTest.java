package com.imile.attendance.rule;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.infrastructure.idwork.IdWorkUtils;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.dao.migrate.ReissueCardConfigRangeMigrateDao;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.ReissueCardConfigRangeMigrateDO;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.idwork.IdWorkerUtil;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> chen
 * @Date 2025/5/12 
 * @Description
 */
public class ReissueCardConfigTest extends BaseTest {

    @Resource
    private ReissueCardConfigManage reissueCardConfigManage;
    @Resource
    private ReissueCardConfigDao reissueCardConfigDao;
    @Resource
    private ReissueCardConfigRangeDao reissueCardConfigRangeDao;
    @Resource
    private ReissueCardConfigRangeMigrateDao reissueCardConfigRangeMigrateDao;


    @Test
    public void testgetConfigMapByUserIdList(){
        Map<Long, ReissueCardConfigDO> configMapByUserIdList =
                reissueCardConfigManage.getConfigMapByUserIdList(Arrays.asList(1197567969644118016L, 1103011271906697217L,1214685537454006272L));
        System.out.println(configMapByUserIdList);
    }

    @Test
    public void testListOnJobNoDriverUsersExcludeConfigured(){
        System.out.println("=============single country==================");

        RuleRangeUserQuery singleCountryQuery = RuleRangeUserQuery.builder()
                .country("ITA")
                .build();
        List<UserInfoDO> userInfoDOS = reissueCardConfigRangeDao.listOnJobNoDriverUsersExcludeConfigured(singleCountryQuery);
        Optional.ofNullable(userInfoDOS)
                .ifPresent(i->System.out.println(i.size()));

    }

    @Test
    public void testMigrateSave(){
        List<ReissueCardConfigRangeMigrateDO> list = Stream.of(1, 2, 3, 4, 5, 6, 7, 8, 9, 10).map(i -> {
            ReissueCardConfigRangeMigrateDO reissueCardConfigRangeMigrateDO = new ReissueCardConfigRangeMigrateDO();
            reissueCardConfigRangeMigrateDO.setId(IdWorkerUtil.getId());
            BaseDOUtil.fillDOInsertByUsrOrSystem(reissueCardConfigRangeMigrateDO);
            return reissueCardConfigRangeMigrateDO;
        }).collect(Collectors.toList());
        reissueCardConfigRangeMigrateDao.saveBatch(list);
    }
}
