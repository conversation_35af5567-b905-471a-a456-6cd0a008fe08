package com.imile.attendance.rule;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.rule.dto.UserRangeHistoryDTO;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> chen
 * @Date 2025/4/11 
 * @Description
 */
public class UserRangeHistoryTest extends BaseTest {

    @Resource
    private UserRangeHistoryService userRangeHistoryService;

    @Test
    public void testlistUserRangeHistory(){
        List<UserRangeHistoryDTO> userRangeHistoryDTOS =
                userRangeHistoryService.listUserRangeHistory(1103011271906697217L);
        Optional.ofNullable(userRangeHistoryDTOS).ifPresent(System.out::println);
    }
}
