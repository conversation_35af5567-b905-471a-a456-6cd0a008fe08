package com.imile.attendance.shift;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.imile.attendance.base.BaseTest;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.UserResourceService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.vo.PermissionCountryDeptVO;
import com.imile.attendance.infrastructure.repository.shift.dto.UserShiftConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.infrastructure.repository.shift.query.UserShiftConfigQuery;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.common.page.PaginationResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR> chen
 * @Date 2025/5/18
 * @Description UserShiftService测试类
 */
public class UserShiftServiceTest {

    @InjectMocks
    private UserShiftService userShiftService;

    @Mock
    private UserResourceService userResourceService;

    @Mock
    private AttendanceDeptService deptService;

    @Mock
    private UserShiftConfigManage userShiftConfigManage;

    @Mock
    private PunchClassConfigQueryService punchClassConfigQueryService;

    @Mock
    private CountryService countryService;

    private Method buildUserShiftConfigQueryMethod;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        // 使用反射获取私有方法
        buildUserShiftConfigQueryMethod = UserShiftService.class.getDeclaredMethod("buildUserShiftConfigQuery", UserShiftConfigQuery.class);
        buildUserShiftConfigQueryMethod.setAccessible(true);
    }

    /**
     * 测试正常情况下的查询条件构建
     */
    @Test
    public void testBuildUserShiftConfigQueryWithValidParameters() throws Exception {
        // 准备测试数据
        UserShiftConfigQuery query = new UserShiftConfigQuery();
        query.setStartTime(new Date());
        query.setEndTime(new Date());
        query.setCountry("CHN");
        query.setDeptIds(Arrays.asList(1L, 2L));

        // 模拟权限服务返回
        PermissionCountryDeptVO permissionDeptVO = new PermissionCountryDeptVO()
                .setIsSysAdmin(false)
                .setHasDeptPermission(true)
                .setHasCountryPermission(true)
                .setHasOrDeptAndCountryPermission(true)
                .setHasAndDeptAndCountryPermission(true)
                .setDeptIdList(Arrays.asList(1L, 2L))
                .setCountryList(Arrays.asList("CHN"));
        when(userResourceService.getPermissionCountryDeptVO(any(), any())).thenReturn(permissionDeptVO);

        // 模拟部门服务返回
        List<AttendanceDept> deptList = new ArrayList<>();
        AttendanceDept dept = new AttendanceDept();
        dept.setCountry("CHN");
        deptList.add(dept);
        when(deptService.listByDeptIds(anyList())).thenReturn(deptList);

        // 执行测试方法
        PaginationResult<UserShiftConfigDTO> result =
                (PaginationResult<UserShiftConfigDTO>) buildUserShiftConfigQueryMethod.invoke(userShiftService, query);

        // 验证结果
        Assert.assertNull("正常情况下应返回null以继续执行查询", result);
        Assert.assertEquals("CHN", query.getCountry());
//        Assert.assertTrue(query.getHasDeptPermission());
//        Assert.assertTrue(query.getHasCountryPermission());
//        Assert.assertTrue(query.getHasOrDeptAndCountryPermission());
//        Assert.assertTrue(query.getHasAndDeptAndCountryPermission());
        Assert.assertNotNull(query.getNormalCountryList());
    }

    /**
     * 测试时间参数为空时的异常处理
     */
    @Test
    public void testBuildUserShiftConfigQueryWithNullTimeParameters() throws Exception {
        // 准备测试数据 - 开始时间为空
        UserShiftConfigQuery query1 = new UserShiftConfigQuery();
        query1.setEndTime(new Date());

        try {
            buildUserShiftConfigQueryMethod.invoke(userShiftService, query1);
            Assert.fail("应该抛出异常，因为开始时间为空");
        } catch (Exception e) {
            Assert.assertTrue(e.getCause() instanceof RuntimeException);
        }

        // 准备测试数据 - 结束时间为空
        UserShiftConfigQuery query2 = new UserShiftConfigQuery();
        query2.setStartTime(new Date());

        try {
            buildUserShiftConfigQueryMethod.invoke(userShiftService, query2);
            Assert.fail("应该抛出异常，因为结束时间为空");
        } catch (Exception e) {
            Assert.assertTrue(e.getCause() instanceof RuntimeException);
        }
    }

    /**
     * 测试无权限情况下的返回值
     */
    @Test
    public void testBuildUserShiftConfigQueryWithNoPermission() throws Exception {
        // 准备测试数据
        UserShiftConfigQuery query = new UserShiftConfigQuery();
        query.setStartTime(new Date());
        query.setEndTime(new Date());
        query.setDeptIds(Arrays.asList(1L, 2L));

        // 模拟权限服务返回 - 无部门权限
        PermissionCountryDeptVO permissionDeptVO = new PermissionCountryDeptVO()
                .setIsSysAdmin(false)
                .setHasDeptPermission(false)
                .setHasCountryPermission(true)
                .setHasOrDeptAndCountryPermission(false)
                .setHasAndDeptAndCountryPermission(false)
                .setDeptIdList(new ArrayList<>())
                .setCountryList(Arrays.asList("CHN"));
        when(userResourceService.getPermissionCountryDeptVO(any(), any())).thenReturn(permissionDeptVO);

        // 执行测试方法
        PaginationResult<UserShiftConfigDTO> result = (PaginationResult<UserShiftConfigDTO>) buildUserShiftConfigQueryMethod.invoke(userShiftService, query);

        // 验证结果
        Assert.assertNotNull("无权限情况下应返回空结果", result);
        Assert.assertTrue("结果列表应为空", result.getResults().isEmpty());
    }

    /**
     * 测试特殊国家和普通国家的用工类型处理
     */
    @Test
    public void testBuildUserShiftConfigQueryWithSpecialCountry() throws Exception {
        // 准备模拟的国家列表
        List<CountryDTO> mockCountries = new ArrayList<>();
        // 添加特殊国家
        for (String specialCountry : CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT) {
            CountryDTO countryDTO = new CountryDTO();
            countryDTO.setCountryShort(specialCountry);
            mockCountries.add(countryDTO);
        }
        // 添加普通国家
        CountryDTO chnCountry = new CountryDTO();
        chnCountry.setCountryShort("CHN");
        mockCountries.add(chnCountry);

        // 模拟国家服务返回
        when(countryService.listAllCountry()).thenReturn(mockCountries);

        // 准备测试数据 - 特殊国家
        UserShiftConfigQuery query = new UserShiftConfigQuery();
        query.setStartTime(new Date());
        query.setEndTime(new Date());
        query.setCountry(CountryCodeEnum.KSA.getCode()); // 沙特是特殊国家

        // 模拟权限服务返回
        PermissionCountryDeptVO permissionDeptVO = new PermissionCountryDeptVO()
                .setIsSysAdmin(true)
                .setHasDeptPermission(true)
                .setHasCountryPermission(true)
                .setHasOrDeptAndCountryPermission(true)
                .setHasAndDeptAndCountryPermission(true)
                .setDeptIdList(new ArrayList<>())
                .setCountryList(Arrays.asList(CountryCodeEnum.KSA.getCode()));
        when(userResourceService.getPermissionCountryDeptVO(any(), any())).thenReturn(permissionDeptVO);

        // 执行测试方法
        buildUserShiftConfigQueryMethod.invoke(userShiftService, query);

        // 验证结果
        Assert.assertTrue("应设置为需要查询特殊国家", query.getIsNeedQuerySpecialCountry());
        Assert.assertTrue("特殊国家列表应包含KSA", query.getSpecialCountryList().contains(CountryCodeEnum.KSA.getCode()));
        Assert.assertTrue("特殊国家用工类型应包含劳务派遣",
                query.getSpecialEmployeeTypeList().contains(EmploymentTypeEnum.OS_FIXED_SALARY.getCode()));

        // 准备测试数据 - 普通国家
        query = new UserShiftConfigQuery();
        query.setStartTime(new Date());
        query.setEndTime(new Date());
        query.setCountry("CHN"); // 中国是普通国家

        // 模拟权限服务返回
        permissionDeptVO = new PermissionCountryDeptVO()
                .setIsSysAdmin(true)
                .setHasDeptPermission(true)
                .setHasCountryPermission(true)
                .setHasOrDeptAndCountryPermission(true)
                .setHasAndDeptAndCountryPermission(true)
                .setDeptIdList(new ArrayList<>())
                .setCountryList(Arrays.asList("CHN"));
        when(userResourceService.getPermissionCountryDeptVO(any(), any())).thenReturn(permissionDeptVO);

        // 执行测试方法
        buildUserShiftConfigQueryMethod.invoke(userShiftService, query);

        // 验证结果
        Assert.assertFalse("普通国家不需要查询特殊国家", query.getIsNeedQuerySpecialCountry());
        Assert.assertTrue("普通国家列表应包含CHN", query.getNormalCountryList().contains("CHN"));
        Assert.assertFalse("普通国家用工类型不应包含劳务派遣",
                query.getNormalEmployeeTypeList().contains(EmploymentTypeEnum.OS_FIXED_SALARY.getCode()));
    }

    /**
     * 测试排班状态和班次条件的处理
     */
    @Test
    public void testBuildUserShiftConfigQueryWithShiftStatusAndClassConditions() throws Exception {
        // 准备测试数据
        UserShiftConfigQuery query = new UserShiftConfigQuery();
        query.setStartTime(new Date());
        query.setEndTime(new Date());
        query.setShiftStatus("SHIFTED"); // 已排班
        query.setClassIdList(Arrays.asList(1L, 2L)); // 班次ID列表
        query.setClassNature("FIXED_CLASS"); // 固定班次

        // 模拟权限服务返回
        PermissionCountryDeptVO permissionDeptVO = new PermissionCountryDeptVO()
                .setIsSysAdmin(true)
                .setHasDeptPermission(true)
                .setHasCountryPermission(true)
                .setHasOrDeptAndCountryPermission(true)
                .setHasAndDeptAndCountryPermission(true)
                .setDeptIdList(new ArrayList<>())
                .setCountryList(Arrays.asList("CHN"));
        when(userResourceService.getPermissionCountryDeptVO(any(), any())).thenReturn(permissionDeptVO);

        // 模拟排班状态查询返回
        Map<Long, List<UserShiftConfigDO>> userShiftMap = new HashMap<>();
        userShiftMap.put(1L, new ArrayList<>());
        userShiftMap.put(2L, new ArrayList<>());
        when(userShiftConfigManage.queryForUserShiftPage(anyLong(), anyLong(), anyString())).thenReturn(userShiftMap);

        // 模拟班次条件查询返回
        Set<Long> classUserIds = new HashSet<>();
        classUserIds.add(1L);
        classUserIds.add(3L);
        when(punchClassConfigQueryService.selectAllUserIds(anyString(), anyList())).thenReturn(classUserIds);

        // 执行测试方法
        buildUserShiftConfigQueryMethod.invoke(userShiftService, query);

        // 验证结果
        Assert.assertNotNull("条件用户ID列表不应为空", query.getConditionUserIds());
        Assert.assertEquals("条件用户ID列表应只包含ID为1的用户（排班状态和班次条件的交集）", 1, query.getConditionUserIds().size());
        Assert.assertTrue("条件用户ID列表应包含ID为1的用户", query.getConditionUserIds().contains(1L));
    }
}
