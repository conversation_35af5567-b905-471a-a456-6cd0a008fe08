package com.imile.attendance.test;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

/**
 * 演示 convertDateByTimeZonePlus 方法的时间戳调整机制
 */
public class TimeZoneConversionDemo {
    
    public static void main(String[] args) {
        // 模拟原始方法
        Date originalDate = new Date(); // 当前系统时间
        
        System.out.println("=== 时区转换演示 ===");
        System.out.println("系统默认时区: " + TimeZone.getDefault().getID());
        System.out.println();
        
        // 原始时间信息
        System.out.println("1. 原始Date对象:");
        System.out.println("   时间戳: " + originalDate.getTime());
        System.out.println("   系统时区显示: " + formatDate(originalDate, TimeZone.getDefault()));
        System.out.println("   迪拜时区显示: " + formatDate(originalDate, TimeZone.getTimeZone("GMT+4")));
        System.out.println("   UTC显示: " + formatDate(originalDate, TimeZone.getTimeZone("UTC")));
        System.out.println();
        
        // 转换为迪拜时间
        Date convertedDate = convertDateByTimeZonePlus("4", originalDate);
        
        System.out.println("2. 转换后Date对象:");
        System.out.println("   时间戳: " + convertedDate.getTime());
        System.out.println("   系统时区显示: " + formatDate(convertedDate, TimeZone.getDefault()));
        System.out.println("   迪拜时区显示: " + formatDate(convertedDate, TimeZone.getTimeZone("GMT+4")));
        System.out.println("   UTC显示: " + formatDate(convertedDate, TimeZone.getTimeZone("UTC")));
        System.out.println();
        
        // 关键对比
        String originalInDubai = formatDate(originalDate, TimeZone.getTimeZone("GMT+4"));
        String convertedInSystem = formatDate(convertedDate, TimeZone.getDefault());
        
        System.out.println("3. 关键对比:");
        System.out.println("   原始Date在迪拜时区显示: " + originalInDubai);
        System.out.println("   转换Date在系统时区显示: " + convertedInSystem);
        System.out.println("   时分秒是否相同: " + extractTimeOnly(originalInDubai).equals(extractTimeOnly(convertedInSystem)));
        
        // 时间戳差异
        long timestampDiff = convertedDate.getTime() - originalDate.getTime();
        System.out.println("   时间戳差异: " + timestampDiff + " 毫秒 (" + (timestampDiff/3600000) + " 小时)");
    }
    
    /**
     * 模拟 convertDateByTimeZonePlus 方法
     */
    public static Date convertDateByTimeZonePlus(String timeZone, Date date) {
        if (timeZone == null || timeZone.isEmpty()) {
            timeZone = "8";
        }
        Date convertDate = null;
        if (date != null) {
            long systemOffset = Calendar.getInstance().getTimeZone().getOffset(System.currentTimeMillis());
            long timeZoneOffset = Calendar.getInstance(TimeZone.getTimeZone("GMT+" + timeZone)).getTimeZone().getOffset(System.currentTimeMillis());
            if (Integer.parseInt(timeZone) < 0) {
                timeZoneOffset = Calendar.getInstance(TimeZone.getTimeZone("GMT" + timeZone)).getTimeZone().getOffset(System.currentTimeMillis());
            }
            convertDate = new Date(date.getTime() + (timeZoneOffset - systemOffset));
        }
        return convertDate;
    }
    
    /**
     * 格式化日期到指定时区
     */
    private static String formatDate(Date date, TimeZone timeZone) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(timeZone);
        return sdf.format(date);
    }
    
    /**
     * 提取时分秒部分
     */
    private static String extractTimeOnly(String dateTimeStr) {
        return dateTimeStr.substring(11); // 提取 "HH:mm:ss" 部分
    }
}
