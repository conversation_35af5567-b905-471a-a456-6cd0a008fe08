# Excel表头导出使用指南

## 概述

本文档介绍了如何在项目中使用Excel表头导出功能。该功能允许开发人员定义和导出Excel表头，支持中英文切换，便于国际化应用。

## 核心组件

Excel表头导出功能主要由以下几个核心组件组成：

1. **ExcelHeaderBaseService接口** - 定义表头的基本行为
2. **ExcelHeaderUtil工具类** - 提供表头转换工具方法
3. **ExcelTitleExportDTO** - 表头导出数据传输对象
4. **表头枚举类** - 实现ExcelHeaderBaseService接口的枚举类，定义具体的表头

## 使用流程

### 1. 定义表头枚举类

首先，需要创建一个枚举类来定义Excel表头。该枚举类需要实现`ExcelHeaderBaseService`接口。

```java
@Getter
@AllArgsConstructor
public enum MyExportHeaderEnum implements ExcelHeaderBaseService {

    EMPLOYEE_ID("Employee ID", "员工ID"),
    EMPLOYEE_NAME("Employee Name", "员工姓名"),
    DEPARTMENT("Department", "部门"),
    // 添加更多表头...
    ;

    private final String englishTitle;
    private final String chineseTitle;

    @Override
    public String getEnglishTitle() {
        return englishTitle;
    }

    @Override
    public String getChineseTitle() {
        return chineseTitle;
    }
}
```

### 2. 在Service层中使用表头

在Service层中，使用`ExcelHeaderUtil`工具类将枚举表头转换为导出DTO列表：

```java
/**
 * 导出excel的表头
 */
public List<ExcelTitleExportDTO> titleExport() {
    // 获取表头，根据当前语言环境决定是否使用中文
    List<ExcelTitleExportDTO> result = 
        ExcelHeaderUtil.convertToExportDTOs(MyExportHeaderEnum.class, RequestInfoHolder.isChinese());
    
    return result;
}
```

### 3. 在Controller层中创建导出接口

在Controller层中，创建一个导出接口，使用`@ExportParamFill`注解处理导出参数：

```java
/**
 * 导出数据
 */
@PostMapping("/export")
@ExportParamFill
public Result<PaginationResult<MyExportDTO>> export(MyExportQuery query) {
    PaginationResult<MyExportDTO> result = myService.export(query);
    return Result.ok(result);
}
```

或者使用HttpServletRequest手动设置导出参数：

```java
/**
 * 导出数据
 */
@PostMapping("/export")
public Result<PaginationResult<MyExportDTO>> export(HttpServletRequest request, MyExportQuery query) {
    setExcelCallBackParam(request, query);
    PaginationResult<MyExportDTO> result = myService.export(query);
    return Result.ok(result);
}
```

### 4. 获取表头数据的接口

为了支持前端获取表头信息，通常需要提供一个单独的接口：

```java
/**
 * 获取导出表头
 */
@PostMapping("/title/export")
public Result<List<ExcelTitleExportDTO>> titleExport(MyExportQuery query) {
    List<ExcelTitleExportDTO> titles = myService.titleExport(query);
    return Result.ok(titles);
}
```

## 实际案例

以下是一个完整的实际案例，展示了如何定义和使用Excel表头：

### 1. 定义表头枚举

```java
@Getter
@AllArgsConstructor
public enum ShiftExportHeaderEnum implements ExcelHeaderBaseService {

    EMPLOYEE_ID("Employee ID", "账号"),
    EMPLOYEE_NAME("Employee Name", "姓名"),
    COUNTRY("Country", "国家"),
    DEPARTMENT_NAME("Department Name", "部门"),
    DESIGNATION("Designation", "岗位"),
    EMPLOYEE_TYPE("Employee Type", "用工类型"),
    PUNCH_CLASS_TYPE("Punch Class Type", "班次类型"),
    MATCHED_PUNCH_CLASS("Number of matched Punch Class", "班次适配数"),
    CALENDAR("Calendar", "日历");

    private final String englishTitle;
    private final String chineseTitle;

    @Override
    public String getEnglishTitle() {
        return englishTitle;
    }

    @Override
    public String getChineseTitle() {
        return chineseTitle;
    }
}
```

### 2. 在Service中实现表头导出

```java
/**
 * 排班导出excel的表头
 */
public List<ExcelTitleExportDTO> titleExport(UserShiftConfigQuery query) {
    Date startTime = query.getStartTime();
    Date endTime = query.getEndTime();
    if (null == startTime || null == endTime) {
        throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_VALID_ERROR,
                "startTime or endTime is null");
    }
    // 获取固定表头
    List<ExcelTitleExportDTO> result =
            ExcelHeaderUtil.convertToExportDTOs(ShiftExportHeaderEnum.class, RequestInfoHolder.isChinese());
    
    // 可以在这里添加动态表头，例如日期列
    
    return result;
}
```

### 3. 在Controller中提供接口

```java
/**
 * 获取排班导出表头
 */
@PostMapping("/title/export")
public Result<List<ExcelTitleExportDTO>> titleExport(@RequestBody UserShiftConfigQuery query) {
    List<ExcelTitleExportDTO> titles = userShiftService.titleExport(query);
    return Result.ok(titles);
}

/**
 * 导出排班数据
 */
@PostMapping("/export")
@ExportParamFill
public Result<PaginationResult<UserShiftExportDTO>> export(UserShiftConfigQuery query) {
    PaginationResult<UserShiftExportDTO> result = userShiftService.export(query);
    return Result.ok(result);
}
```

## 注意事项

1. 表头枚举类必须实现`ExcelHeaderBaseService`接口
2. 导出接口可以使用`@ExportParamFill`注解或手动设置导出参数
3. 表头支持中英文切换，通过`RequestInfoHolder.isChinese()`判断当前语言环境
4. 可以在表头列表中添加动态表头，例如日期列

## 总结

Excel表头导出功能提供了一种统一的方式来定义和导出Excel表头，支持中英文切换，便于国际化应用。通过定义表头枚举类，使用`ExcelHeaderUtil`工具类，可以轻松实现Excel表头的导出。
