# 移动打卡接口位置
http://127.0.0.1:80/mobile/punch/punchIn


### 移动打卡接口调用示例
```shell
curl --location --request POST 'http://127.0.0.1:80/mobile/punch/punchIn' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer xxxxxxx' \
--data-raw '{
"ivStr": "3LuCOWbXVa9OqjTON5tsjw==",
"content": "i+wopfRAYHnYLFUMMZOAOtrRnNZbHvi037POgZaALmZFWBgpWh2YuXRlOCpY7SNMMqZXvGEsrcynD5o1OpvvQNHfrbSPDrV3l+952wcZn8y4bM3Rky0nklRFKCa2G/VkB+O2mNcG1vAnXAOur3TK88q3ARlcOtf/gTKSJkqzV6q9ph+qr/0sexwOR+VmA3zRmViZxciLP6nhSnFigtOg+wV03RvluvbeSSRjZCg4jONSFP5Gbd+M5A4br5ynEI8QJ408A1+MIASVvmmv4fTDRmV2oeKSI/SILMa43cqYI5fWdZ7mFO5nugIgWSagWxZaTo1IGJOMHa1BjuM7/wPAQnC0Egl5oqvAbGOLYgWbmS1Kgrd5QPu8mex6mx/l6oJv25QQFmLuile7QkScESHpp8xUNgLM+E91/ApKj7wMpQFw5zZJEKCbns8v0Z9hIzKDafx2GryDIxvgixaVNapNVG1K4QgaGRXidLvufIQP5TNXRVoTjI9Dxvykn5nXVmmcdX3jE+Z6UygFJo6j56BSEWVwdOcRde4lDqUzI3oIKlnRv8h8ESPCP3fDFUZbyeqW"
}'
```

### 入参
```json
{
"ivStr": "3LuCOWbXVa9OqjTON5tsjw==",
"content": "i+wopfRAYHnYLFUMMZOAOtrRnNZbHvi037POgZaALmZFWBgpWh2YuXRlOCpY7SNMMqZXvGEsrcynD5o1OpvvQNHfrbSPDrV3l+952wcZn8y4bM3Rky0nklRFKCa2G/VkB+O2mNcG1vAnXAOur3TK88q3ARlcOtf/gTKSJkqzV6q9ph+qr/0sexwOR+VmA3zRmViZxciLP6nhSnFigtOg+wV03RvluvbeSSRjZCg4jONSFP5Gbd+M5A4br5ynEI8QJ408A1+MIASVvmmv4fTDRmV2oeKSI/SILMa43cqYI5fWdZ7mFO5nugIgWSagWxZaTo1IGJOMHa1BjuM7/wPAQnC0Egl5oqvAbGOLYgWbmS1Kgrd5QPu8mex6mx/l6oJv25QQFmLuile7QkScESHpp8xUNgLM+E91/ApKj7wMpQFw5zZJEKCbns8v0Z9hIzKDafx2GryDIxvgixaVNapNVG1K4QgaGRXidLvufIQP5TNXRVoTjI9Dxvykn5nXVmmcdX3jE+Z6UygFJo6j56BSEWVwdOcRde4lDqUzI3oIKlnRv8h8ESPCP3fDFUZbyeqW"
}
```

### 实际入参
```json
{
"configId": null,
"classId": null,
"classItemId": null,
"mobileConfigId": null,
"wifiConfigId": null,
"wifiConfigName": null,
"wifiConfigCity": null,
"longitude": 119.99296569824219,
"latitude": 30.2767391204834,
"gpsConfigId": 1915709370982576129,
"gpsConfigName": "浙富西溪堂2",
"gpsConfigCity": "杭州市",
"mobileUnicode": "add001c81f9ac8ba",
"mobileModel": "iPhone",
"mobileBranch": "Apple",
"mobileVersion": "iOS 18.1.1",
"dayId": 20250526,
"userId": 1232435335464660993,
"dateTime": "Mon May 26 09:28:58 CST 2025",
"currentTimeStamp": 1748222939044,
"ip": "240e:471:800:dba:809:2367:f0e9:b928"
}
```


### 具体可参考线上日志
```shell
appName :"hrms" and "attendance/mobile/punch/punchIn | param"
```


### 加密工具类 AesUtil
```java
public class AesUtil {

    public static void main(String[] args) {
        String ivStr = genBase64IV();
        String content = "your content here";
        String encrypted = encrypt(content, SECRET_KEY_STRING, ivStr);
        System.out.println("Encrypted: " + encrypted);
        String decrypted = decrypt(encrypted, SECRET_KEY_STRING, ivStr);
        System.out.println("Decrypted: " + decrypted);
    }
}
```
